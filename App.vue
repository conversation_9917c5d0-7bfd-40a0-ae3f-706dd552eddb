<script>
import { useCommonStore } from "@/store/common.js"
import { useUserStore } from "@/store/user.js"
export default {
  onLoad() {},
  onLaunch: function (options) {
    console.log("App Launch", options)
    const commonStore = useCommonStore()
    const userStore = useUserStore()

    commonStore.fetchConfig()
    const token = uni.getStorageSync("token") || ""
    userStore.setToken(token)
    if (token) {
      userStore.getUserInfo()
      userStore.getRemindCount()
    } else {
      userStore.logout()
      if (options.path === "pages/index/index") {
        uni.reLaunch({
          url: "/pages/login/login",
        })
      }
    }

    const systemInfo = uni.getSystemInfoSync()
    commonStore.setNavHeight(systemInfo.statusBarHeight + 44)
  },
  onShow: function () {
    console.log("App Show")
  },
  onHide: function () {
    console.log("App Hide")
  },
}
</script>

<style lang="scss">
@import "uview-plus/index.scss";

input,
view {
  box-sizing: border-box;
}

page {
  background-color: #f7f8fa;
  font-family: PingFang SC, PingFang SC;
}

.u-textarea {
  background-color: #f7f8fa !important;
}

.u-search__content__input--placeholder,
.input-placeholder,
.placeholder {
  font-size: 32rpx;
  color: #9ca3b5;
}

button {
  padding: unset;
  margin: unset;
}
button::after {
  border: unset;
}

.dialog {
  width: 630rpx;
  padding: 38rpx;
  background: linear-gradient(179deg, #fff8f8 0%, #ffffff 100%);
  border-radius: 20rpx 20rpx 20rpx 20rpx;
  border: 2rpx solid #ffffff;
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  &.green {
    background: linear-gradient(179deg, #f3ffef 0%, #ffffff 100%);
  }

  &-title {
    margin: 20rpx auto;
    margin-bottom: 92rpx;
    margin-top: 80rpx;
    font-weight: bold;
    font-size: 38rpx;
    color: #06121e;
    line-height: 40rpx;
    text-align: center;
  }
  &-content {
    font-weight: 400;
    font-size: 32rpx;
    color: #9ca3b5;
    text-align: center;
    font-style: normal;
    text-transform: none;
    margin-top: 27rpx;
    margin-bottom: 67rpx;
  }
  .dialog-btn-group {
    display: flex;
    justify-content: space-around;
    gap: 28rpx;
  }
  &-btn {
    flex: 1;
    height: 100rpx;
    background: #f7f8fa;
    border-radius: 55rpx 55rpx 55rpx 55rpx;
    color: #000;
    line-height: 100rpx;
    text-align: center;
  }
  .red {
    background: #ea2b2b;
    color: #fff;
  }
  .green {
    background: #577f49;
    color: #fff;
  }
}

.face-pop {
  width: 630rpx;
  padding: 49rpx 53rpx;
  background: linear-gradient(150deg, #edffe6 0%, #ffffff 100%);
  border-radius: 20rpx 20rpx 20rpx 20rpx;
  border: 2rpx solid #ffffff;
  .img {
    width: 58rpx;
    height: 58rpx;
    margin: 18rpx auto 50rpx;

    image {
      width: 58rpx;
      height: 58rpx;
    }
  }
  .title {
    font-weight: bold;
    font-size: 38rpx;
    color: #262626;
    text-align: center;
    margin-bottom: 31rpx;
  }
  .text {
    font-weight: 400;
    font-size: 32rpx;
    color: #9ca3b5;
    text-align: center;
    font-style: normal;
    text-transform: none;
    margin-bottom: 51rpx;
  }
  .btn {
    width: 524rpx;
    height: 100rpx;
    background: #577f49;
    border-radius: 55rpx 55rpx 55rpx 55rpx;
    color: #fff;
    line-height: 100rpx;
    text-align: center;
  }
}

.u-dropdown {
  .u-cell__title-text {
    text-align: center;
  }
}

.u-loadmore {
  padding-top: 50rpx;
  padding-bottom: 50rpx;

  .u-loadmore__content {
    text {
      color: #bdc4ce !important;
    }
  }
}

view[hidden] {
  display: none !important;
}

.u-table2 {
  width: 100%;
  padding: 10rpx;
  background: #ffffff;
  box-shadow: 0rpx 6rpx 13rpx 0rpx rgba(0, 0, 0, 0.04);
  border-radius: 16rpx 16rpx 16rpx 16rpx;

  .u-table-header {
    background: unset !important;
    color: #bdc4ce;
  }

  .u-table-body {
    .u-table-row {
      &:last-child {
        border-bottom: unset;
      }
    }
  }

  .u-table-cell {
    padding: 22rpx !important;
    font-size: 30rpx !important;
  }

  .u-table-row-child {
    .u-table-cell {
      color: #39455b;
      font-size: 24rpx !important;
    }
  }
}
</style>
