<script setup>
import { ref, computed, toRef } from "vue"
import { questionTypeMap } from "@/utils/constants"

const props = defineProps({
  questionDetail: {
    type: Object,
    default: {},
  },
  number: {
    type: Number,
    default: 0,
  },
})

const questionDetail = toRef(props, "questionDetail")

const indexToLetter = (index) => {
  return String.fromCharCode(65 + index)
}

const correctAnswerIndex = computed(() => {
  return questionDetail.value.correct.split(",").map((i) => parseInt(i) - 1)
})

const selectedAnswerIndex = computed(() => {
  return questionDetail.value.selections.split(",").map((i) => parseInt(i) - 1)
})
</script>

<template>
  <view class="card" v-if="questionDetail.id">
    <view class="title">
      {{ number }}. ({{ questionTypeMap[questionDetail.question_type].text }})
      {{ questionDetail.question_title }}
    </view>

    <view
      class="answerList"
      v-if="['single', 'judge'].includes(questionDetail.question_type)"
    >
      <view
        class="answer"
        :class="{
          correct: item.is_correct,
          incorrect: !item.is_correct,
          checked: selectedAnswerIndex.includes(index),
        }"
        v-for="(item, index) in questionDetail.options"
        :key="index"
      >
        {{ indexToLetter(index) }}. {{ item.title }}
      </view>
    </view>

    <view class="answerList" v-if="questionDetail.question_type == 'multiple'">
      <view class="uni-list">
        <checkbox-group>
          <label
            class="uni-list-cell uni-list-cell-pd"
            v-for="(item, index) in questionDetail.options"
            :key="index"
          >
            <view
              class="answer"
              :class="{
                correct: item.is_correct,
                incorrect: !item.is_correct,
                checked: selectedAnswerIndex.includes(index),
              }"
              style="display: flex"
            >
              <checkbox
                :value="item.id"
                :checked="selectedAnswerIndex.includes(index)"
              />
              <view> {{ indexToLetter(index) }}. {{ item.title }}</view>
            </view>
          </label>
        </checkbox-group>
      </view>
    </view>

    <template>
      <view class="instant-result yes" v-if="questionDetail.is_correct">
        <image class="icon" src="/static/image/correct.svg" />
        回答正确
      </view>
      <view class="instant-result no" v-else>
        <image class="icon" src="/static/image/incorrect.svg" />
        回答错误
      </view>
    </template>

    <view class="desc">
      <view class="answer">
        正确答案：
        <text style="color: #577f49">
          {{ correctAnswerIndex.map((i) => indexToLetter(i)).join(", ") }}
        </text>
      </view>
      <view class="info">
        <view class="label">要点提示：</view>
        <view class="content">{{ questionDetail.question_analysis }}</view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.card {
  width: 702rpx;
  margin-bottom: 20rpx;
  background: #ffffff;
  box-shadow: 0rpx 6rpx 13rpx 1rpx rgba(0, 0, 0, 0.04);
  border-radius: 20rpx 20rpx 20rpx 20rpx;
  padding: 30rpx;
}

.desc {
  margin-top: 36rpx;
  font-weight: bold;
  font-size: 32rpx;
  color: #333333;

  .info {
    margin-top: 26rpx;
    display: flex;
    align-items: flex-start;
    font-size: 32rpx;
    color: #333333;

    .label {
      flex-shrink: 0;
      font-weight: bold;
    }

    .content {
      flex: 1;
      font-weight: 500;
      margin-left: 20rpx;
    }
  }
}

.title {
  width: 640rpx;
  margin: 0 auto;
  font-weight: bold;
  font-size: 32rpx;
  color: #333333;
  margin-bottom: 46rpx;
  text-align: left;
  font-style: normal;
  text-transform: none;
}

.answerList {
  .answer {
    width: 640rpx;
    padding: 20rpx;
    background: #ffffff;
    border-radius: 20rpx 20rpx 20rpx 20rpx;
    margin-bottom: 20rpx;

    checkbox {
      margin-right: 12rpx;
      transform: scale(0.75);
    }

    &.checked {
      background: #fafff8;
      border: 2rpx solid #577f49;

      &.correct {
        background: #fafff8;
        box-shadow: 6rpx 6rpx 0rpx #577f49;
        border-color: #577f49;
      }

      &.incorrect {
        background: #fff8f8;
        box-shadow: 6rpx 6rpx 0rpx #ea2b2b;
        border-color: #ea2b2b;
      }
    }
  }
}

.instant-result {
  display: flex;
  align-items: center;

  .icon {
    width: 70rpx;
    height: 70rpx;
    margin-right: 20rpx;
  }

  &.yes {
    color: #577f49;
  }

  &.no {
    color: #d81e06;
  }
}
</style>
