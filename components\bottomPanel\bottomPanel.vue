<script setup>
import { ref, computed, onMounted } from "vue"

const props = defineProps({
  isGenerating: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(["confirm", "inputBlur", "inputFocus"])

const isInputting = ref(false)
const onInputBlur = () => {
  isInputting.value = false
  emit("inputBlur")
}
const onInputFocus = () => {
  isInputting.value = true
  emit("inputFocus")
}

const keyboardHeight = ref("0px")
const floatBottom = computed(() => {
  return isInputting.value ? keyboardHeight.value : "0px"
})

const confirm = (text) => {
  if (text === "") {
    return
  }
  emit("confirm", text)
  isInputting.value = false
}

onMounted(() => {
  uni.onKeyboardHeightChange((res) => {
    if (res.height > 200) {
      keyboardHeight.value = res.height + "px"
      isInputting.value = true
    } else {
      isInputting.value = false
    }
  })
})
</script>

<template>
  <view class="bottom-panel" @click.top>
    <view class="content" :class="{ 'keyboard-show': isInputting }">
      <view class="input-box">
        <view class="input-area">
          <inputComp
            :isGenerating="isGenerating"
            :keyboardHeight="keyboardHeight"
            @confirm="confirm"
            @inputFocus="onInputFocus"
            @inputBlur="onInputBlur"
          />
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.bottom-panel {
  width: 100%;
  position: fixed;
  z-index: 9;
  bottom: v-bind(floatBottom);
  left: 0;
  background: rgba(248, 250, 254, 0.8);
  box-shadow: 0rpx 32rpx 48rpx 0rpx rgba(160, 163, 189, 0.16);
  border-radius: 64rpx 64rpx 0rpx 0rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.5);
}

.content {
  padding: 24rpx;
  padding-bottom: calc(env(safe-area-inset-bottom) + 24rpx);
  display: flex;
  flex-direction: column;
  gap: 24rpx;

  &.keyboard-show {
    padding-bottom: 24rpx;
  }

  .input-box {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .input-area {
      width: 100%;
    }
  }
}
</style>
