<script setup>
import { ref, watch, computed } from "vue"
import { chapterTemplateMap } from "@/utils/constants"

const props = defineProps(["list"])
const emit = defineEmits(["clickItem"])

const lastEndedChapterIndex = ref(-1)

watch(
  () => props.list,
  (newVal) => {
    if (newVal.length) {
      lastEndedChapterIndex.value = newVal.length - 1
      for (let i = newVal.length - 1; i >= -1; i--) {
        lastEndedChapterIndex.value = i
        if (newVal[i]?.process?.status === 1) {
          break
        }
      }
    }
  },
  {
    immediate: true,
    deep: true,
  }
)

const onClickItem = (item, index) => {
  if (index - 1 > lastEndedChapterIndex.value) {
    return
  }
  emit("clickItem", item)
}
</script>

<template>
  <view class="course-catalog">
    <view
      v-for="(item, index) in list"
      :key="item.id"
      class="course-catalog-item"
      :class="{ disabled: index - 1 > lastEndedChapterIndex }"
      @click="onClickItem(item, index)"
    >
      <view class="left">
        <image
          class="icon"
          :src="
            item.process?.status === 1
              ? chapterTemplateMap[item.template].activeIcon
              : chapterTemplateMap[item.template].inactiveIcon
          "
        />
        <view class="line" v-if="index < list.length - 1"></view>
      </view>
      <view class="right">
        <view class="title"> {{ item.name }} </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.course-catalog {
  .course-catalog-item {
    display: flex;
    gap: 28rpx;
    padding: 10rpx 0;

    &.disabled {
      opacity: 0.5;
    }

    .left {
      flex-shrink: 0;
      position: relative;
      margin-top: 20rpx;

      .icon {
        border-radius: 50%;
        width: 68rpx;
        height: 68rpx;
        position: relative;
        z-index: 1;
      }

      .line {
        display: block;
        width: 0rpx;
        height: calc(100% - 34rpx);
        border-left: 1rpx dashed #577f49;
        position: absolute;
        top: 72rpx;
        left: 50%;
        z-index: 1;
      }
    }

    .right {
      flex: 1;
      display: flex;
      align-items: center;
      min-height: 100rpx;
      padding: 28rpx 32rpx;
      background: #ffffff;
      border-radius: 20rpx 20rpx 20rpx 20rpx;

      .title {
        font-weight: 500;
        font-size: 32rpx;
        color: #333;
      }
    }
  }
}
</style>
