<script setup>
import { ref, provide, watch, computed } from "vue"

const props = defineProps({
  // 当前选中的值
  modelValue: {
    type: [String, Number, Boolean],
    default: "",
  },

  // 兼容 uview-plus 的 placement 属性
  placement: {
    type: String,
    default: "",
    validator: (value) => ["", "row", "column"].includes(value),
  },
  // 是否禁用整个组
  disabled: {
    type: Boolean,
    default: false,
  },
  // 自定义样式
  customStyle: {
    type: Object,
    default: () => ({}),
  },
  // 选项间距
  gap: {
    type: [String, Number],
    default: "20rpx",
  },
})

const emit = defineEmits(["update:modelValue", "change"])

// 内部选中值
const selectedValue = ref(props.modelValue)

// 监听外部值变化
watch(
  () => props.modelValue,
  (newVal) => {
    selectedValue.value = newVal
  },
  { immediate: true }
)

// 监听内部值变化，向外发射
watch(selectedValue, (newVal) => {
  emit("update:modelValue", newVal)
  emit("change", newVal)
})

// 计算样式
const groupStyle = computed(() => {
  const baseStyle = {
    display: "flex",
    flexDirection: props.placement === "row" ? "row" : "column",
    gap: typeof props.gap === "number" ? `${props.gap}rpx` : props.gap,
    ...props.customStyle,
  }
  return baseStyle
})

// 提供给子组件的方法和数据
provide("radioGroup", {
  selectedValue,
  disabled: computed(() => props.disabled),
  updateValue: (value) => {
    selectedValue.value = value
  },
})
</script>

<template>
  <view class="custom-radio-group" :style="groupStyle">
    <slot></slot>
  </view>
</template>

<style lang="scss" scoped>
.custom-radio-group {
  width: 100%;
}
</style>
