<script setup>
import { inject, computed } from "vue"

const props = defineProps({
  // 选项的值
  value: {
    type: [String, Number, Boolean],
    required: true,
  },
  // 显示的标签文本
  label: {
    type: String,
    default: "",
  },
  // 是否禁用此选项
  disabled: {
    type: Boolean,
    default: false,
  },
  // 自定义样式
  customStyle: {
    type: Object,
    default: () => ({}),
  },

  // small: 26rpx, medium: 32rpx, large: 38rpx
  size: {
    type: String,
    default: "medium",
  },

  // 文字颜色
  textColor: {
    type: String,
    default: "#262626",
  },
  // 禁用时的文字颜色
  disabledTextColor: {
    type: String,
    default: "#C8C9CC",
  },
})

const emit = defineEmits(["change"])

// 从父组件注入数据
const radioGroup = inject("radioGroup", null)

// 计算是否选中
const isChecked = computed(() => {
  if (!radioGroup) return false
  const checked = radioGroup.selectedValue.value === props.value
  return checked
})

// 计算是否禁用
const isDisabled = computed(() => {
  return props.disabled || (radioGroup && radioGroup.disabled.value)
})

// 点击处理
const handleClick = () => {
  if (isDisabled.value) return

  if (radioGroup) {
    radioGroup.updateValue(props.value)
  }

  emit("change", props.value)
}

// 计算样式
const itemStyle = computed(() => {
  return {
    opacity: isDisabled.value ? 0.5 : 1,
    cursor: isDisabled.value ? "not-allowed" : "pointer",
    ...props.customStyle,
  }
})

const textStyle = computed(() => {
  return {
    fontSize:
      props.size === "small"
        ? "26rpx"
        : props.size === "medium"
        ? "32rpx"
        : "38rpx",
    color: isDisabled.value ? props.disabledTextColor : props.textColor,
  }
})

const iconStyle = computed(() => {
  return {
    width:
      props.size === "small"
        ? "24rpx"
        : props.size === "medium"
        ? "32rpx"
        : "38rpx",
    height:
      props.size === "small"
        ? "24rpx"
        : props.size === "medium"
        ? "32rpx"
        : "38rpx",
  }
})
</script>

<template>
  <view class="custom-radio-item" :style="itemStyle" @click="handleClick">
    <view class="radio-icon" :style="iconStyle">
      <image
        :src="
          isChecked
            ? '/static/image/radio-checked.svg'
            : '/static/image/radio-unchecked.svg'
        "
        mode="aspectFit"
        class="icon-image"
      />
    </view>
    <view class="radio-label" :style="textStyle">
      {{ label || value }}
    </view>
  </view>
</template>

<style lang="scss" scoped>
.custom-radio-item {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 12rpx 0;
  transition: opacity 0.3s ease;

  .radio-icon {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;

    .icon-image {
      width: 100%;
      height: 100%;
    }
  }

  .radio-label {
    flex: 1;
    line-height: 1.4;
    word-break: break-all;
  }
}
</style>
