<script setup>
import { ref, toRef } from "vue"

const props = defineProps({
  title: {
    type: String,
    default: "选择接收单位",
  },
  nodes: {
    type: Array,
    default: () => [],
  },
  selectedData: {
    type: Array,
    default: () => [],
  },
})
const emit = defineEmits(["change", "update:selectedData"])
const treeRef = ref(null)

const selectedData = toRef(props, "selectedData")
const isAllSelected = ref(false)

const toggleSelectAll = () => {
  isAllSelected.value = !isAllSelected.value
  treeRef.value.toggleSelectAll(isAllSelected.value)
}

const onChange = (e) => {
  selectedData.value = e
  emit("update:selectedData", e)
  emit("change", e)
}

const confirmDepartment = () => {
  treeRef.value.confirm()
}
</script>

<template>
  <view class="tree-container">
    <view class="title-bar">
      <view class="title"> {{ title }} </view>
    </view>

    <view class="wrapper">
      <treePicker
        ref="treeRef"
        valueKey="code"
        :data="nodes"
        :selectedData="selectedData"
        themeColor="#577F49"
        @change="onChange"
      />
    </view>

    <view class="footer">
      <label class="">
        <checkbox :value="isAllSelected" @click="toggleSelectAll" />
        <text>全选</text>
      </label>
      <button type="primary" @click="confirmDepartment">确定选择</button>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.close {
  width: 38rpx;
  height: 38rpx;
}
.tree-container {
  padding: 20rpx;

  display: flex;
  flex-direction: column;
  justify-content: space-between;

  gap: 20rpx;

  .title-bar {
    padding: 10rpx 20rpx;
    text-align: center;
  }

  .wrapper {
    height: 60vh;
  }

  .footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10rpx 20rpx;

    button {
      width: 407rpx;
      height: 100rpx;
      line-height: 100rpx;
      background: #577f49;
      border-radius: 50rpx 50rpx 50rpx 50rpx;
      font-size: 34rpx;
      color: #ffffff;
    }
  }
}
</style>
