<script setup>
const props = defineProps({
  theme: {
    type: String,
    default: "1",
  },
})
</script>

<template>
  <view class="empty">
    <image
      class="empty-img"
      :src="
        theme === '2' ? '/static/image/empty2.svg' : '/static/image/empty.png'
      "
    ></image>
    <view class="empty-text">
      <slot> 暂无数据 </slot>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding-top: 200rpx;

  .empty-img {
    width: 226rpx;
    height: 226rpx;
  }

  .empty-text {
    font-size: 32rpx;
    color: #9ca3b5;
    margin-top: 24rpx;
  }
}
</style>
