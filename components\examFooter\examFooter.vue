<script setup>
import { ref, computed } from "vue"

const props = defineProps({
  paperType: {
    type: String,
    default: "exam", // practice, assessment, exam
  },
  status: {
    type: String,
    default: "unanswered", // unanswered, answered, submitted
  },
  feedbackType: {
    type: String,
    default: "final", // final, instant
  },
  allDown: {
    type: Boolean,
    default: false,
  },
  result: {
    type: String,
    default: "", // correct, incorrect
  },
  currentNo: {
    type: Number,
    default: 1,
  },
})
const emit = defineEmits(["next", "prev", "answer", "finish"])

const right = computed(() => props.result === "correct")

// 上一题
const prev = () => {
  console.log("上一题")
  emit("prev")
}

// 下一题
const next = () => {
  console.log("下一题")
  if (props.feedbackType === "final") {
    uni.showToast({
      title: "请先提交答案",
      icon: "none",
    })
    if (props.status !== "answered") return
  } else {
    uni.showToast({
      title: "请先选择答案",
      icon: "none",
    })
    if (props.status !== "submitted") return
  }
  emit("next")
}

// 提交答案
const answerQuestion = () => {
  console.log("提交答案")
  emit("answer")
}

// 结束
const finish = () => {
  if (props.feedbackType === "final") {
    if (props.status !== "answered") return
  } else {
    if (props.status !== "submitted") return
  }
  emit("finish")
}
</script>

<template>
  <view class="footer">
    <view class="btn">
      <view class="btn-left">
        <view
          class="yes"
          v-if="feedbackType == 'final' && currentNo > 1"
          @click="prev"
        >
          上一题
        </view>
        <template v-if="feedbackType == 'instant' && status == 'submitted'">
          <view class="instant-result yes" v-if="right">
            <image class="icon" src="/static/image/correct.svg" />
            回答正确
          </view>
          <view class="instant-result no" v-if="!right">
            <image class="icon" src="/static/image/incorrect.svg" />
            回答错误
          </view>
        </template>
      </view>
      <view class="btn-right">
        <template v-if="!allDown">
          <view
            v-if="feedbackType == 'instant' && status !== 'submitted'"
            class="btn-submit"
            :class="{
              nochecked: status == 'unanswered',
              checked: status == 'answered',
            }"
            @click="answerQuestion"
          >
            提交答案
          </view>

          <view
            v-if="feedbackType == 'final' || status == 'submitted'"
            class="btn-submit"
            @click="next"
          >
            下一题
          </view>
        </template>
        <template v-else>
          <view
            class="btn-submit"
            @click="finish"
            v-if="paperType == 'practice'"
          >
            结束练习
          </view>
          <view v-else class="btn-submit checked" @click="finish">
            提交答卷
          </view>
        </template>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.footer {
  position: fixed;
  width: 750rpx;
  padding: 40rpx 60rpx;
  padding-bottom: calc(env(safe-area-inset-bottom) / 2 + 40rpx);
  background: #ffffff;
  box-shadow: 0rpx 3rpx 60rpx 1rpx rgba(0, 0, 0, 0.16);
  border-radius: 22rpx 22rpx 0rpx 0rpx;
  bottom: 0rpx;
  left: 0rpx;

  .btn {
    display: flex;
    justify-content: space-between;

    .btn-left {
      height: 100rpx;
      line-height: 100rpx;
      width: 285rpx;
      font-weight: bold;
      font-size: 32rpx;
      text-align: center;

      .instant-result {
        display: flex;
        align-items: center;

        .icon {
          width: 70rpx;
          height: 70rpx;
          margin-right: 20rpx;
        }

        &.yes {
          color: #577f49;
        }

        &.no {
          color: #d81e06;
        }
      }
    }

    .btn-right {
      .btn-submit {
        width: 285rpx;
        height: 100rpx;
        line-height: 100rpx;
        text-align: center;
        border: 1rpx solid #e1e1e1;
        border-radius: 50rpx 50rpx 50rpx 50rpx;
        color: #21232c;
      }

      .checked {
        background: #577f49;
        color: #ffffff;
      }

      .nochecked {
        background: #bdc4ce;
        color: #ffffff;
      }
    }
  }
}
</style>
