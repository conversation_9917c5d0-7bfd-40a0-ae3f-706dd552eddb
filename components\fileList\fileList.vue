<script setup>
import { ref } from "vue"
import { fileExtensionMap } from "@/utils/constants.js"
import request from "@/utils/request.js"

const props = defineProps({
  fileList: {
    type: Array,
    default: () => [],
  },
})

const previewFile = async (file) => {
  if (!file.url) {
    file.url = file.full_path
  }

  if (!file.type) {
    file.type = fileExtensionMap[file.ext].type
  }

  if (file.type === "document") {
    uni.downloadFile({
      url: file.url,
      success: (res) => {
        const filePath = res.tempFilePath
        uni.openDocument({
          filePath,
          fileType: file.ext,
          success: () => {
            console.log("打开文档成功")
          },
          fail: (err) => {
            console.error("打开文档失败", err)
          },
        })
      },
      fail: (err) => {
        console.error("下载文件失败", err)
      },
    })
  } else if (["image", "video"].includes(file.type)) {
    console.log("previewMedia", file.type, file.url)
    uni.previewMedia({
      sources: [
        {
          type: file.type,
          url: file.url || file.full_path,
        },
      ],
    })
  } else {
    uni.showToast({
      title: "暂不支持预览该文件",
      icon: "none",
    })
  }
}
</script>

<template>
  <view class="file-list">
    <view
      v-for="(file, index) in fileList"
      :key="index"
      class="file-item"
      @click="previewFile(file)"
    >
      <view class="file-info">
        <image
          :src="
            fileExtensionMap[file.ext]?.icon || '/static/image/file-default.svg'
          "
          class="file-icon"
        />
        <view class="file-name">{{ file.filename }}</view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.file-list {
  display: flex;
  flex-direction: column;

  .file-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    max-width: 618rpx;
    height: 80rpx;
    background: #f7f8fa;
    border-radius: 20rpx;
    margin: 10rpx auto;
    padding: 14rpx;
    gap: 20rpx;
    overflow: hidden;

    .file-info {
      flex: 1;
      display: flex;
      align-items: center;
      overflow: hidden;

      .file-icon {
        flex-shrink: 0;
        width: 32rpx;
        height: 32rpx;
        margin-right: 10rpx;
      }
      .file-name {
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-weight: 500;
        font-size: 26rpx;
        color: #000000;
      }
    }
    .delete-icon {
      margin-right: 6rpx;
      flex-shrink: 0;
      width: 30rpx;
      height: 30rpx;
    }
  }
}
</style>
