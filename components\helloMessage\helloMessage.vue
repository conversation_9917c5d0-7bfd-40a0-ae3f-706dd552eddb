<script setup></script>

<template>
  <view class="hello-message" id="hello-message">
    <view class="content">
      <view class="title">
        <view>你好，我是你的智能助手~</view>
      </view>
      <view class="desc">
        我能帮助你解答一些问题，并且能够给 你推送征兵相关得政策及通知。
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.hello-message {
  position: relative;
  padding: 54rpx 0;

  .content {
    width: 620rpx;
    padding: 40rpx;
    background: linear-gradient(
      135deg,
      rgba(255, 255, 255, 0.9) 0%,
      rgba(255, 255, 255, 0.7) 100%
    );
    box-shadow: 8rpx 14rpx 48rpx 1rpx rgba(0, 0, 0, 0.02);
    border-radius: 0rpx 20rpx 20rpx 20rpx;

    .title {
      font-size: 40rpx;
      font-weight: bold;
      color: #253a57;
      margin-bottom: 20rpx;
    }

    .desc {
      font-size: 32rpx;
      color: #333333;
    }
  }
}
</style>
