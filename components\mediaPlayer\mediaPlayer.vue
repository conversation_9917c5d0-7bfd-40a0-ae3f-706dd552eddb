<template>
  <view class="media-player">
    <template v-if="type === 'voice'">
      <image v-if="playing" src="/static/image/audio-bg.gif" />
      <image v-else src="/static/image/audio-bg.jpg" />
      <playerUI
        :title="title"
        :playing="playing"
        :currentTime="currentTime"
        :duration="duration"
        :isFullscreen="false"
        type="voice"
        @togglePlay="togglePlay"
      />
    </template>
    <template v-if="type === 'video'">
      <video
        id="videoPlayer"
        :src="src"
        :controls="false"
        autoplay
        @play="onVideoPlay"
        @pause="onVideoPause"
        @ended="onVideoEnded"
        @timeupdate="onVideoTimeUpdate"
        @fullscreenchange="onVideoFullScreenChange"
        @loadedmetadata="onVideoLoadedmetadata"
        @error="onVideoError"
      >
        <playerUI
          :title="title"
          :playing="playing"
          :currentTime="currentTime"
          :duration="duration"
          :isFullScreen="isFullScreen"
          type="video"
          @togglePlay="togglePlay"
          @toggleFullScreen="toggleFullScreen"
        />
      </video>
    </template>
  </view>
</template>

<script>
import playerUI from "./playerUI.vue"

var audioContext
var videoContext

export default {
  components: {
    playerUI,
  },
  data() {
    return {
      currentTime: 0,
      duration: 0,
      playing: false,
      isFullScreen: false,

      audioTimer: null,
    }
  },
  props: {
    src: String,
    type: {
      type: String,
      default: "voice",
    },
    title: {
      type: String,
      default: "",
    },
    startTime: {
      type: Number,
      default: 0,
    },
  },
  mounted() {
    console.log("mounted==============================")
    this.init()
  },
  watch: {
    src: {
      handler(newSrc, oldSrc) {
        // 在 mediaSrc 变化时执行的操作
        console.log(`Media source changed from ${oldSrc} to ${newSrc}`)

        if (newSrc && newSrc !== oldSrc) {
          this.init()
        }
      },
    },
  },
  methods: {
    init() {
      try {
        audioContext.destroy()
      } catch (error) {
        console.error("audioContext.destroy", error)
      }

      this.currentTime = 0
      this.duration = 0
      this.playing = true

      if (this.type === "voice") {
        audioContext = uni.createInnerAudioContext({
          useWebAudioImplement: true,
        })
        audioContext.obeyMuteSwitch = false
        audioContext.autoplay = true
        audioContext.src = this.src

        this.onVoicePlay()
        this.onVoicePause()
        this.onVoiceEnded()
        this.onVoiceTimeUpdate()
        this.onVoiceCanplay()
        this.onVoiceError()
        this.onVoiceSeeked()

        this.startAudioTimer()
      } else if (this.type === "video") {
        videoContext = uni.createVideoContext("videoPlayer", this)
      }
    },

    togglePlay() {
      if (this.playing) {
        this.pause()
      } else {
        this.play()
      }
    },

    onVoiceCanplay() {
      audioContext.onCanplay((e) => {
        console.log("onVoiceCanplay", e)
        console.log(audioContext.paused)
      })
    },
    onVoiceError() {
      audioContext.onError((e) => {
        console.error("onVoiceError", e)

        this.init()
      })
    },
    onVoiceSeeked() {
      audioContext.onSeeked(() => {
        console.log("onVoiceSeeked", audioContext.paused)
      })
    },
    onVoicePlay() {
      audioContext.onPlay(() => {
        console.log("onVoicePlay")
        this.playing = true
        this.$emit("play", { currentTime: this.currentTime })
      })
    },
    onVoicePause() {
      audioContext.onPause(() => {
        console.log("onVoicePause")
        this.playing = false
        this.$emit("pause", { currentTime: this.currentTime })
      })
    },
    onVoiceEnded() {
      audioContext.onEnded(() => {
        console.log("onVoiceEnded")
        this.playing = false
        this.$emit("ended", { currentTime: this.duration })
      })
    },
    onVoiceTimeUpdate() {
      audioContext.onTimeUpdate(() => {
        console.log("onVoiceTimeUpdate", audioContext.currentTime)
        const currentTime = audioContext.currentTime
        if (
          currentTime &&
          isFinite(currentTime) &&
          currentTime > 0 &&
          currentTime > this.currentTime
        ) {
          this.currentTime = currentTime
        }
      })
    },

    onVideoLoadedmetadata(e) {
      console.log("onVideoLoadedmetadata", e)
      this.duration = e.detail.duration
      if (this.startTime && this.duration - this.startTime > 1) {
        this.seek(this.startTime)
      }
    },
    onVideoPlay() {
      console.log("onVideoPlay")
      this.playing = true
      this.$emit("play", { currentTime: this.currentTime })
    },
    onVideoPause() {
      console.log("onVideoPause")
      this.playing = false
      this.$emit("pause", { currentTime: this.currentTime })
    },
    onVideoEnded() {
      console.log("onVideoEnded")
      this.playing = false
      this.$emit("ended", { currentTime: this.duration })
      if (this.isFullScreen) {
        videoContext.exitFullScreen()
      }
    },
    onVideoTimeUpdate(e) {
      // console.log("onVideoTimeUpdate", e)
      this.playing = true
      this.currentTime = e.detail.currentTime
      this.duration = e.detail.duration
      this.$emit("timeupdate", { currentTime: e.detail.currentTime })
    },
    onVideoFullScreenChange(e) {
      console.log("onVideoFullScreenChange", e)
      this.isFullScreen = e.detail.fullScreen
    },
    onVideoError(e) {
      console.error("onVideoError", e)
      this.init()
    },
    // 关闭时调用
    stop() {
      // if (!this.playing) return
      this.playing = false
      this.currentTime = 0
      if (this.type === "voice") {
        audioContext.stop()
      } else if (this.type === "video") {
        videoContext.stop()
      }
    },
    // 重新播放
    replay() {
      this.currentTime = 0
      if (this.type === "voice") {
        audioContext.seek(0)
        audioContext.play()
      } else if (this.type === "video") {
        videoContext.seek(0)
        videoContext.play()
      }
    },
    // 点击播放
    play() {
      console.log("play")
      if (this.type === "voice") {
        // audioContext.pause()
        audioContext.play()
      } else if (this.type === "video") {
        videoContext.play()
      }
    },
    // 从指定时间播放
    seek(time = 0) {
      this.currentTime = time
      if (this.type === "voice") {
        audioContext.seek(time)
      } else if (this.type === "video") {
        videoContext.seek(time)
      }
    },
    // 点击暂停
    pause() {
      console.log("pause")
      if (this.type === "voice") {
        audioContext.pause()
      } else if (this.type === "video") {
        videoContext.pause()
      }
    },

    toggleFullScreen() {
      console.log("toggleFullScreen")
      if (this.isFullScreen) {
        videoContext.exitFullScreen()
      } else {
        videoContext.requestFullScreen()
      }
    },

    startAudioTimer() {
      this.audioTimer = setInterval(() => {
        let duration = audioContext.duration

        console.log("audioTimer", duration)

        if (duration && isFinite(duration) && duration > 0 && !this.duration) {
          this.duration = duration
          if (this.startTime && this.duration - this.startTime > 1) {
            this.seek(this.startTime)
            // audioContext.currentTime = this.startTime
          }
          clearInterval(this.audioTimer)
        }
      }, 200)
    },
  },
}
</script>

<style lang="scss" scoped>
.media-player {
  position: relative;
  width: 750rpx;
  height: 422rpx;
  background: #f5f5f5;
  overflow: hidden;
}

image,
video {
  width: 100%;
  height: 100%;
}
</style>
