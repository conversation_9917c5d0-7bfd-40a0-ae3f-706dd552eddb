<script setup>
import { ref, computed } from "vue"

const props = defineProps({
  type: {
    type: String,
    default: "voice",
  },
  title: {
    type: String,
    default: "",
  },
  playing: {
    type: Boolean,
    default: false,
  },
  duration: {
    type: Number,
    default: 0,
  },
  currentTime: {
    type: Number,
    default: 0,
  },
  isFullScreen: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(["togglePlay", "toggleFullScreen"])

const getTime = (time) => {
  var duration = parseInt(time)
  var minute = parseInt(duration / 60)
  var sec = (duration % 60) + ""
  var isM0 = ":"
  if (minute === 0) {
    minute = "00"
  } else if (minute < 10) {
    minute = "0" + minute
  }
  if (sec.length === 1) {
    sec = "0" + sec
  }
  return minute + isM0 + sec
}

const leftTime = computed(() => {
  const diff = props.duration - props.currentTime
  return getTime(diff)
})
</script>

<template>
  <view class="player-ui">
    <view class="title">
      <view
        class="title-text"
        :class="{
          r: title.length > (isFullScreen ? 10 : 50),
        }"
        :style="{
          marginTop: type === 'video' ? '10px' : '0',
        }"
      >
        {{ title }}
      </view>
    </view>

    <view class="controller">
      <image
        class="play-btn"
        @click="emit('togglePlay')"
        :src="playing ? '/static/image/pause.svg' : '/static/image/play.svg'"
      ></image>
      <view style="flex: 1">
        <slider
          class="audio-slider"
          activeColor="#577F49"
          backgroundColor="#EEEEEE"
          blockSize="10"
          :max="duration > 0 ? parseInt(duration) : 60"
          :value="currentTime"
          disabled
        ></slider>
      </view>
      <view class="left-time"> {{ leftTime }} </view>
      <view
        class="full-screen"
        v-if="type === 'video'"
        @click="emit('toggleFullScreen')"
      >
        <image src="/static/image/full-screen.svg" class="full-screen-btn" />
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
@keyframes loop {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-100%);
  }
}

.player-ui {
  width: 100%;
  height: 100%;
  position: absolute;
  bottom: 0;
  left: 0;
  z-index: 1;
}

.title {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 50px;
  background: linear-gradient(
    180deg,
    rgba(0, 0, 0, 0.6) 0%,
    rgba(0, 0, 0, 0) 100%
  );
  overflow: hidden;
  padding: 7px 14px 20px;
  box-sizing: border-box;

  font-size: 16px;
  color: #ffffff;
  font-weight: 500;
  white-space: nowrap;

  .title-text {
    // margin-top: 10px;
  }

  .r {
    animation: loop 10s linear infinite;
  }
}

.controller {
  z-index: 1;
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 48px;
  background: rgba(0, 0, 0, 0.3);
  padding: 0 14px;
  box-sizing: border-box;
  display: flex;
  align-items: center;

  .play-btn {
    width: 30px;
    height: 30px;
  }
  .left-time {
    font-size: 15px;
    color: #ffffff;
    font-weight: 500;
  }

  .full-screen {
    margin-left: 20px;
    width: 25px;
    height: 20px;

    .full-screen-btn {
      width: 100%;
      height: 100%;
    }
  }
}
</style>
