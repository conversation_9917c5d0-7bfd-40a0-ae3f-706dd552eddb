<script setup>
import markdownit from "markdown-it"
import { navTo } from "@/utils/utils"

const md = markdownit()

const props = defineProps({
  list: {
    type: Array,
    default: () => [],
  },
  mode: {
    type: String,
    default: "chat",
  },
})

const onArticleClick = (article) => {
  console.log(article)
  navTo("/pages/policy/detail", {
    articleId: article.id,
  })
}
</script>

<template>
  <view
    class="message"
    :class="message.role"
    :id="`message-${index}`"
    v-for="(message, index) in list"
    :key="message.id"
  >
    <view
      class="message-content"
      :class="message.role"
      v-if="message.role === 'user'"
    >
      {{ message.content }}
    </view>

    <template v-else>
      <view class="generating" v-if="message.status === 'generating'">
        <image
          class="generating"
          src="/static/image/generating.gif"
          mode="aspectFit"
        />
        <text>思考中...</text>
      </view>

      <view class="message-content" :class="message.role" v-else>
        <template
          v-if="['done'].includes(message.status) || mode === 'history'"
        >
          <mp-html
            :tag-style="{
              p: 'margin-bottom: 40rpx;',
              li: 'margin-bottom: 40rpx;',
            }"
            :content="md.render(message.content)"
          ></mp-html>

          <view class="article-list">
            <view
              class="article-item"
              v-for="(article, index) in message.articles"
              :key="index"
              @click="onArticleClick(article)"
            >
              <view class="article-title">{{ article.title }}</view>
              <image class="right-arrow" src="/static/image/right.png" />
            </view>
          </view>
        </template>
        <jp-typewriter
          v-else
          :text="message.content"
          :speed="100"
          :textStyle="{
            color: '#253A57',
            fontWeight: 'normal',
            fontSize: '34rpx',
          }"
        >
        </jp-typewriter>
      </view>
    </template>
  </view>
</template>

<style lang="scss" scoped>
.message {
  padding: 12rpx 0;
  display: flex;

  &.user {
    padding-left: 42rpx;
    justify-content: flex-end;
  }
}

.message-content {
  font-size: 32rpx;
  line-height: 50rpx;
  padding: 30rpx;
  text-align: justify;

  &.user {
    background: #577f49;
    border-radius: 20rpx 0rpx 20rpx 20rpx;
    color: #ffffff;
    width: fit-content;
    max-width: 620rpx;
  }

  &.assistant {
    width: 620rpx;
    background: linear-gradient(
      135deg,
      rgba(255, 255, 255, 0.9) 0%,
      rgba(255, 255, 255, 0.7) 100%
    );
    box-shadow: 0rpx 16rpx 64rpx 0rpx rgba(110, 113, 145, 0.12);
    padding: 30rpx;
    border-radius: 12rpx 48rpx 48rpx 48rpx;
    border: 2rpx solid rgba(255, 255, 255, 0.5);
    color: #253a57;
  }
}

.generating {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  font-weight: 500;
  color: #6e7191;

  image {
    width: 102rpx;
    height: 102rpx;
    margin-right: 24rpx;
  }
}

.article-list {
  margin-top: 26rpx;

  .article-item {
    display: flex;
    gap: 20rpx;
    align-items: center;
    padding: 15rpx 34rpx;
    background: #f7f8fa;
    border-radius: 55rpx 55rpx 55rpx 55rpx;
    color: #434a54;
    font-size: 30rpx;
    font-weight: 500;
    margin-bottom: 14rpx;

    .article-title {
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      padding-left: 10rpx;
    }

    .right-arrow {
      flex-shrink: 0;
      width: 8rpx;
      height: 16rpx;
    }
  }
}
</style>
