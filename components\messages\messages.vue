<script setup>
import {
  ref,
  computed,
  watch,
  onMounted,
  getCurrentInstance,
  nextTick,
} from "vue"
import contentList from "./contentList.vue"
import { rpx2px } from "@/utils/utils"

const props = defineProps({
  list: {
    type: Array,
    default: () => [],
  },
  isMute: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(["manual", "rate", "refresh", "speak"])

const onSpeak = (message) => {
  console.log("onSpeak", message)
  emit("speak", message)
}
const onRate = (message) => {
  console.log("onRate", message)
  emit("rate", message)
}
const onManual = (message) => {
  console.log("onManual", message)
  emit("manual")
}
const onRefresh = (message) => {
  console.log("onRefresh", message)
  emit("refresh")
}

const onScrollToUpper = () => {
  console.log("onScrollToUpper")
}
const refresherTriggered = ref(false)
const onPull = () => {
  console.log("onPull")
  refresherTriggered.value = true

  nextTick(() => {
    setTimeout(() => {
      refresherTriggered.value = false
    }, 50)
  })
}

let instance = null
let query
const windowHeight = ref(0)
const contentHeight = ref(0)
const scrollTop = ref(0)
const helloMessageHeight = ref(rpx2px(640))
const bottomPanelHeight = ref(rpx2px(388))
const placeholderHeight = computed(() => {
  return windowHeight.value - helloMessageHeight.value
})
const getWindowHeight = () => {
  query
    .select(".messages")
    .boundingClientRect((rect) => {
      windowHeight.value = rect.height
    })
    .exec()
}
const updateScrollTop = (mode) => {
  query
    .select(".list-content")
    .boundingClientRect()
    .exec((e) => {
      if (e[0]) {
        const lastEvent = e[e.length - 1]

        const currentContentHeight = contentHeight.value
        contentHeight.value = lastEvent.height
        if (mode === "upper") {
          const diff = lastEvent.height - currentContentHeight
          scrollTop.value = diff - rpx2px(100)
        } else {
          scrollTop.value =
            lastEvent.height - windowHeight.value + bottomPanelHeight.value
        }
      }
    })
}
const scrollIntoBottom = (offset = 0) => {
  query
    .select(".list-content")
    .boundingClientRect()
    .exec((e) => {
      if (e[0]) {
        const lastEvent = e[e.length - 1]
        contentHeight.value = lastEvent.height
        scrollTop.value = lastEvent.height - helloMessageHeight.value + offset
      }
    })
}
const onScroll = (e) => {
  // console.log("onScroll", e.detail.scrollTop)
  // scrollTop.value = e.detail.scrollTop
}

watch(
  () => props.list,
  (newVal) => {
    const lastMessage = newVal[newVal.length - 1]
    if (
      lastMessage &&
      (lastMessage.status === "generating" || lastMessage.role === "artificial")
    ) {
      nextTick(() => {
        updateScrollTop("lower")
      })
    } else {
      updateScrollTop("lower")
    }
  },
  { deep: true }
)

onMounted(() => {
  instance = getCurrentInstance()
  query = uni.createSelectorQuery().in(instance)

  getWindowHeight()
})

defineExpose({
  scrollIntoBottom,
  updateScrollTop,
})
</script>

<template>
  <scroll-view
    class="messages"
    scroll-y
    :scroll-top="scrollTop"
    refresher-enabled
    refresher-default-style="none"
    :refresher-triggered="refresherTriggered"
    :upper-threshold="rpx2px(100)"
    @scroll="onScroll"
    @scrolltoupper="onScrollToUpper"
    @refresherrefresh="onPull"
  >
    <view class="list-content">
      <helloMessage />

      <contentList
        :list="list"
        :isMute="isMute"
        @manual="onManual"
        @rate="onRate"
        @refresh="onRefresh"
        @speak="onSpeak"
      />
    </view>

    <view :style="{ height: `${placeholderHeight}px` }"></view>
  </scroll-view>
</template>

<style>
.messages {
  width: 100%;
  height: 100%;
}

.list-content {
  padding: 0 20rpx;
}
</style>
