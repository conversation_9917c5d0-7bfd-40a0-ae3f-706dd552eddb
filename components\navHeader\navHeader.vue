<script setup>
import { ref } from "vue"
import { useCommonStore } from "@/store/common.js"
const store = useCommonStore()
const height = store.navHeight
const props = defineProps({
  title: {
    type: String,
    default: "",
  },
  showLeft: {
    type: Boolean,
    default: true,
  },
  bg: {
    type: String,
    default: "",
  },
  imgHeight: {
    type: Number,
    default: 980,
  },
  color: {
    type: String,
    default: "#000",
  },
  clickLeft: {
    type: Function,
    default: null,
  },
})

const bgColor = ref("rgba(255, 0, 0, 0)")
</script>

<template>
  <view class="">
    <image
      :src="bg"
      mode=""
      class="imgCss"
      :style="{ height: bg ? imgHeight + 'rpx' : '0' }"
    ></image>
    <up-navbar
      :style="{ height: height + 'px', backgroundColor: bgColor }"
      :bgColor="bgColor"
      :autoBack="clickLeft ? false : true"
      titleWidth="365rpx"
      :title="title"
      :titleStyle="{
        color: color,
      }"
      @leftClick="clickLeft"
    >
      <template #left>
        <up-icon
          name="arrow-left"
          :color="color"
          size="19"
          v-if="showLeft"
        ></up-icon>
      </template>
      <!-- <template #center>
        <text :style="{ color: color }">{{ title }}</text>
      </template> -->
    </up-navbar>
  </view>
</template>

<style lang="scss" scoped>
.imgCss {
  width: 750rpx;
  position: absolute;
  top: 0;
  left: 0;
  z-index: -1;
}
</style>
