<script setup>
import { ref } from "vue"
import dayjs from "dayjs"

const props = defineProps({
  item: {
    type: Object,
    default: () => ({}),
  },
})

const emit = defineEmits(["openDetail"])
</script>

<template>
  <view class="box" @click="emit('openDetail', item)">
    <view class="title"> {{ item.title }} </view>
    <view class="footer">
      <view class="time">
        <image src="/static/image/time.png" mode=""></image>
        <text> {{ dayjs(item.created_at).format("YYYY-MM-DD") }} </text>
      </view>
      <view class="address">
        <image src="/static/image/user.png" mode=""></image>
        <text> {{item.district_name}}{{ item.department_name }} </text>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.box {
  width: 702rpx;
  min-height: 210rpx;
  background: #ffffff;
  box-shadow: 2rpx 11rpx 48rpx 1rpx rgba(0, 0, 0, 0.04);
  border-radius: 20rpx 20rpx 20rpx 20rpx;
  border: 1rpx solid #ffffff;
  margin: 0 auto;
  padding: 28rpx;
  margin-bottom: 20rpx;

  .title {
    font-weight: 500;
    font-size: 32rpx;
    color: #111111;
    height: 89rpx;
    line-height: 44rpx;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .footer {
    display: flex;
    margin-top: 24rpx;
    .time {
      display: flex;
      font-weight: 400;
      font-size: 26rpx;
      color: #bdc4ce;
      text-align: left;
      font-style: normal;
      text-transform: none;
      margin-right: 45rpx;
      gap: 13rpx;
      align-items: center;

      image {
        width: 25rpx;
        height: 25rpx;
      }
    }

    .address {
      display: flex;
      font-weight: 400;
      font-size: 26rpx;
      color: #bdc4ce;
      text-align: left;
      font-style: normal;
      text-transform: none;
      align-items: center;
      gap: 13rpx;

      image {
        width: 25rpx;
        height: 25rpx;
      }
    }
  }
}
</style>
