<script setup>
import { ref } from "vue"
const props = defineProps({
  item: {
    type: Object,
    default: () => ({}),
  },
})
</script>

<template>
  <view class="list-item">
    <view class="list-left">
      <image src="/static/image/document.svg" mode=""></image>
    </view>
    <view class="list-right">
      <view class="title"> {{ item.title }} </view>
      <view class="text">
        <text
          style="margin-right: 10rpx"
          v-for="category in item.categories"
          :key="category.id"
        >
          #{{ category.name }}
        </text>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.list-item {
  display: flex;
  padding: 26rpx;
  width: 702rpx;
  height: 213rpx;
  background: rgba(255, 255, 255, 0.94);
  box-shadow: 0rpx 6rpx 13rpx 1rpx rgba(0, 0, 0, 0.04);
  border-radius: 20rpx 20rpx 20rpx 20rpx;
  border: 2rpx solid #ffffff;
  margin-bottom: 20rpx;

  .list-left {
    flex-shrink: 0;
    width: 168rpx;
    height: 161rpx;
    margin-right: 38rpx;
    image {
      width: 100%;
      height: 100%;
    }
  }
  .list-right {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding-bottom: 9rpx;
    .title {
      font-weight: 500;
      font-size: 32rpx;
      color: #333333;
      text-align: left;
      font-style: normal;
      text-transform: none;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      line-height: 45rpx;
      height: 90rpx;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: normal;
    }
    .text {
      font-weight: 400;
      font-size: 24rpx;
      color: #bdc4ce;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }
  }
}
</style>
