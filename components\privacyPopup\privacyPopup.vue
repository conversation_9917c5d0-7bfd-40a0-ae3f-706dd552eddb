<script setup>
import { ref, onMounted, onUnmounted } from "vue"

const emit = defineEmits(["agree", "disagree"])

let closeOtherPagePopUpHooks =
  getApp().globalData.closeOtherPagePopUpHooks || new Set()

const closeOtherPagePopUp = (closePopUp) => {
  closeOtherPagePopUpHooks.forEach((hook) => {
    if (closePopUp !== hook) {
      hook()
    }
  })
}

const title = ref("用户隐私保护提示")
const desc1 = ref("感谢您使用本小程序，您使用本小程序前应当阅井同意")
const urlTitle = ref("《用户隐私保护指引》")
const desc2 = ref(
  "当您点击同意并开始时用产品服务时，即表示你已理解并同息该条款内容，该条款将对您产生法律约束力。如您拒绝，将无法进入小程序。"
)
const innerShow = ref(false)
const height = ref(0)

const handleDisagree = (e) => {
  emit("disagree")
  disPopUp()
  wx.exitMiniProgram()
}

const handleAgree = (e) => {
  emit("agree")
  disPopUp()
  if (closePopUp.value) {
    closeOtherPagePopUp(closePopUp.value)
  }
}

const popUp = () => {
  innerShow.value = true
}

const disPopUp = () => {
  innerShow.value = false
  closeOtherPagePopUpHooks.delete(closePopUp.value)
  getApp().globalData.closeOtherPagePopUpHooks = closeOtherPagePopUpHooks
}

const openPrivacyContract = () => {
  wx.openPrivacyContract({
    success: (res) => {
      console.log("打开隐私协议成功：", res)
    },
    fail: (err) => {
      console.log("打开隐私协议失败：", err)
    },
    complete: () => {},
  })
}

const closePopUp = ref()

onMounted(() => {
  let _closePopUp = () => {
    disPopUp()
  }
  if (wx.getPrivacySetting) {
    wx.getPrivacySetting({
      success: (res) => {
        console.log(
          "是否需要授权：",
          res.needAuthorization,
          "隐私协议的名称为：",
          res.privacyContractName
        )
        if (res.needAuthorization) {
          popUp()
          closePopUp.value = _closePopUp
          closeOtherPagePopUpHooks.add(_closePopUp)
          getApp().globalData.closeOtherPagePopUpHooks =
            closeOtherPagePopUpHooks
        } else {
          emit("agree")
        }
      },
      fail: () => {},
      complete: () => {},
    })
  } else {
    // 低版本基础库不支持 wx.getPrivacySetting 接口，隐私接口可以直接调用
    emit("agree")
  }
})
</script>

<template>
  <view class="overlay" v-if="innerShow">
    <view
      class="half-screen-dialog"
      :style="{ position: 'fixed', bottom: height + 'px' }"
    >
      <view class="half-screen-dialog__hd">
        <text class="half-screen-dialog__title">{{ title }}</text>
      </view>
      <view class="half-screen-dialog__bd">
        <view class="half-screen-dialog__tips">{{ desc1 }}</view>
        <view
          class="half-screen-dialog__tips"
          style="color: #497ee9"
          @tap="openPrivacyContract"
          >{{ urlTitle }}</view
        >
        <view class="half-screen-dialog__tips">{{ desc2 }}</view>
      </view>
      <view class="half-screen-dialog__ft">
        <view class="half-screen-dialog__btn-area">
          <button
            id="disagree-btn"
            type="default"
            class="btn"
            @tap="handleDisagree"
          >
            不同意并退出
          </button>
          <button
            id="agree-btn"
            type="default"
            open-type="agreePrivacyAuthorization"
            class="btn agree"
            @agreeprivacyauthorization="handleAgree"
          >
            同意并继续
          </button>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 99999;
}
.half-screen-dialog {
  width: 100%;
  position: absolute;
  left: 0;
  bottom: 0;
  border-radius: 20px 20px 0 0;
  overflow: hidden;

  &__hd {
    padding: 20px;
    text-align: center;
    background-color: #fff;
  }

  &__title {
    font-size: 18px;
    color: #000;
    font-weight: bold;
  }

  &__bd {
    padding: 20px 30px;
    background-color: #fff;
  }

  &__tips {
    font-size: 14px;
    color: #21232c;
    line-height: 1.5;
    margin-bottom: 10px;
  }

  &__ft {
    padding: 20px;
    background-color: #fff;
    padding-bottom: calc(env(safe-area-inset-bottom) / 2 + 20px);
  }

  &__btn-area {
    display: flex;
    justify-content: space-between;
  }

  .btn {
    width: 45%;
    height: 50px;
    line-height: 50px;
    border-radius: 50px;
    background-color: #f7f8fa;
    color: #21232c;
    font-size: 14px;
  }

  .btn.agree {
    background-color: #577f49;
    color: #fff;
  }
}
</style>
