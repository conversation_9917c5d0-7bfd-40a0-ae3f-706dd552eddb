<script setup>
import { ref, computed, watch, getCurrentInstance } from "vue"
import { questionTypeMap } from "@/utils/constants"

const props = defineProps({
  qaId: {
    type: Number,
    default: 0,
  },
  type: {
    // 单选题 single, 多选题 multiple, 判断题 judge
    type: String,
    default: "single",
  },
  title: {
    type: String,
    default: "",
  },
  options: {
    type: Array,
    default: () => [],
  },
  correctSerials: {
    type: String,
    default: "",
  },
  status: {
    type: String,
    default: "unanswered", // unanswered, answered, submitted
  },
  feedbackType: {
    type: String,
    default: "final", // final, instant
  },
  analysis: {
    type: String,
    default: "",
  },
  selections: {
    type: Array,
    default: () => [],
  },
})

const emit = defineEmits(["select"])

const correctAnswerIndex = computed(() => {
  console.log(props.correctSerials)

  return props.correctSerials.split(",").map((num) => parseInt(num) - 1)
})
const selectedAnswerIndex = ref([])

const checkboxChange = (e) => {
  if (props.feedbackType === "instant" && props.status === "submitted") {
    return
  }

  selectedAnswerIndex.value = e.detail.value.map((id) => {
    return props.options.findIndex((item) => item.id == id)
  })

  emit(
    "select",
    selectedAnswerIndex.value.map((i) => i + 1)
  )
}

const indexToLetter = (index) => {
  return String.fromCharCode(65 + index)
}

const selectSingleAnswer = (index) => {
  if (props.feedbackType === "instant" && props.status === "submitted") {
    return
  }

  selectedAnswerIndex.value = [index]
  emit("select", [index + 1])
}

watch(
  () => props.qaId,
  (newVal) => {
    selectedAnswerIndex.value = []
    // const { ctx } = getCurrentInstance()
    // ctx?.$forceUpdate()
    console.log('newVal===================================', newVal)
    if (props.selections.length) {
      selectedAnswerIndex.value = props.selections.map((i) => i - 1)
    }
  }
)

// watch(
//   () => props.selections,
//   (newVal) => {
//     console.log('newVal===================================', newVal)
//     selectedAnswerIndex.value = newVal.map((i) => i - 1)
//   }
// )
</script>

<template>
  <view class="card">
    <view class="title"> ({{ questionTypeMap[type].text }}) {{ title }} </view>

    <view class="answerList" v-if="['single', 'judge'].includes(type)">
      <view
        class="answer"
        :class="{
          correct:
            status == 'submitted' &&
            feedbackType == 'instant' &&
            item.is_correct,
          incorrect:
            status == 'submitted' &&
            feedbackType == 'instant' &&
            !item.is_correct,
          checked: selectedAnswerIndex.includes(index),
        }"
        v-for="(item, index) in options"
        :key="index"
        @click="selectSingleAnswer(index)"
      >
        {{ indexToLetter(index) }}. {{ item.title }}
      </view>
    </view>

    <view class="answerList" v-if="type == 'multiple'">
      <view class="uni-list">
        <checkbox-group @change="checkboxChange">
          <label
            class="uni-list-cell uni-list-cell-pd"
            v-for="(item, index) in options"
            :key="index"
          >
            <view
              class="answer"
              :class="{
                correct:
                  status == 'submitted' &&
                  feedbackType == 'instant' &&
                  item.is_correct,
                incorrect:
                  status == 'submitted' &&
                  feedbackType == 'instant' &&
                  !item.is_correct,
                checked: selectedAnswerIndex.includes(index),
              }"
              style="display: flex"
            >
              <checkbox
                :value="item.id"
                :checked="selectedAnswerIndex.includes(index)"
              />
              <view>{{ indexToLetter(index) }}. {{ item.title }}</view>
            </view>
          </label>
        </checkbox-group>
      </view>
    </view>
    <view
      class="desc"
      v-if="status == 'submitted' && feedbackType == 'instant'"
    >
      <view class="answer">
        正确答案：
        <text style="color: #577f49">
          {{ correctAnswerIndex.map((i) => indexToLetter(i)).join(", ") }}
        </text>
      </view>
      <view class="info">
        <view class="label">要点提示：</view>
        <view class="content">{{ analysis }}</view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.desc {
  margin-top: 74rpx;
  font-weight: bold;
  font-size: 32rpx;
  color: #333333;

  .info {
    margin-top: 26rpx;
    display: flex;
    align-items: flex-start;
    font-size: 32rpx;
    color: #333333;

    .label {
      flex-shrink: 0;
      font-weight: bold;
    }

    .content {
      flex: 1;
      font-weight: 500;
      margin-left: 20rpx;
    }
  }
}

.card {
  width: 640rpx;
  margin: 0 auto;
}

.title {
  width: 640rpx;
  margin: 0 auto;
  margin-top: 90rpx;

  font-weight: bold;
  font-size: 32rpx;
  color: #333333;
  margin-bottom: 46rpx;
  text-align: left;
  font-style: normal;
  text-transform: none;
}

.answerList {
  .answer {
    width: 640rpx;
    padding: 20rpx;
    background: #ffffff;
    border-radius: 20rpx 20rpx 20rpx 20rpx;
    margin-bottom: 20rpx;

    checkbox {
      margin-right: 12rpx;
      transform: scale(0.75);
    }

    &.checked {
      background: #fafff8;
      border: 2rpx solid #577f49;

      &.correct {
        background: #fafff8;
        box-shadow: 6rpx 6rpx 0rpx #577f49;
        border-color: #577f49;
      }

      &.incorrect {
        background: #fff8f8;
        box-shadow: 6rpx 6rpx 0rpx #ea2b2b;
        border-color: #ea2b2b;
      }
    }
  }
}
</style>
