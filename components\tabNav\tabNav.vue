<script setup>
import { ref, onMounted, computed } from "vue"
import { navTo } from "@/utils/utils"
import { useUserStore } from "@/store/user"

const props = defineProps(["index"])

const userStore = useUserStore()

const redDot = computed(() => userStore.remindCount > 0)

onMounted(() => {
  uni.hideTabBar()
})
</script>

<template>
  <view class="footer">
    <view class="nav" @click="navTo('/pages/index/index')">
      <image
        src="/static/tabbar/home-active.png"
        mode=""
        v-if="index == 0"
      ></image>
      <image src="/static/tabbar/home.png" mode="" v-else></image>
      <view :class="index == 0 ? 'active' : 'item'"> 主页 </view>
    </view>
    <view class="nav" @click="navTo('/pages/todoTask/todoTask')">
      <image
        src="/static/tabbar/daiban-active.png"
        mode=""
        v-if="index == 1"
      ></image>
      <image src="/static/tabbar/daiban.png" mode="" v-else></image>
      <view :class="index == 1 ? 'active' : 'item'"> 待办 </view>
    </view>
    <view class="nav" @click="navTo('/pages/data/data')">
      <image
        src="/static/tabbar/data-active.png"
        mode=""
        v-if="index == 2"
      ></image>
      <image src="/static/tabbar/data.png" mode="" v-else></image>
      <view :class="index == 2 ? 'active' : 'item'"> 数据 </view>
    </view>
    <view class="nav" @click="navTo('/pages/my/my')">
      <view class="red-dot" v-if="redDot"></view>
      <image
        src="/static/tabbar/my-active.png"
        mode=""
        v-if="index == 3"
      ></image>
      <image src="/static/tabbar/my.png" mode="" v-else></image>
      <view :class="index == 3 ? 'active' : 'item'"> 我的 </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
image {
  margin: 0 auto;
  width: 54rpx;
  height: 54rpx;
}

.footer {
  position: fixed;
  bottom: 0rpx;
  left: 0rpx;
  z-index: 11;
  display: flex;
  justify-content: space-around;
  align-items: center;
  width: 750rpx;
  min-height: 147rpx;
  padding-top: 27rpx;
  padding-bottom: calc(env(safe-area-inset-bottom) / 2 + 27rpx);
  background: #ffffff;
  box-shadow: 0rpx 3rpx 60rpx 1rpx rgba(0, 0, 0, 0.16);
  border-radius: 22rpx 22rpx 0rpx 0rpx;

  .nav {
    width: 25%;
    text-align: center;
    position: relative;

    .red-dot {
      width: 16rpx;
      height: 16rpx;
      background: #ff0000;
      border: 2px solid #ffffff;
      border-radius: 50%;
      position: absolute;
      top: 0;
      right: 66rpx;
    }

    .active {
      font-weight: bold;
      font-size: 22rpx;
      color: #39455b;
      text-align: center;
    }

    .item {
      font-weight: 500;
      font-size: 22rpx;
      color: #9ca3b5;
      text-align: center;
    }
  }
}
</style>
