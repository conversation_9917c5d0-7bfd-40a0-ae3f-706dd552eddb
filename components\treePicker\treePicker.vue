<script setup>
import { ref, watch, toRef, onMounted, onUnmounted } from "vue"

const emit = defineEmits(["change"])

const props = defineProps({
  valueKey: {
    type: String,
    default: "id",
  },
  textKey: {
    type: String,
    default: "name",
  },
  childrenKey: {
    type: String,
    default: "children",
  },
  data: {
    type: Array,
    default: () => [],
  },
  selectedData: {
    type: Array,
    default: () => [],
  },
  multiple: {
    type: Boolean,
    default: true,
  },
  selectParent: {
    type: Boolean,
    default: true,
  },
  themeColor: {
    type: String,
    default: "#409EFF",
  },
  switchColor: {
    type: String,
    default: "",
  },
  border: {
    type: Boolean,
    default: false,
  },
})

const data = toRef(props, "data")

const treeList = ref([])

const confirm = () => {
  let selectedList = []
  let selectedNames
  let currentLevel = -1
  treeList.value.forEach((item, index) => {
    if (currentLevel >= 0 && item.level > currentLevel) {
    } else {
      if (item.checkStatus === 2) {
        currentLevel = item.level
        selectedList.push(item.id)
        selectedNames = selectedNames
          ? selectedNames + " / " + item.name
          : item.name
      } else {
        currentLevel = -1
      }
    }
  })
  emit("change", selectedList, selectedNames)
}

//格式化原数据（原数据为tree结构）
const formatTreeData = (
  list = [],
  level = 0,
  parentItem,
  isShowChild = true
) => {
  let nextIndex = 0
  let parentId = -1
  let initCheckStatus = 0
  if (parentItem) {
    nextIndex =
      treeList.value.findIndex((item) => item.id === parentItem.id) + 1
    parentId = parentItem.id
    if (!props.multiple) {
      initCheckStatus = 0
    } else initCheckStatus = parentItem.checkStatus == 2 ? 2 : 0
  }
  list.forEach((item) => {
    let isLastLevel = true
    if (item && item[props.childrenKey]) {
      let children = item[props.childrenKey]
      if (Array.isArray(children) && children.length > 0) {
        isLastLevel = false
      }
    }

    let itemT = {
      id: item[props.valueKey],
      name: item[props.textKey],
      level,
      isLastLevel,
      isShow: isShowChild,
      isShowChild: false,
      checkStatus: initCheckStatus,
      orCheckStatus: 0,
      parentId,
      children: item[props.childrenKey],
      childCount: item[props.childrenKey] ? item[props.childrenKey].length : 0,
      childCheckCount: 0,
      childCheckPCount: 0,
    }

    if (props.selectedData.indexOf(itemT.id) >= 0) {
      itemT.checkStatus = 2
      itemT.orCheckStatus = 2
      itemT.childCheckCount = itemT.children ? itemT.children.length : 0
      onItemParentSelect(itemT, nextIndex)
    }

    treeList.value.splice(nextIndex, 0, itemT)
    nextIndex++
  })
}

// 节点打开、关闭切换
const onItemSwitch = (item, index) => {
  if (item.isLastLevel === true) {
    return
  }
  item.isShowChild = !item.isShowChild
  if (item.children) {
    formatTreeData(item.children, item.level + 1, item)
    item.children = undefined
  } else {
    onItemChildSwitch(item, index)
  }
}

// 节点选中、取消选中
const onItemChildSwitch = (item, index) => {
  const firstChildIndex = index + 1
  if (firstChildIndex > 0)
    for (var i = firstChildIndex; i < treeList.value.length; i++) {
      let itemChild = treeList.value[i]
      if (itemChild.level > item.level) {
        if (item.isShowChild) {
          if (itemChild.parentId === item.id) {
            itemChild.isShow = item.isShowChild
            if (!itemChild.isShow) {
              itemChild.isShowChild = false
            }
          }
        } else {
          itemChild.isShow = item.isShowChild
          itemChild.isShowChild = false
        }
      } else {
        return
      }
    }
}

// 节点选中、取消选中
const onItemSelect = (item, index) => {
  if (!props.multiple) {
    item.checkStatus = item.checkStatus == 0 ? 2 : 0

    treeList.value.forEach((v, i) => {
      if (i != index) {
        treeList.value[i].checkStatus = 0
      } else {
        treeList.value[i].checkStatus = 2
      }
    })

    let selectedList = []
    let selectedNames
    selectedList.push(item.id)
    selectedNames = item.name
    emit("change", selectedList, selectedNames)
    return
  }

  let oldCheckStatus = item.checkStatus
  switch (oldCheckStatus) {
    case 0:
      item.checkStatus = 2
      item.childCheckCount = item.childCount
      item.childCheckPCount = 0
      break
    case 1:
    case 2:
      item.checkStatus = 0
      item.childCheckCount = 0
      item.childCheckPCount = 0
      break
    default:
      break
  }
  //子节点 全部选中
  onItemChildSelect(item, index)
  //父节点 选中状态变化
  onItemParentSelect(item, index, oldCheckStatus)
}

const onItemChildSelect = (item, index) => {
  let allChildCount = 0
  if (item.childCount && item.childCount > 0) {
    index++
    while (
      index < treeList.value.length &&
      treeList.value[index].level > item.level
    ) {
      let itemChild = treeList.value[index]
      itemChild.checkStatus = item.checkStatus
      if (itemChild.checkStatus == 2) {
        itemChild.childCheckCount = itemChild.childCount
        itemChild.childCheckPCount = 0
      } else if (itemChild.checkStatus == 0) {
        itemChild.childCheckCount = 0
        itemChild.childCheckPCount = 0
      }
      index++
    }
  }
}

const onItemParentSelect = (item, index, oldCheckStatus) => {
  const parentIndex = treeList.value.findIndex(
    (itemP) => itemP.id == item.parentId
  )
  if (parentIndex >= 0) {
    let itemParent = treeList.value[parentIndex]
    let count = itemParent.childCheckCount
    let oldCheckStatusParent = itemParent.checkStatus

    if (oldCheckStatus == 1) {
      itemParent.childCheckPCount -= 1
    } else if (oldCheckStatus == 2) {
      itemParent.childCheckCount -= 1
    }
    if (item.checkStatus == 1) {
      itemParent.childCheckPCount += 1
    } else if (item.checkStatus == 2) {
      itemParent.childCheckCount += 1
    }

    if (itemParent.childCheckCount <= 0 && itemParent.childCheckPCount <= 0) {
      itemParent.childCheckCount = 0
      itemParent.childCheckPCount = 0
      itemParent.checkStatus = 0
    } else if (itemParent.childCheckCount >= itemParent.childCount) {
      itemParent.childCheckCount = itemParent.childCount
      itemParent.childCheckPCount = 0
      itemParent.checkStatus = 2
    } else {
      itemParent.checkStatus = 1
    }
    onItemParentSelect(itemParent, parentIndex, oldCheckStatusParent)
  }
}

// 重置数据
const reTreeList = () => {
  treeList.value.forEach((v, i) => {
    treeList.value[i].checkStatus = v.orCheckStatus
  })
}

// 全选
const toggleSelectAll = (isAllSelected) => {
  console.log("all", isAllSelected, treeList.value)
  treeList.value.forEach((v, i) => {
    if (isAllSelected) {
      treeList.value[i].checkStatus = 2
    } else {
      treeList.value[i].checkStatus = 0
    }
  })
}

watch(data, () => {
  initTree()
})

const initTree = () => {
  treeList.value = []
  formatTreeData(data.value)
}

onMounted(() => {
  initTree()
})

defineExpose({
  reTreeList,
  confirm,
  toggleSelectAll,
})
</script>

<template>
  <view class="tree-view">
    <scroll-view class="tree-list" :scroll-y="true">
      <block v-for="(item, index) in treeList" :key="index">
        <view
          class="tree-item"
          :style="[
            {
              paddingLeft: item.level * 30 + 'rpx',
            },
          ]"
          :class="{
            itemBorder: border === true,
            show: item.isShow,
          }"
        >
          <view class="item-label">
            <view class="item-content" @tap.stop="onItemSelect(item, index)">
              <view
                class="item-check"
                v-if="selectParent ? true : item.isLastLevel"
              >
                <view
                  class="item-check-yes part"
                  v-if="item.checkStatus == 1"
                  :class="{ radio: !multiple }"
                  :style="{ 'border-color': themeColor }"
                >
                  <view
                    class="item-check-yes-part"
                    :style="{ 'background-color': themeColor }"
                  >
                  </view>
                </view>
                <view
                  class="item-check-yes"
                  v-else-if="item.checkStatus == 2"
                  :class="{ radio: !multiple }"
                  :style="{ 'border-color': themeColor }"
                >
                  <view
                    class="item-check-yes-all"
                    :style="{ 'background-color': themeColor }"
                  >
                  </view>
                </view>
                <view
                  class="item-check-no"
                  v-else
                  :class="{ radio: !multiple }"
                  :style="{ 'border-color': themeColor }"
                ></view>
              </view>
              <view class="item-name">
                {{
                  item.name +
                  (item.childCount ? "(" + item.childCount + ")" : "")
                }}
              </view>
            </view>
            <view class="item-icon" @tap.stop="onItemSwitch(item, index)">
              <view
                v-if="!item.isLastLevel && item.isShowChild"
                class="switch-on"
                :style="{ 'border-left-color': switchColor }"
              >
              </view>
              <view
                v-else-if="!item.isLastLevel && !item.isShowChild"
                class="switch-off"
                :style="{ 'border-top-color': switchColor }"
              >
              </view>
              <view
                v-else
                class="item-last-dot"
                :style="{ 'border-top-color': switchColor }"
              >
              </view>
            </view>
          </view>
        </view>
      </block>
    </scroll-view>
  </view>
</template>

<style lang="scss">
.tree-view {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  height: 100%;

  .tree-list {
    flex: 1;
    height: 100%;
    overflow: hidden;
  }

  .tree-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    line-height: 1;
    height: 0;
    opacity: 0;
    transition: 0.2s;
    overflow: hidden;
  }

  .tree-item.show {
    height: 90rpx;
    opacity: 1;
  }

  .tree-item.showchild:before {
    transform: rotate(90deg);
  }

  .tree-item.last:before {
    opacity: 0;
  }

  .switch-on {
    width: 0;
    height: 0;
    border-left: 10rpx solid transparent;
    border-right: 10rpx solid transparent;
    border-top: 15rpx solid #666;
  }

  .switch-off {
    width: 0;
    height: 0;
    border-bottom: 10rpx solid transparent;
    border-top: 10rpx solid transparent;
    border-left: 15rpx solid #666;
  }

  .item-last-dot {
    position: absolute;
    width: 10rpx;
    height: 10rpx;
    border-radius: 100%;
    background: #666;
  }

  .item-icon {
    width: 26rpx;
    height: 26rpx;
    margin-right: 8rpx;
    padding-right: 20rpx;
    padding-left: 20rpx;
  }

  .item-label {
    flex: 1;
    display: flex;
    align-items: center;
    height: 100%;
    line-height: 1.2;
  }

  .item-content {
    display: flex;
    align-items: center;
  }

  .item-name {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 450rpx;
  }

  .item-check {
    width: 40px;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .item-check-yes,
  .item-check-no {
    width: 20px;
    height: 20px;
    border-top-left-radius: 20%;
    border-top-right-radius: 20%;
    border-bottom-right-radius: 20%;
    border-bottom-left-radius: 20%;
    border-top-width: 1rpx;
    border-left-width: 1rpx;
    border-bottom-width: 1rpx;
    border-right-width: 1rpx;
    border-style: solid;
    border-color: v-bind(themeColor);
    display: flex;
    justify-content: center;
    align-items: center;
    box-sizing: border-box;
  }

  .item-check-yes {
    background-color: v-bind(themeColor);
  }

  .item-check-yes.part {
    background-color: unset !important;
  }

  .item-check-yes-part {
    width: 12px;
    height: 12px;
    border-top-left-radius: 20%;
    border-top-right-radius: 20%;
    border-bottom-right-radius: 20%;
    border-bottom-left-radius: 20%;
    background-color: v-bind(themeColor);
  }

  .item-check-yes-all {
    background-color: unset !important;
    margin-bottom: 4px;
    border: 2px solid;
    border-color: #ffffff;
    border-left: 0;
    border-top: 0;
    height: 12px;
    width: 6px;
    transform-origin: center;
    transition: all 0.3s;
    transform: rotate(45deg);
  }

  .item-check .radio {
    border-top-left-radius: 50%;
    border-top-right-radius: 50%;
    border-bottom-right-radius: 50%;
    border-bottom-left-radius: 50%;
  }

  .item-check .radio .item-check-yes-b {
    border-top-left-radius: 50%;
    border-top-right-radius: 50%;
    border-bottom-right-radius: 50%;
    border-bottom-left-radius: 50%;
  }

  .hover-c {
    opacity: 0.6;
  }

  .itemBorder {
    border-bottom: 1px solid #e5e5e5;
  }
}
</style>
