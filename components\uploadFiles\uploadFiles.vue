<script setup>
import { ref } from "vue"
import { fileExtensionMap, fileTypeMap } from "@/utils/constants.js"
import { fetchUpload } from "@/utils/request.js"

const props = defineProps({
  fileList: {
    type: Array,
    default: [],
  },
})

const emit = defineEmits(["update:fileList", "change"])

const uploadFiles = async (files) => {
  console.log("uploadFiles=====================", files)
  for (const file of files) {
    if (file.url) {
      continue
    }
    emit("change", { fileList: props.fileList })
    const res = await fetchUpload(
      file.tempFilePath,
      {},
      file.filename || file.name || ""
    )
    if (res.code === 200) {
      Object.assign(file, res.result.file)
      emit("change", { fileList: props.fileList })
    }
  }
}

const chooseMedia = () => {
  wx.chooseMedia({
    mediaType: ["image", "video"],
    sourceType: ["album"],
    success: (res) => {
      console.log("chooseMedia success", res)
      const temp = res.tempFiles.map((file) => {
        file.tempFilePath = file.tempFilePath
        file.ext = file.tempFilePath.split(".").pop()
        file.filename = file.tempFilePath.split("/").pop()
        return file
      })
      uploadFiles(temp)
      const fileList = [...props.fileList, ...temp]
      emit("update:fileList", fileList)
      emit("change", { fileList })
    },
  })
}

const chooseMessageFile = () => {
  wx.chooseMessageFile({
    type: "file",
    success: (res) => {
      console.log("chooseMessageFile success", res)
      const temp = res.tempFiles.map((file) => {
        file.tempFilePath = file.path
        file.ext = file.tempFilePath.split(".").pop()
        file.filename = file.name
        return file
      })
      uploadFiles(temp)
      const fileList = [...props.fileList, ...temp]
      emit("update:fileList", fileList)
      emit("change", { fileList })
    },
  })
}

const onUploadBtnClick = () => {
  uni.showActionSheet({
    itemList: ["从相册中选择图片/视频", "从聊天记录选择文件"],
    success: (res) => {
      console.log(res)
      if (res.tapIndex === 0) {
        chooseMedia()
      } else if (res.tapIndex === 1) {
        chooseMessageFile()
      }
    },
  })
}

const onFileItemClick = (file) => {
  console.log("onFileItemClick", file)
  if (fileExtensionMap[file.ext].type === "document") {
    uni.openDocument({
      filePath: file.tempFilePath,
      fileType: file.ext,
    })
  } else {
    uni.previewMedia({
      sources: [
        {
          type: fileExtensionMap[file.ext].type,
          url: file.url || file.full_path || file.tempFilePath,
        },
      ],
    })
  }
}

const onFileItemDelete = (file) => {
  console.log("onFileItemDelete", file)
  const newFileList = props.fileList.filter((item) => item.id !== file.id)
  emit("update:fileList", newFileList)
}
</script>

<template>
  <view class="upload-files">
    <view class="upload-btn" @click="onUploadBtnClick">
      <image src="/static/image/upload-plus.svg" class="upload-btn-icon" />
      <view class="upload-btn-text"
        >支持jpg, png, doc, excel, pdf, zip, mp4(100mb以内)</view
      >
    </view>

    <view class="file-list">
      <view
        v-for="file in fileList"
        :key="file.url"
        class="file-item"
        @click="onFileItemClick(file)"
      >
        <view class="file-info">
          <image
            :src="
              fileExtensionMap[file.ext]?.icon ||
              '/static/image/file-default.svg'
            "
            class="file-icon"
          />
          <view class="file-name">{{ file.filename }}</view>
        </view>
        <image
          src="/static/image/close-dark.svg"
          class="delete-icon"
          @click.stop="onFileItemDelete(file)"
        />
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.upload-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 618rpx;
  background: #f7f8fa;
  border-radius: 20rpx;
  margin: 20rpx auto;
  padding: 28rpx;

  .upload-btn-icon {
    width: 50rpx;
    height: 50rpx;
    margin: 16rpx 0;
  }
  .upload-btn-text {
    font-size: 24rpx;
    color: #9aa0ae;
  }
}

.file-list {
  display: flex;
  flex-direction: column;
  .file-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    max-width: 618rpx;
    height: 80rpx;
    background: #ffffff;
    border: 1rpx solid #bdc4ce;
    border-radius: 20rpx;
    margin: 10rpx auto;
    padding: 14rpx;
    gap: 20rpx;
    overflow: hidden;

    .file-info {
      flex: 1;
      display: flex;
      align-items: center;
      overflow: hidden;

      .file-icon {
        flex-shrink: 0;
        width: 32rpx;
        height: 32rpx;
        margin-right: 10rpx;
      }
      .file-name {
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-weight: 500;
        font-size: 26rpx;
        color: #000000;
      }
    }
    .delete-icon {
      margin-right: 6rpx;
      flex-shrink: 0;
      width: 30rpx;
      height: 30rpx;
    }
  }
}
</style>
