<script setup>
import { ref, computed, onMounted } from "vue"
const plugin = requirePlugin("WechatSI")

const recordRecoManager = plugin.getRecordRecognitionManager()

const emit = defineEmits(["confirm", "cancel"])

const viewState = ref("recording") // recording | cancelling

const viewStateTipsMap = {
  recording: "松手发送，上移取消",
  cancelling: "松手取消",
}

const viewStateTips = computed(() => viewStateTipsMap[viewState.value])

let oringinY = 0
let msgWillSend = false

const onTouchStart = (e) => {
  oringinY = e.touches[0].clientY
  viewState.value = "recording"
  recordRecoManager.start({
    duration: 60000,
    lang: "zh_CN",
  })
}

const onTouchMove = (event) => {
  if (event.touches[0].clientY - oringinY < -50) {
    viewState.value = "cancelling"
  } else {
    viewState.value = "recording"
  }
}

const onTouchEnd = (e) => {
  emit("cancel")
  if (viewState.value === "cancelling") {
    msgWillSend = false
  } else {
    msgWillSend = true
  }
  recordRecoManager.stop()
  viewState.value = "recording"
}

const initRecord = () => {
  recordRecoManager.onStart = function (res) {
    console.log("recordRecoManager.onStart", res)
  }
  //有新的识别内容返回，则会调用此事件
  recordRecoManager.onRecognize = (res) => {
    console.log("recordRecoManager.onRecognize", res)
  }
  // 识别结束事件
  recordRecoManager.onStop = (res) => {
    console.log("recordRecoManager.onStop", res)
    if (msgWillSend) {
      confirm(res.result)
    }
    msgWillSend = false
  }
  // 识别错误事件
  recordRecoManager.onError = (res) => {
    console.log("recordRecoManager.onError", res)
    if (msgWillSend) {
      uni.showToast({
        title: "没有听清楚你说的话",
        icon: "none",
      })
    }
    msgWillSend = false
  }
}

const confirm = (text) => {
  console.log(text)
  emit("confirm", text)
}

onMounted(() => {
  initRecord()
})

defineExpose({
  onTouchStart,
  onTouchMove,
  onTouchEnd,
})
</script>

<template>
  <view class="voice-input">
    <view class="voice-tips">{{ viewStateTips }}</view>
    <view
      class="voice-btn"
      @touchstart="onTouchStart"
      @touchmove="onTouchMove"
      @touchend="onTouchEnd"
      :class="`state_${viewState}`"
    >
      <wave
        :barsCount="30"
        :active="viewState === 'recording'"
        color="#ffffff"
      ></wave>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.voice-input {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.voice-tips {
  padding: 20rpx 0 44rpx;
  font-size: 24rpx;
  color: #6e7191;
  font-weight: 500;
}

.voice-btn {
  width: 100%;
  height: 128rpx;

  border-radius: 48rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.5);
  box-shadow: 0rpx 16rpx 64rpx 0rpx rgba(110, 113, 145, 0.12);
  display: flex;
  justify-content: center;
  align-items: center;

  &.state_recording {
    background: #577f49;
  }

  &.state_cancelling {
    background: #eb5446;
  }
}
</style>
