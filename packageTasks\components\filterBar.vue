<script setup>
const props = defineProps({
  placeholder: {
    type: String,
    default: "关键词搜索",
  },
  keyword: {
    type: String,
    default: "",
  },
  filterActive: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits([
  "update:keyword",
  "openFilter",
  "closeFilter",
  "confirm",
])

const openFilter = () => {
  emit("openFilter")
}

const confirm = () => {
  emit("confirm")
}

const closeFilter = () => {
  emit("closeFilter")
}

const input = (e) => {
  emit("update:keyword", e)
}
</script>

<template>
  <view class="filter-bar">
    <view class="search">
      <up-search
        :show-action="false"
        :placeholder="placeholder"
        placeholderColor="#bdc4ce"
        searchIconColor="#bdc4ce"
        :value="keyword"
        bgColor="#ffffff"
        :searchIconSize="28"
        :inputStyle="{ height: '80rpx' }"
        @change="input"
        @search="confirm"
        @clear="confirm"
      ></up-search>
    </view>
    <view
      class="filter-btn"
      :class="{ active: filterActive }"
      @click="openFilter"
    >
      <text>筛选</text>
      <image
        :src="
          filterActive
            ? '/static/image/filter-active.png'
            : '/static/image/filter.png'
        "
        mode=""
        class="filter-icon"
      ></image>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.filter-bar {
  margin-bottom: 35rpx;
  display: flex;
  gap: 20rpx;

  .search {
    flex: 1;
  }

  .filter-btn {
    width: 160rpx;
    height: 80rpx;
    padding: 0 28rpx;
    background: #ffffff;
    box-shadow: 0rpx 6rpx 13rpx 1rpx rgba(0, 0, 0, 0.04);
    border-radius: 40rpx 40rpx 40rpx 40rpx;
    display: flex;
    justify-content: center;
    align-items: center;

    text {
      font-size: 26rpx;
      color: #21232c;
    }

    .filter-icon {
      width: 28rpx;
      height: 28rpx;
      margin-left: 10rpx;
    }
  }
}
</style>
