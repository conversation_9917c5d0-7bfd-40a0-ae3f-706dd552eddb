<script setup>
import { ref, onMounted, toRef, computed, watch } from "vue"
import ReadMore from "@/pages/matching/components/readMore.vue"
import request from "@/utils/request"
import { useUserStore } from "@/store/user"

const userStore = useUserStore()

const userInfo = computed(() => userStore.userInfo)

const props = defineProps({
  options: {
    type: Array,
    default: () => [],
  },
  modelValue: {
    type: Object,
    default: {},
  },
  showDepartment: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(["update:modelValue", "change"])

const modelValue = toRef(props, "modelValue")

const confirm = () => {
  treeRef.value.confirm()
  
  // 确保部门数据已同步
  if (selectedData.value && selectedData.value.length > 0) {
    modelValue.value.department = selectedData.value
  }
  emit("update:modelValue", modelValue.value)
  emit("change", modelValue.value)
}

const onSelect = (key, optionIndex, item) => {
  if (!modelValue.value[key]) {
    modelValue.value[key] = []
  }

  // 获取实际的值，如果有value数组则使用value，否则使用显示文本
  const actualValue = item.value
    ? item.value[optionIndex]
    : item.options[optionIndex]

  if (modelValue.value[key].includes(actualValue)) {
    modelValue.value[key] = modelValue.value[key].filter(
      (selectedItem) => selectedItem !== actualValue
    )
  } else {
    modelValue.value[key].push(actualValue)
  }
}

const treeRef = ref(null)
const treeData = ref([])

const loadDepartmentTree = () => {
  request({
    url: "/mini/department_tree",
    method: "get",
    data: {
      p_code: userInfo.value.district_code,
    },
  }).then((res) => {
    treeData.value = res.result
  })
}

const selectedData = ref([])

// 监听 modelValue.department 变化，同步到 selectedData
watch(() => modelValue.value.department, (newVal) => {
  selectedData.value = newVal || []
}, { immediate: true })

const onChange = (e) => {
  console.log("onChange==========", e)
  selectedData.value = e
  // 同步部门数据到 modelValue
  if (!modelValue.value.department) {
    modelValue.value.department = []
  }
  modelValue.value.department = e
  // emit("update:modelValue", modelValue.value)
  // emit("change", modelValue.value)
}

// 判断选项是否被选中
const isOptionSelected = (item, optionIndex) => {
  const actualValue = item.value
    ? item.value[optionIndex]
    : item.options[optionIndex]
  return props.modelValue[item.key]?.includes(actualValue)
}

onMounted(() => {
  loadDepartmentTree()
})
</script>

<template>
  <view class="filter">
    <view class="filter-title">筛选</view>
    <view class="filter-content">
      <view class="filter-item" v-for="(item, index) in options" :key="index">
        <view class="filter-item-title">
          {{ item.title }}

          <view class="filter-item-title-right">
            <text>{{ item.expand ? "收起" : "展开" }}</text>

            <up-icon
              v-if="!item.expand"
              @click="item.expand = !item.expand"
              name="arrow-down"
            ></up-icon>
            <up-icon
              v-if="item.expand"
              @click="item.expand = !item.expand"
              name="arrow-up"
            ></up-icon>
          </view>
        </view>
        <read-more :expand="item.expand">
          <view class="filter-item-content">
            <view
              class="filter-item-content-item"
              :class="{ active: isOptionSelected(item, index1) }"
              v-for="(option, index1) in item.options"
              :key="index1"
              @click="onSelect(item.key, index1, item)"
            >
              {{ option }}
            </view>
          </view>
        </read-more>
      </view>

      <view class="wrapper" v-if="showDepartment">
        <treePicker
          ref="treeRef"
          valueKey="code"
          :data="treeData"
          :selectedData="selectedData"
          themeColor="#577F49"
          @change="onChange"
        />
      </view>
    </view>
    <view class="filter-footer">
      <button class="reset" @click="emit('update:modelValue', {})">重置</button>
      <button class="confirm" @click="confirm">确定</button>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.filter {
  display: flex;
  flex-direction: column;
  max-height: 80vh;

  .filter-title {
    text-align: center;
    font-size: 34rpx;
    color: #303445;
    font-weight: bold;
    padding: 38rpx 0;
  }

  .filter-content {
    min-height: 50vh;
    padding: 0 38rpx;
    flex: 1;
    overflow-y: auto;

    .filter-item {
      margin-bottom: 48rpx;

      .filter-item-title {
        font-weight: 500;
        font-size: 28rpx;
        color: #21232c;
        margin-bottom: 12rpx;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .filter-item-title-right {
          flex-shrink: 0;
          display: flex;
          align-items: center;
          gap: 4rpx;
          font-size: 24rpx;
          color: #989898;
        }
      }
      .filter-item-content {
        display: flex;
        flex-wrap: wrap;
        gap: 20rpx;

        .filter-item-content-item {
          min-width: 189rpx;
          height: 72rpx;
          line-height: 72rpx;
          text-align: center;
          padding: 0 32rpx;
          border-radius: 55rpx 55rpx 55rpx 55rpx;
          background: #f7f8fa;
          color: #434a54;

          &.active {
            border: 2rpx solid #4c9f6b;
            background: #f3ffef;
            color: #577f49;
          }
        }
      }
    }
  }

  .filter-footer {
    width: 100%;
    display: flex;
    justify-content: space-between;
    padding: 40rpx 38rpx;
    gap: 34rpx;

    button {
      flex: 1;
      height: 100rpx;
      line-height: 100rpx;
      font-size: 34rpx;
      color: #fff;
      border-radius: 50rpx 50rpx 50rpx 50rpx;
    }
    .reset {
      background: rgba(87, 127, 73, 0.1);
      color: #577f49;
    }
    .confirm {
      background: #577f49;
      color: #ffffff;
    }
  }
}
</style>
