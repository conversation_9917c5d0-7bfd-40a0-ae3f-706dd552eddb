<script setup>
import ReadMore from "@/pages/matching/components/readMore.vue"
import { computed } from "vue"
import { personStudyFormMap, personIsEngineeringMap } from "@/packageTasks/utils/consts"

const props = defineProps({
  baseInfoExpand: {
    type: Boolean,
    default: false,
  },
  peopleDetail: {
    type: Object,
    default: () => ({}),
  },
})

const emit = defineEmits(["update:baseInfoExpand"])

// 解析手机号字段，用空格分隔
const phoneNumbers = computed(() => {
  if (!props.peopleDetail.phone) return []
  return props.peopleDetail.phone.split(" ").filter((phone) => phone.trim())
})

const call = (phoneNumber = null) => {
  const number = phoneNumber || props.peopleDetail.phone
  if (number) {
    uni.makePhoneCall({
      phoneNumber: number,
    })
  } else {
    uni.showToast({
      title: "无联系方式",
      icon: "none",
    })
  }
}

const fieldList = [
  {
    label: "民族",
    key: "nation",
  },
  {
    label: "政治面貌",
    key: "political_outlook",
  },
  {
    label: "所在区",
    key: "district_name",
  },
  {
    label: "户籍地(高校或街道)",
    key: "address",
  },
  {
    label: "通讯地址",
    key: "address",
  },
  {
    label: "所学专业",
    key: "major",
  },
  {
    label: "学习类型",
    key: "study_form",
    render: (value) => {
      return personStudyFormMap[value]?.text || "未知"
    },
  },
  {
    label: "是否理工类",
    key: "is_engineering",
    render: (value) => {
      return personIsEngineeringMap[value]?.text || "未知"
    },
  },
  {
    label: "高级以上职业技能等级证书(职业资格证书)名称及编号",
    key: "skill_certificate",
  },
]
</script>

<template>
  <view class="name-call">
    <view class="name">
      {{ peopleDetail.name }}
      <image
        v-if="peopleDetail.sex"
        class="gender"
        :src="
          peopleDetail.sex == '男'
            ? '/static/image/boy.png'
            : '/static/image/girl.png'
        "
      ></image>
    </view>
    <image
      src="/static/image/call.svg"
      mode=""
      class="call"
      @click="call(phoneNumbers[0])"
    ></image>
  </view>
  <view class="base-info">
    <view class="base-info-expand">
      <view class="base-info-expand-title"> 基础信息 </view>

      <view
        class="base-info-expand-content"
        @click="emit('update:baseInfoExpand', !baseInfoExpand)"
      >
        <text>{{ baseInfoExpand ? "收起" : "展开" }}</text>

        <up-icon
          v-if="!baseInfoExpand"
          @click="emit('update:baseInfoExpand', !baseInfoExpand)"
          name="arrow-down"
        ></up-icon>
        <up-icon
          v-if="baseInfoExpand"
          @click="emit('update:baseInfoExpand', !baseInfoExpand)"
          name="arrow-up"
        ></up-icon>
      </view>
    </view>
    <read-more :expand="baseInfoExpand" :max-height="0">
      <view class="info">
        <view class="item" v-if="phoneNumbers.length > 1">
          <view class="label"> 其他手机号 </view>
          <view class="value">
            <view class="another-phone">
              <view
                class="another-phone-item"
                v-for="(phone, index) in phoneNumbers.slice(1)"
                :key="index"
              >
                <view class="another-phone-item-label">
                  手机号{{ index + 2 }}
                </view>
                <view class="another-phone-item-call">
                  <image
                    src="/static/image/call.svg"
                    mode=""
                    class="call"
                    @click="call(phone)"
                  ></image>
                </view>
              </view>
            </view>
          </view>
        </view>

        <view class="item" v-for="field in fieldList" :key="field.key">
          <view class="label">
            {{ field.label }}
          </view>
          <view class="value">
            <template v-if="field.key === 'skill_certificate'">
              <view class="skill-certificate">
                <view
                  class="skill-certificate-item"
                  v-for="item in peopleDetail[field.key]"
                  :key="item.name"
                >
                  <view class="skill-certificate-item-name">
                    {{ item.name }}
                  </view>
                  <view class="skill-certificate-item-number">
                    {{ item.number }}
                  </view>
                </view>
              </view>
            </template>
            <template v-else>
              {{
                field.render
                  ? field.render(peopleDetail[field.key])
                  : peopleDetail[field.key]
              }}
            </template>
          </view>
        </view>
      </view>
    </read-more>
  </view>
</template>

<style lang="scss" scoped>
.name-call {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .name {
    display: flex;
    align-items: center;
    font-size: 52rpx;
    font-weight: bold;
    color: #21232c;

    .gender {
      width: 49rpx;
      height: 49rpx;
      margin-left: 22rpx;
    }
  }

  .call {
    width: 56rpx;
    height: 56rpx;
    border: 1rpx solid #c3cbd6;
    padding: 8rpx;
    border-radius: 50%;
  }
}

.base-info {
  margin-top: 64rpx;
  border-bottom: 2rpx solid #0ba133;

  .base-info-expand {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 30rpx;

    .base-info-expand-title {
      font-size: 32rpx;
      color: #111111;
    }

    .base-info-expand-content {
      display: flex;
      align-items: center;
      gap: 4rpx;
      font-size: 26rpx;
      color: #989898;
    }
  }

  .info {
    .item {
      padding: 20rpx 0;
      border-bottom: 2rpx solid #f6f6f6;

      .label {
        font-size: 28rpx;
        color: #9ca3b5;
        margin-bottom: 7rpx;
      }

      .value {
        font-size: 32rpx;
        color: #111111;
      }

      .another-phone {
        display: flex;
        align-items: center;
        gap: 120rpx;

        .another-phone-item {
          display: flex;
          align-items: center;
          gap: 20rpx;

          .another-phone-item-label {
            font-size: 32rpx;
            color: #111111;
            margin-right: 50rpx;
          }

          .another-phone-item-call {
            display: flex;
            align-items: center;
            justify-content: center;
            border: 1rpx solid #c3cbd6;
            border-radius: 50%;
            overflow: hidden;

            .call {
              width: 24rpx;
              height: 24rpx;
            }
          }
        }
      }

      .skill-certificate {
        display: flex;
        flex-direction: column;
        gap: 10rpx;

        .skill-certificate-item {
          display: flex;
          flex-direction: column;
          gap: 10rpx;

          .skill-certificate-item-name {
            font-size: 32rpx;
            color: #111111;
          }

          .skill-certificate-item-number {
            font-size: 32rpx;
            color: #111111;
          }
        }
      }
    }
  }
}
</style>
