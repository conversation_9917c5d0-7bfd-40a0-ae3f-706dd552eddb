<script setup>
import { ref } from "vue"
import {
  personEducationMap,
  personGraduateMap,
  personIsEngineeringMap,
  personPriorityMap,
} from "@/packageTasks/utils/consts"

const props = defineProps({
  item: {
    type: Object,
    default: () => {},
  },
})

const emit = defineEmits(["click"])

const handleClick = () => {
  emit("click", props.item)
}
</script>

<template>
  <view class="box" @click="handleClick">
    <view class="box-left">
      <view class="title">
        {{ item.name }}
        <image
          v-if="item.sex"
          :src="
            item.sex === 2 ? '/static/image/girl.png' : '/static/image/boy.png'
          "
          mode=""
          class="sex-icon"
        ></image>
        <view class="extra-info">
          <slot name="extra-info"></slot>
        </view>
      </view>
      <view class="date">
        <text>{{ personEducationMap[item.education].text }} · </text>
        <text>{{ personGraduateMap[item.graduate].text }} · </text>
        <text>{{ personIsEngineeringMap[item.is_engineering].text }} · </text>
        <text>{{ personPriorityMap[item.priority].text }} · </text>
        <text v-if="item.age">{{ item.age }}岁</text>
      </view>
    </view>
    <view class="box-right">
      <slot name="right"></slot>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.box {
  width: 702rpx;
  background: rgba(255, 255, 255, 0.94);
  box-shadow: 0rpx 6rpx 13rpx 1rpx rgba(0, 0, 0, 0.04);
  border-radius: 20rpx 20rpx 20rpx 20rpx;
  border: 2rpx solid #ffffff;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  padding: 46rpx 49rpx;

  .checkbox {
    width: 40rpx;
    height: 40rpx;
    margin-right: 54rpx;
    display: flex;
    justify-content: center;
    align-items: center;

    image {
      width: 40rpx;
      height: 40rpx;
    }
  }

  .sex-icon {
    width: 30rpx;
    height: 30rpx;
    margin-left: 18rpx;
  }

  .box-left {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    line-height: 40rpx;
    .title {
      font-weight: 500;
      font-size: 32rpx;
      color: #333333;
      display: flex;
      align-items: center;
    }
    .date {
      font-weight: 400;
      font-size: 24rpx;
      color: #bdc4ce;
      text-align: left;
      font-style: normal;
      text-transform: none;
      margin-top: 10rpx;
    }
    .extra-info {
      font-size: 24rpx;
      text-align: left;
      margin-left: 10rpx;
    }
  }
  .box-right {
    flex-shrink: 0;
    margin-left: 40rpx;
    font-weight: 400;
    font-size: 28rpx;
    color: #9ca3b5;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
}
</style>
