<script setup>
import { ref, computed, watch } from "vue"
import {
  getStatisticsMissionSocialPersonList,
  createStatisticsQueryBuilder,
} from "@/packageTasks/utils/api/missionPerson"
import { personEducationMap } from "@/packageTasks/utils/consts"
import { usePagination } from "@/utils/hooks"
import socialNumberOfPeopleTable from "./tables/socialNumberOfPeopleTable.vue"
import socialEducationTable from "./tables/socialEducationTable.vue"
import socialAgeTable from "./tables/socialAgeTable.vue"

const props = defineProps({
  stages: {
    type: Array,
    default: () => [],
  },
  departmentTree: {
    type: Array,
    default: () => [],
  },
  myDepartmentCode: {
    type: String,
    default: "",
  },
  missionId: {
    type: [String, Number],
    default: "",
  },
})

const keyword = ref("")
const list = ref([])
const loading = ref(false)

// 筛选参数
const formData = ref({
  dimension: "person", // person, age, education
  education: `${personEducationMap["1"].status}`, // 文化程度
})

// 维度选项
const radiolist = ref([
  {
    name: "人数",
    value: "person",
    disabled: false,
  },
  {
    name: "年龄",
    value: "age",
    disabled: false,
  },
  {
    name: "学业情况",
    value: "education",
    disabled: false,
  },
])

const radiovalue = computed({
  get: () => formData.value.dimension,
  set: (value) => {
    formData.value.dimension = value
    // 切换维度时重新加载数据
    refresh()
  },
})

// 文化程度选项
const educationOptions = computed(() => {
  return Object.values(personEducationMap).map((item) => ({
    value: `${item.status}`,
    text: item.text,
  }))
})

// 部门选项 - 这里简化处理，实际应该根据departmentTree构建
const departmentOptions = computed(() => {
  return props.departmentTree || []
})

// 加载数据
const loadData = async (options = {}) => {
  loading.value = true
  try {
    const query = createStatisticsQueryBuilder(props.missionId)
      .dimension(formData.value.dimension)
      .keyword(keyword.value)
      .page(options.page || 1, options.pageSize || 10)

    // 如果是文化程度维度，添加文化程度参数
    if (formData.value.dimension === "education") {
      query.education(formData.value.education)
    }

    const params = query.build()
    const response = await getStatisticsMissionSocialPersonList(params)

    if (response.code === 200) {
      const data = response.result?.data || []
      if (options.page === 1) {
        list.value = data
      } else {
        list.value.push(...data)
      }
      return {
        result: {
          data: data,
          total: response.result?.total || 0,
          current_page: response.result?.current_page || 1,
          per_page: response.result?.per_page || 10,
        },
      }
    } else {
      throw new Error(response.message || "加载失败")
    }
  } catch (error) {
    console.error("加载数据失败:", error)
    uni.showToast({
      title: error.message || "加载失败",
      icon: "none",
    })
    return { result: { data: [], total: 0 } }
  } finally {
    loading.value = false
  }
}

const { page, loadPage, loadMoreStatus, refresh } = usePagination({
  loadData,
  list,
})

// 搜索
const search = () => {
  refresh()
}

// 部门选择变化
const onDepartmentChange = (value) => {
  formData.value.department_code = value
  refresh()
}

// 文化程度选择变化
const onEducationChange = (value) => {
  formData.value.education = value
  refresh()
}

// 文化程度筛选弹窗
const educationFilterVisible = ref(false)
const tempEducation = ref("")

const openEducationFilter = () => {
  tempEducation.value = formData.value.education
  educationFilterVisible.value = true
}

const closeEducationFilter = () => {
  educationFilterVisible.value = false
}

const selectEducation = (value) => {
  tempEducation.value = value
}

const confirmEducation = () => {
  formData.value.education = tempEducation.value
  educationFilterVisible.value = false
  refresh()
}

// 监听missionId变化
watch(
  () => props.missionId,
  (newVal) => {
    if (newVal) {
      refresh()
    }
  },
  { immediate: true }
)

defineExpose({
  list,
  loadPage,
  refresh,
})
</script>

<template>
  <view class="content">
    <view class="filter-bar">
      <view class="type-radio-group">
        <customRadioGroup v-model="radiovalue" placement="row">
          <customRadioItem
            v-for="(item, index) in radiolist"
            :key="index"
            :value="item.value"
            :label="item.name"
            size="small"
          />
        </customRadioGroup>
      </view>
      <view class="divider"></view>
      <view class="search">
        <up-search
          :show-action="false"
          v-model="keyword"
          placeholder="请输入关键词"
          @search="search"
        />
      </view>
    </view>

    <!-- 筛选条件 -->
    <view class="filter-section" v-if="formData.dimension === 'education'">
      <!-- 文化程度筛选按钮 -->
      <view class="filter-bar">
        <view class="filter-item" @click="openEducationFilter">
          <text
            class="filter-text"
            :class="{ 'filter-text-active': formData.education }"
          >
            {{
              educationOptions.find((item) => item.value === formData.education)
                ?.text || "筛选文化程度"
            }}
          </text>
          <image src="/static/image/down.png" />
        </view>
      </view>
    </view>

    <!-- 文化程度筛选弹窗 -->
    <up-popup
      v-model="educationFilterVisible"
      mode="bottom"
      :safe-area-inset-bottom="true"
      :border-radius="10"
      :closeable="true"
      @close="closeEducationFilter"
    >
      <view class="education-filter">
        <view class="filter-title">选择文化程度</view>
        <view class="filter-content">
          <view
            class="filter-option"
            :class="{ active: formData.education === option.value }"
            v-for="option in educationOptions"
            :key="option.value"
            @click="selectEducation(option.value)"
          >
            {{ option.text }}
          </view>
        </view>
        <view class="filter-footer">
          <button class="cancel-btn" @click="closeEducationFilter">取消</button>
          <button class="confirm-btn" @click="confirmEducation">确定</button>
        </view>
      </view>
    </up-popup>

    <!-- 表格内容 -->
    <view class="table-container">
      <socialNumberOfPeopleTable
        v-if="radiovalue === 'person'"
        :data="list"
        :loading="loading"
      />

      <socialEducationTable
        v-if="radiovalue === 'education'"
        :data="list"
        :loading="loading"
      />

      <socialAgeTable
        v-if="radiovalue === 'age'"
        :data="list"
        :loading="loading"
      />

      <empty
        v-if="list.length === 0 && loadMoreStatus === 'nomore' && !loading"
      />
    </view>
  </view>
</template>

<script>
export default {
  options: {
    styleIsolation: "shared",
  },
}
</script>

<style lang="scss" scoped>
.content {
  padding: 24rpx;

  .filter-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20rpx;

    .divider {
      width: 1rpx;
      height: 30rpx;
      background: #bdc4ce;
      margin: 0 20rpx;
    }

    :deep(.u-search__content) {
      width: 300rpx;
      position: relative;
      padding-left: 0;
      padding-right: 55rpx;
      background-color: unset !important;

      .u-search__content__input {
        background-color: unset !important;
      }

      .u-search__content__input--placeholder {
        color: #bdc4ce;
        font-size: 26rpx;
      }

      .u-search__content__icon {
        position: absolute;
        right: 0rpx;
        z-index: 0;
      }

      .u-search__content__close {
        position: absolute;
        right: 0rpx;
        z-index: 1;
      }
    }
  }

  .filter-section {
    background: #f8f9fa;
    border-radius: 8rpx;
    margin-bottom: 20rpx;

    .filter-bar {
      justify-content: flex-end;

      .filter-item {
        min-width: 165rpx;
        max-width: 400rpx;
        height: 60rpx;
        background: #ffffff;
        border-radius: 37rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 25rpx;
        font-size: 26rpx;
        color: #577f49;
        padding: 0 20rpx;

        .filter-text {
          max-width: 300rpx;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .filter-text-active {
          color: #577f49;
          font-weight: bold;
        }

        image {
          width: 16rpx;
          height: 8rpx;
          flex-shrink: 0;
        }
      }
    }
  }

  .table-container {
    min-height: 400rpx;
  }
}

/* 文化程度筛选弹窗样式 */
.education-filter {
  .filter-title {
    text-align: center;
    font-size: 34rpx;
    color: #303445;
    font-weight: bold;
    padding: 38rpx 0;
  }

  .filter-content {
    padding: 0 38rpx;
    min-height: 200rpx;

    .filter-option {
      padding: 20rpx 30rpx;
      margin-bottom: 20rpx;
      background: #f8f9fa;
      border-radius: 8rpx;
      font-size: 28rpx;
      color: #333;
      text-align: center;

      &.active {
        background: #577f49;
        color: #fff;
      }
    }
  }

  .filter-footer {
    display: flex;
    gap: 20rpx;
    padding: 30rpx 38rpx;

    .cancel-btn,
    .confirm-btn {
      flex: 1;
      height: 80rpx;
      border: none;
      border-radius: 8rpx;
      font-size: 28rpx;
    }

    .cancel-btn {
      background: #f5f5f5;
      color: #666;
    }

    .confirm-btn {
      background: #577f49;
      color: #fff;
    }
  }
}
</style>
