<script setup>
import { ref, computed } from "vue"

const props = defineProps({
  data: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  }
})

const columns = ref([
  { title: "", type: "expand", width: "50rpx" },
  {
    key: "name",
    title: "单位",
    width: "160rpx",
    fixed: "left",
  },
  {
    key: "total.total",
    title: "总人数",
    width: "100rpx",
  },
  {
    key: "total.age_18",
    title: "18岁",
    width: "80rpx",
  },
  {
    key: "total.age_19",
    title: "19岁",
    width: "80rpx",
  },
  {
    key: "total.age_20",
    title: "20岁",
    width: "80rpx",
  },
  {
    key: "total.age_21",
    title: "21岁",
    width: "80rpx",
  },
  {
    key: "total.age_22",
    title: "22岁",
    width: "80rpx",
  },
  {
    key: "total.age_23",
    title: "23岁",
    width: "80rpx",
  },
  {
    key: "total.age_24",
    title: "24岁",
    width: "80rpx",
  },
  {
    key: "total.age_25",
    title: "25岁",
    width: "80rpx",
  },
  {
    key: "total.age_26",
    title: "26岁",
    width: "80rpx",
  },
])

const tableData = computed(() => {
  if (!props.data || props.data.length === 0) return []
  
  // 为每个部门数据添加children，用于展开显示详细信息
  return props.data.map(item => {
    const children = []
    
    // 添加"其中：男"行
    if (item.male) {
      children.push({
        name: "其中：男",
        total: item.male,
        code: `${item.code}_male`
      })
    }
    
    // 添加"其中：女"行
    if (item.female) {
      children.push({
        name: "其中：女", 
        total: item.female,
        code: `${item.code}_female`
      })
    }
    
    return {
      ...item,
      children: children.length > 0 ? children : undefined
    }
  })
})

// 默认展开的行keys
const expandRowKeys = computed(() => {
  // 可以根据需要设置默认展开的行
  return []
})
</script>

<template>
  <view class="table-wrapper">
    <view v-if="loading" class="loading">加载中...</view>
    <up-table2 
      v-else 
      :columns="columns" 
      :data="tableData"
      :tree-props="{ children: 'children' }"
      :expand-row-keys="expandRowKeys"
    />
  </view>
</template>

<style lang="scss" scoped>
.table-wrapper {
  .loading {
    text-align: center;
    padding: 40rpx;
    color: #999;
  }
}
</style>
