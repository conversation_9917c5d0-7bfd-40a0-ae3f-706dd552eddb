<script setup>
import { ref, computed } from "vue"
import matchingFilter from "@/packageTasks/components/matchingFilter.vue"

const props = defineProps({
  data: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  }
})

const columns = ref([
  { title: "", type: "expand", width: "50rpx" },
  {
    key: "name",
    title: "单位",
    width: "200rpx",
  },
  {
    key: "total_total",
    title: "总人数",
    width: "120rpx",
  },
  {
    key: "total_graduate_before",
    title: "在校生",
    width: "120rpx",
  },
  {
    key: "total_graduate",
    title: "毕业生",
    width: "120rpx",
  },
  {
    key: "total_just_graduate",
    title: "应届毕业生",
    width: "140rpx",
  },
])

const tableData = computed(() => {
  if (!props.data || props.data.length === 0) return []
  
  // 应用筛选条件
  let filteredData = props.data
  if (filterValues.value.education && filterValues.value.education.length > 0) {
    // 根据文化程度筛选逻辑（可根据实际需求调整）
    // filteredData = filteredData.filter(item => ...)
  }
  if (filterValues.value.degree && filterValues.value.degree.length > 0) {
    // 根据学位筛选逻辑（可根据实际需求调整）
    // filteredData = filteredData.filter(item => ...)
  }
  
  // 为每个部门数据添加children，用于展开显示详细信息
  return filteredData.map(item => {
    const children = []
    
    // 添加"其中：男"行
    if (item.male && (item.male.total > 0 || item.male.graduate_before > 0 || item.male.graduate > 0 || item.male.just_graduate > 0)) {
      children.push({
        name: "其中：男",
        code: `${item.code}_male`,
        total_total: item.male.total,
        total_graduate_before: item.male.graduate_before,
        total_graduate: item.male.graduate,
        total_just_graduate: item.male.just_graduate
      })
    }
    
    // 添加"其中：女"行
    if (item.female && (item.female.total > 0 || item.female.graduate_before > 0 || item.female.graduate > 0 || item.female.just_graduate > 0)) {
      children.push({
        name: "其中：女",
        code: `${item.code}_female`,
        total_total: item.female.total,
        total_graduate_before: item.female.graduate_before,
        total_graduate: item.female.graduate,
        total_just_graduate: item.female.just_graduate
      })
    }
    
    // 添加"其中：京籍"行
    if (item.is_beijing && (item.is_beijing.total > 0 || item.is_beijing.graduate_before > 0 || item.is_beijing.graduate > 0 || item.is_beijing.just_graduate > 0)) {
      children.push({
        name: "其中：京籍",
        code: `${item.code}_beijing`,
        total_total: item.is_beijing.total,
        total_graduate_before: item.is_beijing.graduate_before,
        total_graduate: item.is_beijing.graduate,
        total_just_graduate: item.is_beijing.just_graduate
      })
    }
    
    // 添加"其中：非京籍"行
    if (item.is_not_beijing && (item.is_not_beijing.total > 0 || item.is_not_beijing.graduate_before > 0 || item.is_not_beijing.graduate > 0 || item.is_not_beijing.just_graduate > 0)) {
      children.push({
        name: "其中：非京籍",
        code: `${item.code}_not_beijing`,
        total_total: item.is_not_beijing.total,
        total_graduate_before: item.is_not_beijing.graduate_before,
        total_graduate: item.is_not_beijing.graduate,
        total_just_graduate: item.is_not_beijing.just_graduate
      })
    }
    
    return {
      ...item,
      // 扁平化主数据的total字段
      total_total: item.total.total,
      total_graduate_before: item.total.graduate_before,
      total_graduate: item.total.graduate,
      total_just_graduate: item.total.just_graduate,
      children: children.length > 0 ? children : undefined
    }
  })
})

// 默认展开的行keys
const expandRowKeys = computed(() => {
  // 可以根据需要设置默认展开的行
  return []
})

const filterVisible = ref(false)
const filterOptions = ref([
  {
    title: "文化程度",
    key: "education",
    options: ["专科", "本科", "本科及以上"],
    value: [1, 2, 3],
  },
  {
    title: "取得学位",
    key: "degree",
    options: ["无学位", "学士", "硕士", "博士"],
    value: [0, 1, 2, 3],
  },
])
const filterValues = ref({
  education: [],
  degree: [],
})
const filterActive = computed(() => {
  return (filterValues.value.education && filterValues.value.education.length > 0) ||
         (filterValues.value.degree && filterValues.value.degree.length > 0)
})

const onFilterChange = (value) => {
  console.log("onFilterChange", value)
  filterValues.value = { ...value }
  filterVisible.value = false
}

const openFilter = () => {
  filterVisible.value = true
}

const closeFilter = () => {
  filterVisible.value = false
}
</script>

<template>
  <view class="data-content">
    <view class="filter-bar">
      <view class="filter-bar-title">
        <text>筛选</text>
        <view class="filter-btn" @click="openFilter">
          <image src="/static/image/filter.svg" mode=""></image>
        </view>
      </view>
    </view>
    <view class="table-wrapper">
      <view v-if="loading" class="loading">加载中...</view>
      <up-table2 
        v-else 
        :columns="columns" 
        :data="tableData"
        :tree-props="{ children: 'children' }"
        :expand-row-keys="expandRowKeys"
      />
    </view>

    <up-popup
      v-model="filterVisible"
      mode="bottom"
      :safe-area-inset-bottom="true"
      :border-radius="10"
      :closeable="true"
      @close="closeFilter"
    >
      <matchingFilter @close="closeFilter" />
    </up-popup>
  </view>
</template>

<style lang="scss" scoped>
.data-content {
  .filter-bar {
    .filter-bar-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20rpx;

      text {
        font-size: 28rpx;
        color: #333;
      }

      .filter-btn {
        width: 60rpx;
        height: 60rpx;
        display: flex;
        justify-content: center;
        align-items: center;
        background: #f5f5f5;
        border-radius: 8rpx;

        image {
          width: 32rpx;
          height: 32rpx;
        }
      }
    }
  }

  .table-wrapper {
    .loading {
      text-align: center;
      padding: 40rpx;
      color: #999;
    }
  }
}
</style>
