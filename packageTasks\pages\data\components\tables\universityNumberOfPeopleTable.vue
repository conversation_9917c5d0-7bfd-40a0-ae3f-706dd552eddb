<script setup>
import { ref, computed } from "vue"

const props = defineProps({
  data: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  }
})

const columns = ref([
  {
    key: "name",
    title: "部门",
    width: "220rpx",
  },
  {
    key: "count",
    title: "总人数",
    width: "120rpx",
  },
  {
    key: "male",
    title: "男",
    width: "120rpx",
  },
  {
    key: "female",
    title: "女",
    width: "120rpx",
  },
  {
    key: "is_beijing",
    title: "京籍",
    width: "120rpx",
  },
  {
    key: "is_not_beijing",
    title: "非京籍",
    width: "120rpx",
  },
])

const tableData = computed(() => {
  return props.data || []
})
</script>

<template>
  <view class="table-wrapper">
    <view v-if="loading" class="loading">加载中...</view>
    <up-table2 v-else :columns="columns" :data="tableData" />
  </view>
</template>

<style lang="scss" scoped>
.table-wrapper {
  .loading {
    text-align: center;
    padding: 40rpx;
    color: #999;
  }
}
</style>
