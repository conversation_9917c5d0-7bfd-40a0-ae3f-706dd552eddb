<script setup>
import { ref, watch, computed, nextTick } from "vue"
import {
  onLoad,
  onShow,
  onPullDownRefresh,
  onReachBottom,
} from "@dcloudio/uni-app"
import request from "@/utils/request"
import { useUserStore } from "@/store/user"
import { useCommonStore } from "@/store/common"

import universityContent from "./components/universityContent.vue"
import socialContent from "./components/socialContent.vue"

const commonStore = useCommonStore()
const userStore = useUserStore()

const missionId = ref(0)

const universityContentRef = ref(null)
const socialContentRef = ref(null)

const tabs = computed(() => [
  {
    name: "university",
    title: "高校人员",
    component: universityContentRef,
    show: 1,
  },
  {
    name: "social",
    title: "社会适龄青年",
    component: socialContentRef,
    show: 1,
  },
])
const currentIndex = ref(0)
const currentTab = computed(() => tabs.value[currentIndex.value])

const changeTab = (index) => {
  currentIndex.value = index
  if (currentTab.value.component?.value?.list.length === 0) {
    currentTab.value.component.value.loadPage()
  }
}

// 初始化数据
const stages = ref([])
const departmentTree = ref([])
const myDepartmentCode = computed(() => userStore.userInfo.department_code)

// 加载部门树数据的函数（如果需要的话）
const loadDepartmentTree = async () => {
  try {
    const response = await request({
      url: "/mini/department_tree",
      method: "get",
      data: {
        p_code: userStore.userInfo.district_code,
      },
    })
    departmentTree.value = response.result || []
  } catch (error) {
    console.error('加载部门树失败:', error)
  }
}

onLoad((options) => {
  let tab = 0
  missionId.value = options.missionId
  if (options.tab) {
    let index = tabs.value.findIndex((tab) => tab.name === options.tab)
    tab = index !== -1 ? index : 0
  }
  
  // 加载部门树数据
  loadDepartmentTree()
  
  nextTick(() => {
    changeTab(tab)
  })
})

onPullDownRefresh(() => {
  currentTab.value?.component.value?.refresh()
  uni.stopPullDownRefresh()
})

onReachBottom(() => {
  currentTab.value?.component.value?.loadPage()
})

onShow(() => {})
</script>

<template>
  <navHeader
    title="人员库数据汇总"
    bg="/static/image/bg.png"
    showLeft
  ></navHeader>
  <view>
    <view class="header">
      <view class="title">
        <template v-for="(tab, index) in tabs" :key="index">
          <view
            v-if="tab.show"
            :class="currentIndex == index ? 'checked' : ''"
            @click="changeTab(index)"
          >
            <view class="">
              {{ tab.title }}
            </view>
            <view class="green" v-if="currentIndex == index"> </view>
          </view>
        </template>
      </view>
    </view>
    <view class="body">
      <universityContent
        ref="universityContentRef"
        v-show="currentTab.name == 'university'"
        :stages="stages"
        :departmentTree="departmentTree"
        :myDepartmentCode="myDepartmentCode"
        :missionId="missionId"
      />
      <socialContent
        ref="socialContentRef"
        v-show="currentTab.name == 'social'"
        :stages="stages"
        :departmentTree="departmentTree"
        :myDepartmentCode="myDepartmentCode"
        :missionId="missionId"
      />

      <view style="height: 180rpx"></view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.header {
  margin-top: 180rpx;
}
.body {
  padding: 24rpx 0;
}
.title {
  font-size: 32rpx;
  color: #7e808a;

  text-align: center;
  font-style: normal;
  text-transform: none;
  display: flex;
  text-align: center;
  padding-top: 50rpx;
  justify-content: space-evenly;

  .checked {
    font-weight: bold;
    font-size: 32rpx;
    color: #577f49;
  }

  .green {
    width: 25rpx;
    height: 0rpx;
    background: #ffffff;
    border-bottom: 6rpx solid #577f49;
    border-radius: 6rpx;
    margin: 10rpx auto;
  }
}
</style>
