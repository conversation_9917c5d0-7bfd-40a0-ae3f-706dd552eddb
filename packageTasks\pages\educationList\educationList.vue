<script setup>
import { ref } from "vue"
import { onLoad, onPullDownRefresh, onReachBottom } from "@dcloudio/uni-app"
import peopleContent from "./components/peopleContent.vue"
import dataContent from "./components/dataContent.vue"

const missionId = ref(0)
const peopleContentRef = ref(null)
const dataContentRef = ref(null)

const tabs = ref([
  {
    title: "人员名单",
    show: true,
  },
  {
    title: "数据汇总",
    show: true,
  },
])

const currentIndex = ref(0)

const changeTab = (index) => {
  currentIndex.value = index
}

onLoad((options) => {
  missionId.value = options.missionId
})

// 下拉刷新
onPullDownRefresh(() => {
  if (currentIndex.value === 0 && peopleContentRef.value) {
    peopleContentRef.value
      .refresh()
      .then(() => {
        uni.stopPullDownRefresh()
      })
      .catch(() => {
        uni.stopPullDownRefresh()
      })
  } else if (currentIndex.value === 1 && dataContentRef.value) {
    dataContentRef.value
      .refresh()
      .then(() => {
        uni.stopPullDownRefresh()
      })
      .catch(() => {
        uni.stopPullDownRefresh()
      })
  } else {
    uni.stopPullDownRefresh()
  }
})

// 上拉加载更多
onReachBottom(() => {
  if (currentIndex.value === 0 && peopleContentRef.value) {
    peopleContentRef.value.loadPage()
  }
})
</script>

<template>
  <navHeader title="教育名单" bg="/static/image/bg.png" showLeft></navHeader>
  <view class="page-education-list">
    <view class="header">
      <view class="title">
        <template v-for="(tab, index) in tabs" :key="index">
          <view
            v-if="tab.show"
            :class="currentIndex == index ? 'checked' : ''"
            @click="changeTab(index)"
          >
            <view class="">
              {{ tab.title }}
            </view>
            <view class="green" v-if="currentIndex == index"> </view>
          </view>
        </template>
      </view>
    </view>

    <view class="content">
      <peopleContent
        v-if="currentIndex == 0"
        ref="peopleContentRef"
        :missionId="missionId"
      />
      <dataContent v-if="currentIndex == 1" ref="dataContentRef" :missionId="missionId" />
    </view>
  </view>
</template>

<style lang="scss" scoped>
.page-education-list {
  padding: 24rpx;
  padding-top: 220rpx;

  .title {
    font-size: 32rpx;
    color: #7e808a;

    text-align: center;
    font-style: normal;
    text-transform: none;
    display: flex;
    text-align: center;
    padding-top: 50rpx;
    justify-content: space-evenly;

    .checked {
      font-weight: bold;
      font-size: 32rpx;
      color: #577f49;
    }

    .green {
      width: 25rpx;
      height: 0rpx;
      background: #ffffff;
      border-bottom: 6rpx solid #577f49;
      border-radius: 6rpx;
      margin: 10rpx auto;
    }
  }
}

.content {
  margin-top: 20rpx;
}
</style>
