<script setup>
import { ref } from "vue"
import { onLoad, onShow } from "@dcloudio/uni-app"
import request from "@/utils/request"
import dayjs from "dayjs"
import { navTo } from "@/utils/utils"

const examList = ref([])

const loadEnabledExam = async () => {
  const res = await request({
    url: `/mini/mission`,
    method: "GET",
  })
  if (res.code === 200) {
    examList.value = res.result?.data || []
  }

  // examList.value = [
  //   {
  //     id: 1,
  //     title: "这里是用户收到的任务名称可以是一行也可以是两行",
  //     status: 1,
  //   },
  //   {
  //     id: 2,
  //     title: "2024年春季任务",
  //     status: 2,
  //   },
  // ]
}

const goExam = (item) => {
  navTo(`/packageTasks/pages/index/links`, {
    id: item.id,
  })
}

onShow(() => {
  loadEnabledExam()
})
</script>

<template>
  <view>
    <navHeader
      bg="/static/image/tasks-bg.png"
      :imgHeight="374"
      :showLeft="true"
      color="#fff"
    ></navHeader>
    <view class="body">
      <view class="exam-list" v-if="examList.length > 0">
        <view
          class="exam-item"
          v-for="item in examList"
          :key="item.id"
          @click="goExam(item)"
        >
          <view class="exam-info">
            <view class="title">{{ item.name }}</view>
            <!-- <view class="time">
              <image
                src="/static/image/time.png"
                mode="widthFix"
                style="width: 24rpx; height: 24rpx"
              />
              todo - todo
            </view> -->
          </view>
          <button class="btn" :class="item.status === 'finished' ? 'disabled' : ''">
            {{ item.status === 'running' ? "进行中" : "已完成" }}
          </button>
        </view>

        <up-loadmore status="nomore" />
      </view>
      <empty v-else> 当前无任务</empty>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.body {
  width: 750rpx;
  background: rgba(247, 248, 250, 0.65);
  border-radius: 44rpx 44rpx 0rpx 0rpx;
  border: 1rpx solid #fff;
  backdrop-filter: blur(10rpx);
  min-height: calc(100vh - 430rpx - 43rpx);
  margin-top: 331rpx;
  padding: 35rpx 24rpx;
}

.exam-list {
  display: flex;
  flex-direction: column;

  .exam-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: rgba(255, 255, 255, 0.94);
    box-shadow: 0rpx 6rpx 13rpx 1rpx rgba(0, 0, 0, 0.04);
    border-radius: 20rpx;
    border: 2rpx solid #ffffff;
    padding: 33rpx 26rpx 32rpx 49rpx;
    margin-bottom: 20rpx;
    position: relative;
    gap: 26rpx;

    .exam-info {
      flex: 1;

      .title {
        -webkit-line-clamp: 2;
        line-clamp: 2;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
        font-size: 32rpx;
        color: #333;
      }

      .time {
        flex-shrink: 0;
        font-size: 24rpx;
        color: #999999;
        margin-top: 18rpx;
        display: flex;
        align-items: center;
        gap: 10rpx;
      }
    }

    .btn {
      width: 120rpx;
      height: 60rpx;
      line-height: 60rpx;
      background: #577f49;
      color: #fff;
      font-weight: 500;
      font-size: 26rpx;
      border-radius: 31rpx;

      &.disabled {
        background: #f7f8fa;
        color: #9ca3b5;
      }
    }
  }
}
</style>
