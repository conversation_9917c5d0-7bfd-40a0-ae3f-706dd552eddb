<script setup>
import { ref } from "vue"
import { onLoad, onShow } from "@dcloudio/uni-app"
import request from "@/utils/request"

const missionId = ref("")

// 响应式数据
const taskInfo = ref({
  name: "",
  config: {
    intention_count_end: "",
  },
})

const navigateTo = (path) => {
  uni.navigateTo({
    url: path + `?missionId=${missionId.value}`,
  })
}

// 获取任务详情
const getMissionDetail = async () => {
  try {
    const res = await request({
      url: `/mini/mission/${missionId.value}`,
      method: "GET",
    })

    if (res.code === 200) {
      taskInfo.value = res.result
    }
  } catch (error) {
    console.error("获取任务详情失败:", error)
  }
}

onLoad((options) => {
  console.log(options)
  if (options.id) {
    missionId.value = options.id
  }
})

onShow(() => {
  getMissionDetail()
})
</script>

<template>
  <view class="page">
    <navHeader title="ZB任务" bg="/static/image/bg.png"></navHeader>

    <view class="body">
      <view class="title">{{ taskInfo.name }}</view>
      <!-- <view class="time">
        <image
          src="/static/image/time.png"
          mode="widthFix"
          style="width: 24rpx; height: 24rpx"
        />
        <text>todo</text>
        -
        <text>todo</text>
      </view> -->

      <view class="task-list">
        <view
          class="task-item"
          @click="
            navigateTo('/packageTasks/pages/taskNumber/taskNumber')
          "
        >
          <view class="task-item-title">
            <text>任务数</text>
          </view>
          <image
            src="/static/image/right.svg"
            mode="widthFix"
            class="right-arrow"
          />
        </view>
        <view
          class="task-item"
          @click="navigateTo('/packageTasks/pages/data/data')"
        >
          <view class="task-item-title">
            <text>人员数据汇总</text>
          </view>
          <image
            src="/static/image/right.svg"
            mode="widthFix"
            class="right-arrow"
          />
        </view>
        <view
          class="task-item"
          @click="
            navigateTo(
              '/packageTasks/pages/intentionStatistics/intentionStatistics'
            )
          "
        >
          <view class="task-item-title">
            <text>意向统计</text>
            <!-- <text class="task-item-sub-title">
              截止至{{ taskInfo.config?.intention_count_end }}
            </text> -->
          </view>
          <image
            src="/static/image/right.svg"
            mode="widthFix"
            class="right-arrow"
          />
        </view>
        <view
          class="task-item"
          @click="
            navigateTo(
              '/packageTasks/pages/promotionComparison/promotionComparison'
            )
          "
        >
          <view class="task-item-title">
            <text>宣传比对</text>
          </view>
          <image
            src="/static/image/right.svg"
            mode="widthFix"
            class="right-arrow"
          />
        </view>
        <view
          class="task-item"
          @click="
            navigateTo(
              '/packageTasks/pages/physicalExamination/physicalExamination'
            )
          "
        >
          <view class="task-item-title">
            <text>体格检查</text>
          </view>
          <image
            src="/static/image/right.svg"
            mode="widthFix"
            class="right-arrow"
          />
        </view>
        <view
          class="task-item"
          @click="
            navigateTo(
              '/packageTasks/pages/politicalExaminationResults/politicalExaminationResults'
            )
          "
        >
          <view class="task-item-title">
            <text>政考名单</text>
          </view>
          <image
            src="/static/image/right.svg"
            mode="widthFix"
            class="right-arrow"
          />
        </view>
        <view
          class="task-item"
          @click="navigateTo('/packageTasks/pages/educationList/educationList')"
        >
          <view class="task-item-title">
            <text>教育名单</text>
          </view>
          <image
            src="/static/image/right.svg"
            mode="widthFix"
            class="right-arrow"
          />
        </view>
        <view
          class="task-item"
          @click="
            navigateTo(
              '/packageTasks/pages/numberOfCompleted/numberOfCompleted'
            )
          "
        >
          <view class="task-item-title">
            <text>完成数</text>
          </view>
          <image
            src="/static/image/right.svg"
            mode="widthFix"
            class="right-arrow"
          />
        </view>
        <view
          class="task-item"
          @click="
            navigateTo('/packageTasks/pages/reservePersonnel/reservePersonnel')
          "
        >
          <view class="task-item-title">
            <text>预储人员</text>
          </view>
          <image
            src="/static/image/right.svg"
            mode="widthFix"
            class="right-arrow"
          />
        </view>
        
        <!-- <view
          class="task-item"
          @click="
            navigateTo('/packageTasks/pages/returnPersonnel/returnPersonnel')
          "
        >
          <view class="task-item-title">
            <text>退兵人员</text>
          </view>
          <image
            src="/static/image/right.svg"
            mode="widthFix"
            class="right-arrow"
          />
        </view> -->
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.page {
  padding: 24rpx;
  padding-top: 220rpx;
}

.title {
  -webkit-line-clamp: 2;
  line-clamp: 2;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 42rpx;
  color: #000000;
  text-align: center;
  padding: 0 20rpx;
}

.time {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 24rpx;
  font-size: 26rpx;
  color: #bdc4ce;
  gap: 10rpx;
}

.task-list {
  background: rgba(255, 255, 255, 0.94);
  box-shadow: 0rpx 6rpx 13rpx 0rpx rgba(0, 0, 0, 0.04);
  border-radius: 20rpx 20rpx 20rpx 20rpx;
  border: 2rpx solid #ffffff;
  margin-top: 48rpx;

  .task-item {
    height: 100rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-left: 49rpx;
    padding-right: 20rpx;
    border-bottom: 1rpx solid rgba(112, 112, 112, 0.25);

    .task-item-title {
      font-size: 32rpx;
      color: #333333;
    }

    .task-item-sub-title {
      font-size: 20rpx;
      color: #ea2b2b;
      margin-left: 10rpx;
    }

    .right-arrow {
      width: 48rpx;
      height: 48rpx;
    }

    &:last-child {
      border-bottom: none;
    }
  }
}
</style>
