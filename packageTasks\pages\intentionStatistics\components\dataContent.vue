<script setup>
import { ref, computed, watch } from "vue"
import { getStatisticsMissionIntentionList } from "@/packageTasks/utils/api/missionPerson"
import matchingFilter from "@/packageTasks/components/matchingFilter.vue"

const props = defineProps({
  missionId: {
    type: [String, Number],
    default: "",
  },
})

const columns = ref([
  { title: "", type: "expand", width: "50rpx" },
  {
    title: "单位",
    key: "department_name",
    width: "220rpx",
  },
  {
    title: "总人数",
    key: "person_count",
    width: "120rpx",
  },
  {
    title: "有意向",
    key: "intention_yes_count",
    width: "120rpx",
  },
  {
    title: "无意向",
    key: "intention_no_count",
    width: "120rpx",
  },
])

const data = ref([])
const loading = ref(false)

// 数据处理 - 扁平化并添加children
const tableData = computed(() => {
  if (!data.value || data.value.length === 0) return []
  
  // 应用筛选条件
  let filteredData = data.value
  if (filterValues.value.name && filterValues.value.name.length > 0) {
    // 根据学业情况筛选逻辑（可根据实际需求调整）
    // filteredData = filteredData.filter(item => ...)
  }
  if (filterValues.value.education && filterValues.value.education.length > 0) {
    // 根据文化程度筛选逻辑（可根据实际需求调整）
    // filteredData = filteredData.filter(item => ...)
  }
  if (filterValues.value.degree && filterValues.value.degree.length > 0) {
    // 根据学位筛选逻辑（可根据实际需求调整）
    // filteredData = filteredData.filter(item => ...)
  }
  
  return filteredData.map(item => {
    const children = []
    
    // 添加"其中：高校人员"行
    if (item.school && (item.school.person_count > 0 || item.school.intention_yes_count > 0 || item.school.intention_no_count > 0)) {
      children.push({
        department_name: "其中：高校人员",
        code: `${item.code}_school`,
        person_count: item.school.person_count,
        intention_yes_count: item.school.intention_yes_count,
        intention_no_count: item.school.intention_no_count
      })
    }
    
    // 添加"其中：社会适龄青年"行
    if (item.social && (item.social.person_count > 0 || item.social.intention_yes_count > 0 || item.social.intention_no_count > 0)) {
      children.push({
        department_name: "其中：社会适龄青年",
        code: `${item.code}_social`,
        person_count: item.social.person_count,
        intention_yes_count: item.social.intention_yes_count,
        intention_no_count: item.social.intention_no_count
      })
    }
    
    return {
      ...item,
      // 扁平化主数据的total字段
      department_name: item.name,
      person_count: item.total.person_count,
      intention_yes_count: item.total.intention_yes_count,
      intention_no_count: item.total.intention_no_count,
      children: children.length > 0 ? children : undefined
    }
  })
})

// 加载数据
const loadData = async () => {
  if (!props.missionId) return
  
  loading.value = true
  try {
    const params = {
      mission_id: props.missionId,
      // 不分页，获取所有数据
      page: 1,
      per_page: 10000
    }
    
    const response = await getStatisticsMissionIntentionList(params)
    
    if (response.code === 200) {
      data.value = response.result?.data || []
      
      // 计算统计数据
      calculateStatistics()
    } else {
      throw new Error(response.message || "加载失败")
    }
  } catch (error) {
    console.error("加载数据失败:", error)
    uni.showToast({
      title: error.message || "加载失败",
      icon: "none",
    })
  } finally {
    loading.value = false
  }
}

// 计算统计数据
const calculateStatistics = () => {
  if (!data.value || data.value.length === 0) return
  
  const total = data.value.reduce((sum, item) => {
    return {
      person_count: sum.person_count + (item.total?.person_count || 0),
      intention_yes_count: sum.intention_yes_count + (item.total?.intention_yes_count || 0),
      intention_no_count: sum.intention_no_count + (item.total?.intention_no_count || 0),
    }
  }, { person_count: 0, intention_yes_count: 0, intention_no_count: 0 })
  
  // 这里可以更新统计盒子的数据，但按要求先不动
  console.log("统计数据:", total)
}

// 监听missionId变化
watch(
  () => props.missionId,
  (newVal) => {
    if (newVal) {
      loadData()
    }
  },
  { immediate: true }
)

// 刷新数据
const refresh = () => {
  loadData()
}

// 导出方法供父组件使用
defineExpose({
  refresh,
  loadData,
})

const onExpandChange = (row) => {
  console.log(row)
}

const filterVisible = ref(false)

const openFilter = () => {
  filterVisible.value = true
}

const closeFilter = () => {
  filterVisible.value = false
}

const filterOptions = ref([
  {
    title: "学业情况",
    key: "name",
    options: ["在校生", "应届毕业生", "往届毕业生"],
    value: [1, 2, 3],
  },
  {
    title: "文化程度",
    key: "education",
    options: ["专科", "本科", "研究生"],
    value: [1, 2, 3],
  },
  {
    title: "取得学位",
    key: "degree",
    options: ["学士", "硕士", "博士"],
    value: [0, 1, 2, 3],
  },
])

const filterValues = ref({
  name: [],
  education: [],
  degree: [],
})

const filterActive = computed(() => {
  return (filterValues.value.name && filterValues.value.name.length > 0) ||
         (filterValues.value.education && filterValues.value.education.length > 0) ||
         (filterValues.value.degree && filterValues.value.degree.length > 0)
})

const onFilterChange = (value) => {
  console.log("onFilterChange", value)
  filterValues.value = { ...value }
  filterVisible.value = false
}

const confirm = () => {
  console.log(keyword.value)
}
</script>

<template>
  <view class="data-content">
    <view class="box-list">
      <view class="box">
        <view class="title"> 总体联系进度 </view>
        <view class="content">
          <view class="item"> 已联系：1500 人 </view>
          <view class="item"> 未联系：1000人 </view>
        </view>
      </view>
      <view class="box">
        <view class="title"> 意向统计 </view>
        <view class="content">
          <view class="item"> 有意向：500 人 </view>
          <view class="item"> 无意向：700人 </view>
        </view>
      </view>
    </view>

    <view class="filter-bar">
      <view
        class="filter-btn"
        :class="{ active: filterActive }"
        @click="openFilter"
      >
        <text>筛选</text>
        <image
          :src="
            filterActive
              ? '/static/image/filter-active.png'
              : '/static/image/filter.png'
          "
          mode=""
          class="filter-icon"
        ></image>
      </view>
    </view>

    <view v-if="loading" class="loading">加载中...</view>
    <up-table2
      v-else
      :columns="columns"
      :data="tableData"
      :tree-props="{ children: 'children' }"
      :expand-row-keys="[]"
      @expand-change="onExpandChange"
    />
  </view>

  <up-popup
    closeable
    :show="filterVisible"
    @close="closeFilter"
    :round="10"
    mode="bottom"
  >
    <matchingFilter
      :options="filterOptions"
      v-model="filterValues"
      @change="onFilterChange"
    />
  </up-popup>
</template>

<style lang="scss" scoped>
.data-content {
  .box-list {
    display: flex;
    flex-direction: column;
    gap: 20rpx;
    margin-bottom: 40rpx;

    .box {
      width: 100%;
      background: rgba(255, 255, 255, 0.94);
      box-shadow: 0rpx 6rpx 13rpx 0rpx rgba(0, 0, 0, 0.04);
      border-radius: 20rpx 20rpx 20rpx 20rpx;
      border: 2rpx solid #ffffff;
      padding: 44rpx 48rpx;

      .title {
        font-size: 32rpx;
        color: #333333;
        margin-bottom: 20rpx;
      }

      .content {
        display: flex;
        gap: 84rpx;

        .item {
          font-size: 26rpx;
          color: #8c9198;
        }
      }
    }
  }

  .filter-bar {
    margin-bottom: 35rpx;
    display: flex;
    justify-content: flex-end;

    .filter-btn {
      width: 160rpx;
      height: 80rpx;
      padding: 0 28rpx;
      background: #ffffff;
      box-shadow: 0rpx 6rpx 13rpx 1rpx rgba(0, 0, 0, 0.04);
      border-radius: 40rpx 40rpx 40rpx 40rpx;
      display: flex;
      justify-content: center;
      align-items: center;

      text {
        font-size: 26rpx;
        color: #21232c;
      }

      .filter-icon {
        width: 28rpx;
        height: 28rpx;
        margin-left: 10rpx;
      }
    }
  }

  .loading {
    text-align: center;
    padding: 40rpx;
    color: #999;
  }
}
</style>
