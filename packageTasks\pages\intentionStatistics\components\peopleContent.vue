<script setup>
import { ref, computed, watch, onMounted } from "vue"
import matchingFilter from "@/packageTasks/components/matchingFilter.vue"
import personnelItem from "@/packageTasks/components/personnelItem.vue"
import filterBar from "@/packageTasks/components/filterBar.vue"
import { usePagination } from "@/utils/hooks"
import {
  personEducationMap,
  personStudyFormMap,
  personGraduateMap,
  personPriorityMap,
  personIntentionMap,
  personPreTypeMap,
  politicalOutlookArr,
} from "@/packageTasks/utils/consts"
import {
  getMissionPersonList,
  createQueryBuilder,
} from "@/packageTasks/utils/api/missionPerson"

const props = defineProps({
  missionId: {
    type: [String, Number],
    default: 0,
  },
})

const list = ref([])
const keyword = ref("")
const filterVisible = ref(false)
const type = ref("school") // school=高校人员(1), social=社会适龄青年(2)

const openFilter = () => {
  filterVisible.value = true
}

const closeFilter = () => {
  filterVisible.value = false
}

const filterOptions = ref([
  // 年龄 政治面貌 文化程度 学业情况 取得学位 学习类型 是否有高级以上职业技能等级证书(职业资格证书) 是否联系 是否有意向 是否预储人员 优先条件
  {
    title: "年龄",
    key: "age",
    options: ["18", "19", "20"],
    value: [18, 19, 20],
  },
  {
    title: "政治面貌",
    key: "politics",
    options: politicalOutlookArr,
    value: politicalOutlookArr.map((item) => item),
  },
  {
    title: "学业情况",
    key: "graduate",
    options: Object.values(personGraduateMap).map((item) => item.text),
    value: Object.values(personGraduateMap).map((item) => item.status),
  },
  {
    title: "学习类型",
    key: "study_form",
    options: Object.values(personStudyFormMap).map((item) => item.text),
    value: Object.values(personStudyFormMap).map((item) => item.status),
  },
  {
    title: "文化程度",
    key: "education",
    options: Object.values(personEducationMap).map((item) => item.text),
    value: Object.values(personEducationMap).map((item) => item.status),
  },
  {
    title: "是否有高级以上职业技能等级证书(职业资格证书)",
    key: "skillCertificate",
    options: ["有", "没有"],
    value: [1, 0],
  },
  {
    title: "取得学位",
    key: "degree",
    options: ["学士", "硕士", "博士"],
    value: ["学士", "硕士", "博士"],
  },
  {
    title: "是否联系",
    key: "isContact",
    options: ["是", "否"],
    value: [1, 0],
  },
  {
    title: "是否有意向",
    key: "intention",
    options: ["是", "否"],
    value: [1, 0],
  },
  {
    title: "是否预储人员",
    key: "isPreStorage",
    options: ["是", "否"],
    value: [1, 0],
  },
  {
    title: "优先条件",
    key: "priority",
    options: Object.values(personPriorityMap).map((item) => item.text),
    value: Object.values(personPriorityMap).map((item) => item.status),
  },
])

const filterValues = ref({
  age: [],
  politics: [],
  graduate: [],
  study_form: [],
  education: [],
  skillCertificate: [],
  degree: [],
  isContact: [],
  intention: [],
  isPreStorage: [],
  priority: [],
})

const filterActive = computed(() => {
  return Object.keys(filterValues.value).length > 0
})

// API调用逻辑
const loadData = (options = {}) => {
  return new Promise((resolve, reject) => {
    // 使用查询构建器构建参数
    const queryBuilder = createQueryBuilder(props.missionId)
      .type(type.value)
      .page(options.page || 1)

    // 添加搜索关键词
    if (keyword.value.trim()) {
      queryBuilder.keyword(keyword.value.trim())
    }

    // 添加筛选条件
    Object.keys(filterValues.value).forEach((key) => {
      if (filterValues.value[key] && filterValues.value[key].length > 0) {
        // 根据不同筛选条件转换参数
        switch (key) {
          case "isPreStorage":
            const value = filterValues.value[key]
            const preType = []
            if (value.includes(1)) {
              preType.push(
                personPreTypeMap[1]?.status,
                personPreTypeMap[2]?.status
              )
            }
            if (value.includes(0)) {
              preType.push(personPreTypeMap[-1]?.status)
            }
            queryBuilder.preType(preType.join(","))
            break
          default:
            queryBuilder.params[key] = filterValues.value[key].join(",")
            break
        }
      }
    })

    const params = queryBuilder.build()

    // 调试信息
    console.log("搜索参数:", params)
    console.log("关键词:", keyword.value)
    console.log("筛选条件:", filterValues.value)

    getMissionPersonList(params)
      .then((res) => {
        console.log("API响应:", res)
        if (res.code === 200) {
          list.value.push(...(res.result?.data || []))
          resolve(res)
        } else {
          reject(res)
        }
      })
      .catch(reject)
  })
}

const { page, loadPage, refresh, loadMoreStatus } = usePagination({
  loadData,
  list,
})

const onFilterChange = (value) => {
  filterValues.value = value
  filterVisible.value = false
  refresh() // 应用筛选后刷新列表
}

const confirm = () => {
  refresh() // 搜索后刷新列表
}

const handleItemClick = (item, currentType) => {
  uni.navigateTo({
    url: `/packageTasks/pages/intentionStatistics/personDetail?id=${item.id}&type=${currentType}`,
  })
}

const callPhone = (phone, id) => {
  uni.makePhoneCall({
    phoneNumber: phone,
  })
}

// 监听类型变化，重新加载数据
watch(type, () => {
  refresh()
})

// 向父组件暴露方法
defineExpose({
  refresh,
  loadPage,
})

// 组件挂载时加载数据
onMounted(() => {
  if (props.missionId) {
    loadPage()
  }
})
</script>

<template>
  <view class="people-content">
    <view class="type-radio-group">
      <customRadioGroup v-model="type" placement="row">
        <customRadioItem value="school" label="高校人员" size="small" />
        <customRadioItem value="social" label="社会适龄青年" size="small" />
      </customRadioGroup>
    </view>

    <filterBar
      v-model:keyword="keyword"
      :filterActive="filterActive"
      placeholder="输入姓名或身份证号码搜索"
      @openFilter="openFilter"
      @closeFilter="closeFilter"
      @confirm="confirm"
    />

    <view class="list">
      <personnelItem
        v-for="item in list"
        :key="item.id"
        :item="item"
        @click="handleItemClick(item, type)"
      >
        <template #extra-info>
          <text style="color: #ea2b2b" v-if="item.intention === -1">
            无意向
          </text>
          <text style="color: #577f49" v-if="item.intention === 1">
            有意向
          </text>
          <text
            style="color: #999"
            v-if="item.intention === null || item.intention === undefined"
          >
            未联系
          </text>
        </template>
        <template #right>
          <image
            v-if="item.intention !== null && item.intention !== undefined"
            src="/static/image/call-gray.svg"
            mode=""
            style="width: 56rpx; height: 56rpx"
          ></image>
          <image
            v-else
            src="/static/image/call.svg"
            mode=""
            style="width: 56rpx; height: 56rpx"
            @click.stop="callPhone(item.phone, item.id)"
          ></image>
        </template>
      </personnelItem>

      <!-- 空状态 -->
      <empty
        v-if="list.length === 0 && loadMoreStatus === 'nomore'"
        theme="2"
        text="暂无数据"
      />

      <!-- 加载更多状态 -->
      <up-loadmore v-if="list.length" :status="loadMoreStatus" />
    </view>
  </view>

  <up-popup
    closeable
    :show="filterVisible"
    @close="closeFilter"
    :round="10"
    mode="bottom"
  >
    <matchingFilter
      :options="filterOptions"
      v-model="filterValues"
      @change="onFilterChange"
    />
  </up-popup>
</template>

<style lang="scss" scoped>
.people-content {
  .type-radio-group {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 24rpx;
  }
}
</style>
