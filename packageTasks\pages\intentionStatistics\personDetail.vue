<script setup>
import { ref, computed } from "vue"
import { onLoad } from "@dcloudio/uni-app"
import personBaseInfo from "@/packageTasks/components/personBaseInfo.vue"
import {
  personIsEngineeringMap,
  personIntentionMap,
  personEducationMap,
} from "@/packageTasks/utils/consts"
import {
  updateMissionPersonDetail,
  getMissionPersonDetail,
} from "@/packageTasks/utils/api/missionPerson"

const type = ref("school")
const id = ref("")

const peopleDetail = ref({})

const baseInfoExpand = ref(false)

const formData = ref({
  intention: "",
  is_engineering: "",
  priority: "",
  education: "",
  graduation_year: "",
  remark: "",
})

const statisticsFieldList = computed(() => {
  let list = [
    {
      label: "是否有报名意向？",
      key: "intention",
      required: true,
      type: "radio",
      options: Object.values(personIntentionMap).map((item) => ({
        label: item.text,
        value: item.status,
      })),
    },
    {
      label: "是否理工类",
      key: "is_engineering",
      required: true,
      type: "radio",
      options: Object.values(personIsEngineeringMap).map((item) => ({
        label: item.text,
        value: item.status,
      })),
    },
    {
      label: "优先条件",
      key: "priority",
      required: true,
      type: "radio",
      options: [
        {
          label: "1类",
          value: "1",
        },
        {
          label: "2类",
          value: "2",
        },
        {
          label: "3类",
          value: "3",
        },
        {
          label: "4类",
          value: "4",
        },
        {
          label: "5类",
          value: "5",
        },
        {
          label: "其他",
          value: "other",
        },
      ],
    },
  ]

  if (type.value === "social") {
    // 优先条件后添加 学历层次、毕业年份
    list.push({
      label: "学历层次",
      key: "education",
      required: true,
      type: "radio",
      options: Object.values(personEducationMap).map((item) => ({
        label: item.text,
        value: item.status,
      })),
    })

    list.push({
      label: "毕业年份",
      key: "graduation_year",
      required: true,
      type: "radio",
      options: [
        {
          label: "2021",
          value: "2021",
        },
        {
          label: "2022",
          value: "2022",
        },
        {
          label: "2023",
          value: "2023",
        },
        {
          label: "2024",
          value: "2024",
        },
        {
          label: "2025",
          value: "2025",
        },
      ],
    })
  }

  list.push({
    label: "备注",
    key: "remark",
    type: "textarea",
  })
  return list
})

const submit = async () => {
  try {
    // 数据验证
    const requiredFields = statisticsFieldList.value.filter(
      (field) => field.required
    )
    for (const field of requiredFields) {
      console.log(formData.value[field.key])
      if (!formData.value[field.key] && formData.value[field.key] !== 0) {
        uni.showToast({
          title: `请填写${field.label}`,
          icon: "none",
        })
        return
      }
    }

    // 构建请求数据，映射前端字段到数据库字段
    const requestData = {
      intention: parseInt(formData.value.intention), // 意向状态
      is_engineering: parseInt(formData.value.is_engineering), // 是否理工类
      priority:
        formData.value.priority === "other"
          ? null
          : parseInt(formData.value.priority), // 优先级
      intention_remark: formData.value.remark, // 意向备注
      intention_at: new Date().toISOString(), // 意向统计时间
    }

    // 如果是社会人员，添加学历和毕业年份
    if (type.value === "social") {
      if (formData.value.education) {
        // 映射学历层次到数字
        const educationMap = {
          unknown: 0,
          college: 1,
          bachelor: 2,
          bachelor_plus: 3,
        }
        requestData.education = educationMap[formData.value.education]
      }

      if (formData.value.graduation_year) {
        requestData.graduation_year = parseInt(formData.value.graduation_year)
      }
    }

    // 调用更新接口
    const response = await updateMissionPersonDetail(id.value, requestData)

    if (response.code === 200) {
      uni.showToast({
        title: "提交成功",
        icon: "success",
      })

      // 延迟返回上一页
      setTimeout(() => {
        uni.navigateBack()
      }, 1500)
    }
  } catch (error) {
    console.error("提交失败:", error)
    // request.js 中已经处理了错误提示，这里不需要额外处理
  }
}

// 获取人员详情
const getPersonDetail = async () => {
  try {
    if (!id.value) {
      uni.showToast({
        title: "缺少人员ID",
        icon: "none",
      })
      return
    }

    const response = await getMissionPersonDetail(id.value)

    if (response.code === 200 && response.result) {
      peopleDetail.value = response.result

      // 初始化表单数据（如果已有意向统计数据）
      if (
        response.result.intention !== undefined &&
        response.result.intention !== null &&
        response.result.intention !== -1
      ) {
        formData.value.intention = String(response.result.intention)
      }

      if (
        response.result.is_engineering !== undefined &&
        response.result.is_engineering !== null
      ) {
        formData.value.is_engineering = String(response.result.is_engineering)
      }

      if (response.result.priority) {
        formData.value.priority = String(response.result.priority)
      }

      if (
        response.result.education !== undefined &&
        response.result.education !== null
      ) {
        // 映射数字到学历层次
        const educationReverseMap = {
          0: "unknown",
          1: "college",
          2: "bachelor",
          3: "bachelor_plus",
        }
        formData.value.education =
          educationReverseMap[response.result.education] || ""
      }

      if (response.result.graduation_year) {
        formData.value.graduation_year = String(response.result.graduation_year)
      }

      if (response.result.intention_remark) {
        formData.value.remark = response.result.intention_remark
      }
    }
  } catch (error) {
    console.error("获取人员详情失败:", error)
  }
}

onLoad((options) => {
  console.log(options)
  type.value = options.type
  id.value = options.id

  // 获取人员详情
  if (options.id) {
    getPersonDetail()
  }
})
</script>

<template>
  <navHeader title="意向统计" bg="/static/image/bg.png"></navHeader>
  <view class="people-detail">
    <personBaseInfo
      v-model:baseInfoExpand="baseInfoExpand"
      :peopleDetail="peopleDetail"
    />

    <view class="intention-info">
      <view class="intention-info-title"> 意向统计 </view>
      <view class="intention-info-content">
        <view
          class="intention-info-content-item"
          v-for="(field, index) in statisticsFieldList"
          :key="field.key"
        >
          <view
            class="intention-info-content-item-title"
            :class="{ required: field.required }"
          >
            {{ index + 1 }}.{{ field.label }}
          </view>
          <view class="intention-info-content-item-value">
            <template v-if="field.type === 'radio'">
              <customRadioGroup
                v-model="formData[field.key]"
                placement="column"
              >
                <customRadioItem
                  v-for="option in field.options"
                  :key="option.value"
                  :value="option.value"
                  :label="option.label"
                ></customRadioItem>
              </customRadioGroup>
            </template>
            <template v-else-if="field.type === 'textarea'">
              <up-textarea
                v-model="formData[field.key]"
                placeholder="请输入备注"
              />
            </template>
          </view>
        </view>
      </view>
    </view>

    <view class="btn-wrapper">
      <button class="submit" @click="submit">提交</button>
    </view>
  </view>
</template>

<style>
page {
  background-color: #ffffff;
}
</style>

<style lang="scss" scoped>
.people-detail {
  display: flex;
  flex-direction: column;
  padding: 220rpx 45rpx 0;

  .intention-info {
    margin-top: 64rpx;

    .intention-info-title {
      font-size: 32rpx;
      color: #111111;
    }

    .intention-info-content {
      margin-top: 40rpx;

      .intention-info-content-item {
        background: #ffffff;
        box-shadow: 0rpx -4rpx 13rpx 0rpx rgba(0, 0, 0, 0.04),
          0rpx 4rpx 13rpx 0rpx rgba(0, 0, 0, 0.04);
        border-radius: 20rpx 20rpx 20rpx 20rpx;
        margin-bottom: 23rpx;

        .intention-info-content-item-title {
          font-size: 32rpx;
          color: #111111;
          border-bottom: 1rpx solid #e5e5e5;
          padding: 25rpx 30rpx;

          &.required {
            &::before {
              content: "*";
              color: #ff0000;
              margin-right: 4rpx;
            }
          }
        }

        .intention-info-content-item-value {
          display: flex;
          align-items: center;
          gap: 20rpx;
          padding: 50rpx 60rpx;
        }
      }
    }
  }

  .btn-wrapper {
    display: flex;
    justify-content: center;
    margin-top: 50rpx;
    margin-bottom: 40rpx;
    .submit {
      width: 702rpx;
      height: 100rpx;
      line-height: 100rpx;
      font-size: 34rpx;
      color: #ffffff;
      background: #577f49;
      box-shadow: 0rpx 20rpx 48rpx 1rpx rgba(87, 127, 73, 0.2);
      border-radius: 50rpx 50rpx 50rpx 50rpx;
    }
  }
}
</style>
