<script setup>
import { ref, watch } from "vue"
import { onLoad, onPullDownRefresh } from "@dcloudio/uni-app"
import { getStatisticsMissionGoList } from "@/packageTasks/utils/api/missionPerson"

const missionId = ref(0)

const columns = ref([
  {
    key: "name",
    title: "单位",
    width: "220rpx",
  },
  {
    key: "graduate_before",
    title: "在校生完成人数",
    width: "250rpx",
  },
  {
    key: "graduate",
    title: "毕业生完成人数",
    width: "250rpx",
  },
  {
    key: "person_count",
    title: "完成人数",
    width: "180rpx",
  },
  {
    key: "completion_rate",
    title: "完成率",
    width: "180rpx",
  },
])

const data = ref([])
const loading = ref(false)

// 加载数据
const loadData = async () => {
  if (!missionId.value) return Promise.resolve()
  
  loading.value = true
  try {
    const params = {
      mission_id: missionId.value,
      // 不分页，获取所有数据
      page: 1,
      per_page: 10000
    }
    
    const response = await getStatisticsMissionGoList(params)
    
    if (response.code === 200) {
      // 处理数据，直接使用后端字段名
      const mappedData = (response.result?.data || []).map(item => {
        const inSchoolCount = item.graduate_before || 0
        const graduateCount = item.graduate || 0
        const totalCount = item.person_count || 0
        
        // 计算完成率（暂时使用总人数作为参考，具体计算逻辑可能需要根据业务需求调整）
        let completionRate = 0
        const baseCount = inSchoolCount + graduateCount
        if (baseCount > 0) {
          completionRate = Math.round((totalCount / baseCount) * 100)
        } else if (totalCount > 0) {
          completionRate = 100
        }
        
        return {
          ...item,
          completion_rate: `${completionRate}%`
        }
      })
      data.value = mappedData
    } else {
      throw new Error(response.message || "加载失败")
    }
  } catch (error) {
    console.error("加载数据失败:", error)
    uni.showToast({
      title: error.message || "加载失败",
      icon: "none",
    })
  } finally {
    loading.value = false
  }
}

// 监听missionId变化
watch(
  () => missionId.value,
  (newVal) => {
    if (newVal) {
      loadData()
    }
  },
  { immediate: true }
)

// 刷新数据
const refresh = () => {
  return loadData()
}

onLoad((options) => {
  missionId.value = options.missionId || 0
})

// 下拉刷新
onPullDownRefresh(() => {
  refresh().then(() => {
    uni.stopPullDownRefresh()
  }).catch(() => {
    uni.stopPullDownRefresh()
  })
})

const cellStyleFunc = (scope) => {
  if (scope.column.key == "completion_rate") {
    return {
      color: "#577F49",
    }
  } else {
    return {}
  }
}
</script>

<template>
  <navHeader title="完成数" bg="/static/image/bg.png"></navHeader>

  <view class="page-number-of-completed">
    <view v-if="loading" class="loading">加载中...</view>
    <up-table2 v-else :columns="columns" :data="data" :cellStyle="cellStyleFunc" />
  </view>
</template>

<style lang="scss" scoped>
.page-number-of-completed {
  padding: 24rpx;
  padding-top: 220rpx;

  .loading {
    text-align: center;
    padding: 40rpx;
    color: #999;
  }
}
</style>
