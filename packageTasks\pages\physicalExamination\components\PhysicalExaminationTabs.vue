<template>
  <view class="content">
    <!-- 计划标签页 -->
    <template v-if="currentIndex === 0">
      <planList :list="physicalExamPlanData" />
    </template>
    
    <!-- 结果标签页 -->
    <template v-if="currentIndex === 1">
      <template v-if="dataView">
        <dataContent1
          :tableData="physicalCheckData"
          @departmentChange="onDepartmentChange"
          @keywordSearch="onKeywordSearch"
        />
      </template>
      <template v-else>
        <SummaryCard
          :summaryData="physicalCheckSummary"
          :loading="loading"
          :showPassRate="true"
          :showStationRate="true"
          :showDivider="true"
          @cardClick="onResultCardClick"
        />
        <peopleContent
          ref="peopleContentRef"
          type="physical_check"
          :missionId="missionId"
        />
      </template>
    </template>
    
    <!-- 复查标签页 -->
    <template v-if="currentIndex === 2">
      <template v-if="dataView">
        <dataContent23
          :tableData="physicalRecheckData"
          dataType="recheck"
          @departmentChange="onDepartmentChange"
          @keywordSearch="onKeywordSearch"
        />
      </template>
      <template v-else>
        <SummaryCard
          :summaryData="physicalRecheckSummary"
          :loading="loading"
          @cardClick="onRecheckCardClick"
        />
        <peopleContent
          ref="peopleContentRef"
          type="physical_recheck"
          :missionId="missionId"
        />
      </template>
    </template>
    
    <!-- 抽查标签页 -->
    <template v-if="currentIndex === 3">
      <template v-if="dataView">
        <dataContent23
          :tableData="physicalSpotCheckData"
          dataType="spot_check"
          @departmentChange="onDepartmentChange"
          @keywordSearch="onKeywordSearch"
        />
      </template>
      <template v-else>
        <SummaryCard
          :summaryData="physicalSpotCheckSummary"
          :loading="loading"
          :showPassRate="true"
          @cardClick="onSpotCheckCardClick"
        />
        <peopleContent
          ref="peopleContentRef"
          type="physical_spot_check"
          :missionId="missionId"
        />
      </template>
    </template>
  </view>
</template>

<script setup>
import { ref, computed, watch } from "vue"
import request from "@/utils/request"
import planList from "./planList.vue"
import peopleContent from "./peopleContent.vue"
import dataContent1 from "./dataContent1.vue"
import dataContent23 from "./dataContent23.vue"
import SummaryCard from "./SummaryCard.vue"
import {
  getStatisticsMissionPhysicalCheckList,
  getStatisticsMissionPhysicalRecheckList,
  getStatisticsMissionPhysicalSpotCheckList,
} from "@/packageTasks/utils/api/missionPerson"

const props = defineProps({
  currentIndex: {
    type: Number,
    default: 0
  },
  missionId: {
    type: Number,
    required: true
  }
})

const peopleContentRef = ref(null)
const dataView = ref(false)

// 数据状态
const physicalExamPlanData = ref([])
const physicalCheckData = ref([])
const physicalRecheckData = ref([])
const physicalSpotCheckData = ref([])
const loading = ref(false)

// 汇总数据状态
const physicalCheckTotalData = ref({})
const physicalRecheckTotalData = ref({})
const physicalSpotCheckTotalData = ref({})

// 筛选参数
const filterParams = ref({
  keyword: "",
  department_codes: [],
})

// 计算属性 - 体格检查汇总数据
const physicalCheckSummary = computed(() => {
  const data = physicalCheckTotalData.value
  const passed_count = data.passed_count || 0
  const task_num_actual = data.task_num_actual || 0
  const task_num_should = data.task_num_should || 0
  const un_passed_count = data.un_passed_count || 0
  const station_rate = data.station_rate || 0

  // 计算合格率
  const pass_rate =
    task_num_actual > 0 ? Math.round((passed_count / task_num_actual) * 100) : 0

  // 计算上站体检率（与子组件逻辑保持一致）
  const station_ratio = `1:${station_rate / 100}`

  return {
    pass_rate: `${pass_rate}%`,
    total_count: task_num_actual,
    passed_count,
    un_passed_count,
    task_num_should,
    station_ratio,
  }
})

// 计算属性 - 复查汇总数据
const physicalRecheckSummary = computed(() => {
  const data = physicalRecheckTotalData.value
  const passed_count = data.passed_count || 0
  const un_passed_count = data.un_passed_count || 0
  const total_count = passed_count + un_passed_count

  return {
    total_count,
    passed_count,
    un_passed_count,
  }
})

// 计算属性 - 抽查汇总数据
const physicalSpotCheckSummary = computed(() => {
  const data = physicalSpotCheckTotalData.value
  const passed_count = data.passed_count || 0
  const un_passed_count = data.un_passed_count || 0
  const total_count = passed_count + un_passed_count

  // 计算合格率
  const pass_rate =
    total_count > 0 ? Math.round((passed_count / total_count) * 100) : 0

  return {
    pass_rate: `${pass_rate}%`,
    total_count,
    passed_count,
    un_passed_count,
  }
})

// 加载体格检查计划数据
const loadPhysicalExamPlanData = async () => {
  if (!props.missionId) return Promise.resolve()

  loading.value = true
  try {
    const params = {
      mission_id: props.missionId,
      page: 1,
      per_page: 10000,
    }

    const response = await request({
      url: "/mini/mission_physical_examination",
      method: "GET",
      data: params,
    })

    if (response.code === 200) {
      physicalExamPlanData.value = response.result?.data || []
    } else {
      throw new Error(response.message || "加载失败")
    }
  } catch (error) {
    console.error("加载体格检查计划数据失败:", error)
    uni.showToast({
      title: error.message || "加载失败",
      icon: "none",
    })
  } finally {
    loading.value = false
  }
}

// 加载体格检查数据
const loadPhysicalCheckData = async () => {
  if (!props.missionId) return Promise.resolve()

  loading.value = true
  try {
    const params = {
      mission_id: props.missionId,
      page: 1,
      per_page: 10000,
    }

    // 添加筛选参数
    if (filterParams.value.keyword && filterParams.value.keyword.trim()) {
      params.keyword = filterParams.value.keyword.trim()
    }
    if (filterParams.value.department_codes.length > 0) {
      params.department_codes = filterParams.value.department_codes.join(",")
    }

    const response = await getStatisticsMissionPhysicalCheckList(params)

    if (response.code === 200) {
      physicalCheckData.value = response.result?.data || []
      physicalCheckTotalData.value = response.result?.total_data || {}
    } else {
      throw new Error(response.message || "加载失败")
    }
  } catch (error) {
    console.error("加载体格检查数据失败:", error)
    uni.showToast({
      title: error.message || "加载失败",
      icon: "none",
    })
  } finally {
    loading.value = false
  }
}

// 加载复查数据
const loadPhysicalRecheckData = async () => {
  if (!props.missionId) return Promise.resolve()

  loading.value = true
  try {
    const params = {
      mission_id: props.missionId,
      page: 1,
      per_page: 10000,
    }

    // 添加筛选参数
    if (filterParams.value.keyword && filterParams.value.keyword.trim()) {
      params.keyword = filterParams.value.keyword.trim()
    }
    if (filterParams.value.department_codes.length > 0) {
      params.department_codes = filterParams.value.department_codes.join(",")
    }

    const response = await getStatisticsMissionPhysicalRecheckList(params)

    if (response.code === 200) {
      physicalRecheckData.value = response.result?.data || []
      physicalRecheckTotalData.value = response.result?.total_data || {}
    } else {
      throw new Error(response.message || "加载失败")
    }
  } catch (error) {
    console.error("加载复查数据失败:", error)
    uni.showToast({
      title: error.message || "加载失败",
      icon: "none",
    })
  } finally {
    loading.value = false
  }
}

// 加载抽查数据
const loadPhysicalSpotCheckData = async () => {
  if (!props.missionId) return Promise.resolve()

  loading.value = true
  try {
    const params = {
      mission_id: props.missionId,
      page: 1,
      per_page: 10000,
    }

    // 添加筛选参数
    if (filterParams.value.keyword && filterParams.value.keyword.trim()) {
      params.keyword = filterParams.value.keyword.trim()
    }
    if (filterParams.value.department_codes.length > 0) {
      params.department_codes = filterParams.value.department_codes.join(",")
    }

    const response = await getStatisticsMissionPhysicalSpotCheckList(params)

    if (response.code === 200) {
      physicalSpotCheckData.value = response.result?.data || []
      physicalSpotCheckTotalData.value = response.result?.total_data || {}
    } else {
      throw new Error(response.message || "加载失败")
    }
  } catch (error) {
    console.error("加载抽查数据失败:", error)
    uni.showToast({
      title: error.message || "加载失败",
      icon: "none",
    })
  } finally {
    loading.value = false
  }
}

const onResultCardClick = () => {
  dataView.value = true
  loadPhysicalCheckData()
}

const onRecheckCardClick = () => {
  dataView.value = true
  loadPhysicalRecheckData()
}

const onSpotCheckCardClick = () => {
  dataView.value = true
  loadPhysicalSpotCheckData()
}

// 处理子组件的部门筛选变化
const onDepartmentChange = (departmentCodes) => {
  filterParams.value.department_codes = departmentCodes
  loadCurrentTabData()
}

// 处理关键词搜索
const onKeywordSearch = (keyword) => {
  filterParams.value.keyword = keyword
  loadCurrentTabData()
}

// 根据当前标签页加载对应数据
const loadCurrentTabData = () => {
  switch (props.currentIndex) {
    case 0:
      loadPhysicalExamPlanData()
      break
    case 1:
      loadPhysicalCheckData()
      break
    case 2:
      loadPhysicalRecheckData()
      break
    case 3:
      loadPhysicalSpotCheckData()
      break
  }
}

// 监听标签页变化
watch(() => props.currentIndex, (newIndex) => {
  dataView.value = false
  loadCurrentTabData()
}, { immediate: true })

// 暴露方法给父组件调用
const refresh = () => {
  if (
    (props.currentIndex === 0 || props.currentIndex === 1) &&
    peopleContentRef.value
  ) {
    return peopleContentRef.value.refresh()
  }
  return Promise.resolve()
}

const loadPage = () => {
  if (
    (props.currentIndex === 0 || props.currentIndex === 1) &&
    peopleContentRef.value
  ) {
    peopleContentRef.value.loadPage()
  }
}

defineExpose({
  refresh,
  loadPage
})
</script>

<style lang="scss" scoped>
.content {
  margin-top: 20rpx;
}
</style>
