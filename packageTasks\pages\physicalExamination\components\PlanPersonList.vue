<template>
  <view class="plan-person-list">
    <!-- 标题栏 -->
    <!-- <view class="header">
      <view class="title">{{ planInfo.district_name || '人员名单' }}</view>
      <view class="subtitle">
        人数：{{ planInfo.person_count || 0 }}人 | 
        体检时间：{{ formatDate(planInfo.time) }}
      </view>
    </view> -->

    <!-- 搜索栏 -->
    <!-- <view class="search-bar">
      <input 
        v-model="keyword" 
        placeholder="请输入姓名或身份证号搜索"
        @input="onSearch"
        class="search-input"
      />
    </view> -->

    <!-- 人员列表 -->
    <view class="person-list">
      <view v-if="loading" class="loading">加载中...</view>
      <view v-else-if="list.length === 0" class="empty">暂无数据</view>
      <view v-else>
        <personnelItem
          v-for="person in list"
          :key="person.id"
          :item="person"
          @click="handlePersonClick(person)"
        >
          <template #right>
            <view class="result">
              <text
                :style="{ color: getStatusColor(person.physical_check) }"
                v-if="person.physical_check !== undefined"
              >
                {{ getStatusText(person.physical_check) }}
              </text>
              <text v-else class="status-default">
                未体检
              </text>
            </view>
          </template>
        </personnelItem>
      </view>
    </view>

    <!-- 加载更多 -->
    <view v-if="hasMore && !loading" class="load-more" @click="loadMore">
      加载更多
    </view>
  </view>
</template>

<script setup>
import { ref, computed, watch } from "vue"
import dayjs from "dayjs"
import request from "@/utils/request"
import personnelItem from "@/packageTasks/components/personnelItem.vue"
import {
  personPoliticalExamResultMap,
} from "@/packageTasks/utils/consts"

const props = defineProps({
  planInfo: {
    type: Object,
    default: () => ({})
  },
  missionPhysicalExaminationId: {
    type: [String, Number],
    required: true
  }
})

const list = ref([])
const loading = ref(false)
const keyword = ref("")
const currentPage = ref(1)
const hasMore = ref(true)

// 格式化日期
const formatDate = (date) => {
  if (!date) return ''
  return dayjs(date).format("YYYY-MM-DD")
}

// 获取状态颜色
const getStatusColor = (status) => {
  const statusInfo = personPoliticalExamResultMap[status]
  return statusInfo ? statusInfo.color : '#999'
}

// 获取状态文本
const getStatusText = (status) => {
  const statusInfo = personPoliticalExamResultMap[status]
  return statusInfo ? statusInfo.text : '未体检'
}

// 处理人员点击事件
const handlePersonClick = (person) => {
  // 可以在这里添加人员详情查看逻辑
  console.log('点击人员:', person)
}

// 加载人员列表数据
const loadPersonList = async (isRefresh = false) => {
  if (!props.missionPhysicalExaminationId) return

  if (isRefresh) {
    currentPage.value = 1
    hasMore.value = true
  }

  loading.value = true
  try {
    const params = {
      mission_physical_examination_id: props.missionPhysicalExaminationId,
      page: currentPage.value,
      per_page: 20
    }

    // 添加搜索关键词
    if (keyword.value && keyword.value.trim()) {
      params.keyword = keyword.value.trim()
    }

    const response = await request({
      url: "/mini/mission_physical_examination_person_list",
      method: "GET",
      data: params,
    })

    if (response.code === 200) {
      const newData = response.result?.data || []
      
      if (isRefresh) {
        list.value = newData
      } else {
        list.value.push(...newData)
      }

      // 检查是否还有更多数据
      hasMore.value = newData.length >= 20
      
      if (newData.length > 0) {
        currentPage.value++
      }
    } else {
      throw new Error(response.message || "加载失败")
    }
  } catch (error) {
    console.error("加载人员列表失败:", error)
    uni.showToast({
      title: error.message || "加载失败",
      icon: "none",
    })
  } finally {
    loading.value = false
  }
}

// 搜索
const onSearch = () => {
  loadPersonList(true)
}

// 加载更多
const loadMore = () => {
  if (!loading.value && hasMore.value) {
    loadPersonList(false)
  }
}

// 刷新数据
const refresh = () => {
  return loadPersonList(true)
}

// 监听 missionPhysicalExaminationId 变化
watch(() => props.missionPhysicalExaminationId, (newId) => {
  if (newId) {
    loadPersonList(true)
  }
}, { immediate: true })

// 暴露方法给父组件
defineExpose({
  refresh,
  loadMore
})
</script>

<style lang="scss" scoped>
.plan-person-list {
  .header {
    background: rgba(255, 255, 255, 0.94);
    box-shadow: 0rpx 6rpx 13rpx 0rpx rgba(0, 0, 0, 0.04);
    border-radius: 20rpx;
    border: 2rpx solid #ffffff;
    padding: 30rpx;
    margin-bottom: 20rpx;

    .title {
      font-size: 32rpx;
      color: #333333;
      font-weight: bold;
      margin-bottom: 10rpx;
    }

    .subtitle {
      font-size: 26rpx;
      color: #666666;
    }
  }

  .search-bar {
    margin-bottom: 20rpx;

    .search-input {
      width: 100%;
      height: 80rpx;
      background: rgba(255, 255, 255, 0.94);
      border-radius: 40rpx;
      border: 2rpx solid #e5e5e5;
      padding: 0 30rpx;
      font-size: 28rpx;
      color: #333;

      &::placeholder {
        color: #999;
      }
    }
  }

  .person-list {
    .loading, .empty {
      text-align: center;
      padding: 60rpx;
      color: #999;
      font-size: 28rpx;
    }

    .result {
      font-size: 28rpx;
      font-weight: 500;

      .status-default {
        color: #999;
      }
    }
  }

  .load-more {
    text-align: center;
    padding: 30rpx;
    color: #577f49;
    font-size: 28rpx;
    background: rgba(255, 255, 255, 0.94);
    border-radius: 20rpx;
    margin-top: 20rpx;
  }
}
</style>
