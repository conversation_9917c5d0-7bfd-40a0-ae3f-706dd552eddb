<template>
  <view class="header-box" @click="handleCardClick">
    <view v-if="loading" class="loading">加载中...</view>
    <template v-else>
      <!-- 合格率显示 -->
      <view v-if="showPassRate" class="header-box-title">
        <text class="title-text"> 合格率： </text>
        <text class="title-text" style="color: #577f49">
          {{ summaryData.pass_rate }}
        </text>
      </view>
      
      <!-- 基础统计信息 -->
      <view class="header-box-content" :class="{ row: isRowLayout }">
        <view class="item">
          总人数：{{ summaryData.total_count }}人
        </view>
        <view class="item">
          合格人数：{{ summaryData.passed_count }}人
        </view>
        <view class="item">
          不合格人数：{{ summaryData.un_passed_count }}人
        </view>
      </view>

      <!-- 分隔线 -->
      <view v-if="showDivider" class="divider"></view>

      <!-- 上站体检率显示 -->
      <view v-if="showStationRate" class="header-box-title">
        <text class="title-text"> 上站体检率： </text>
        <text class="title-text" style="color: #577f49">
          {{ summaryData.station_ratio }}
        </text>
      </view>
      <view v-if="showStationRate" class="header-box-content row">
        <view class="item">
          任务数：{{ summaryData.task_num_should }}人
        </view>
        <view class="item">
          体检人员：{{ summaryData.total_count }}人
        </view>
      </view>
    </template>
  </view>
</template>

<script setup>
const props = defineProps({
  summaryData: {
    type: Object,
    default: () => ({})
  },
  loading: {
    type: Boolean,
    default: false
  },
  showPassRate: {
    type: Boolean,
    default: false
  },
  showStationRate: {
    type: Boolean,
    default: false
  },
  showDivider: {
    type: Boolean,
    default: false
  },
  isRowLayout: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['cardClick'])

const handleCardClick = () => {
  emit('cardClick')
}
</script>

<style lang="scss" scoped>
.header-box {
  background: rgba(255, 255, 255, 0.94);
  box-shadow: 0rpx 6rpx 13rpx 0rpx rgba(0, 0, 0, 0.04);
  border-radius: 20rpx 20rpx 20rpx 20rpx;
  border: 2rpx solid #ffffff;
  padding: 44rpx 49rpx;
  margin-bottom: 40rpx;

  &-title {
    font-size: 32rpx;
    color: #333333;
  }

  &-content {
    font-size: 26rpx;
    color: #8c9198;
    margin-top: 10rpx;
    display: flex;
    flex-direction: column;
    gap: 18rpx;

    &.row {
      flex-direction: row;
    }
  }

  .divider {
    width: 100%;
    height: 2rpx;
    background: #e5e5e5;
    margin: 20rpx 0;
  }

  .loading {
    text-align: center;
    padding: 40rpx;
    color: #999;
  }
}
</style>
