<template>
  <view class="header">
    <view class="title">
      <template v-for="(tab, index) in tabs" :key="index">
        <view
          v-if="tab.show"
          :class="currentIndex == index ? 'checked' : ''"
          @click="handleTabClick(index)"
        >
          <view class="">
            {{ tab.title }}
          </view>
          <view class="green" v-if="currentIndex == index"> </view>
        </view>
      </template>
    </view>
  </view>
</template>

<script setup>
const props = defineProps({
  tabs: {
    type: Array,
    required: true
  },
  currentIndex: {
    type: Number,
    default: 0
  }
})

const emit = defineEmits(['changeTab'])

const handleTabClick = (index) => {
  emit('changeTab', index)
}
</script>

<style lang="scss" scoped>
.header {
  .title {
    font-size: 32rpx;
    color: #7e808a;
    text-align: center;
    font-style: normal;
    text-transform: none;
    display: flex;
    text-align: center;
    padding-top: 50rpx;
    justify-content: space-evenly;

    .checked {
      font-weight: bold;
      font-size: 32rpx;
      color: #577f49;
    }

    .green {
      width: 25rpx;
      height: 0rpx;
      background: #ffffff;
      border-bottom: 6rpx solid #577f49;
      border-radius: 6rpx;
      margin: 10rpx auto;
    }
  }
}
</style>
