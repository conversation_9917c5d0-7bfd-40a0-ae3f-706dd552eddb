<script setup>
import { ref, computed, onMounted } from "vue"
import filterBar from "@/packageTasks/components/filterBar.vue"
import request from "@/utils/request"
import { useUserStore } from "@/store/user"

const userStore = useUserStore()

const props = defineProps({
  tableData: {
    type: Array,
    default: () => []
  },
  dataType: {
    type: String,
    default: 'recheck' // 'recheck' | 'spot_check'
  }
})

const keyword = ref("")

const columns = ref([
  {
    title: "单位",
    key: "name",
    width: "220rpx",
  },
  {
    title: "合格率",
    key: "pass_rate",
    width: "150rpx",
  },
  {
    title: "合格数",
    key: "passed_count",
    width: "150rpx",
  },
  {
    title: "不合格数",
    key: "un_passed_count",
    width: "150rpx",
  },
])

const cellStyleFunc = (scope) => {
  if (["pass_rate"].includes(scope.column.key)) {
    return {
      color: "#577F49",
    }
  } else {
    return {}
  }
}

const data = computed(() => {
  return props.tableData.map(item => {
    const passed_count = item.passed_count || 0
    let pass_rate = '0%'
    
    if (props.dataType === 'spot_check') {
      // 抽查：合格率 = passed_count / spot_person_count
      const spot_person_count = item.spot_person_count || 0
      pass_rate = spot_person_count > 0 ? Math.round((passed_count / spot_person_count) * 100) + '%' : '0%'
    } else {
      // 复查：合格率 = passed_count / recheck_num
      const recheck_num = item.recheck_num || 0
      pass_rate = recheck_num > 0 ? Math.round((passed_count / recheck_num) * 100) + '%' : '0%'
    }
    
    return {
      ...item,
      pass_rate
    }
  })
})

const treeData = ref([])
const departmentLoading = ref(false)

// 加载部门树数据
const loadDepartmentTree = async () => {
  departmentLoading.value = true
  try {
    const response = await request({
      url: "/mini/department_tree",
      method: "get",
      data: {
        p_code: userStore.userInfo.district_code,
      },
    })
    treeData.value = response.result || []
  } catch (error) {
    console.error('加载部门树失败:', error)
    uni.showToast({
      title: '加载部门数据失败',
      icon: 'none',
      duration: 2000
    })
  } finally {
    departmentLoading.value = false
  }
}
const selectedDepartmentCodeList = ref([])

const filterActive = computed(() => {
  return selectedDepartmentCodeList.value.length > 0
})

const filterVisible = ref(false)

const openFilter = () => {
  filterVisible.value = true
}

const closeFilter = () => {
  filterVisible.value = false
}

const confirm = () => {
  console.log(keyword.value)
  // 通知父组件关键词搜索变化
  emit('keywordSearch', keyword.value)
}

const emit = defineEmits(['departmentChange', 'keywordSearch'])

const onDepartmentChange = (data) => {
  selectedDepartmentCodeList.value = data
  closeFilter()
  // 通知父组件筛选条件已变化
  emit('departmentChange', data)
}

// 组件挂载时加载部门树数据
onMounted(() => {
  loadDepartmentTree()
})
</script>

<template>
  <view class="data-content">
    <filterBar
      v-model:keyword="keyword"
      :filterActive="filterActive"
      placeholder="输入单位名称"
      @openFilter="openFilter"
      @closeFilter="closeFilter"
      @confirm="confirm"
    />
    <up-table2 :columns="columns" :data="data" :cellStyle="cellStyleFunc" />
  </view>

  <up-popup
    closeable
    :show="filterVisible"
    @close="closeFilter"
    :round="10"
    mode="bottom"
  >
    <departmentPicker
      title="筛选"
      :nodes="treeData"
      :selectedData="selectedDepartmentCodeList"
      @change="onDepartmentChange"
    />
  </up-popup>
</template>
