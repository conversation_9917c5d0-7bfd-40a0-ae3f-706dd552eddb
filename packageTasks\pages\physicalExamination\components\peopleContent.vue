<script setup>
import { ref, computed, onMounted, watch } from "vue"
import {
  personPoliticalExamResultMap,
  personEducationMap,
  personGraduateMap,
} from "@/packageTasks/utils/consts"
import { usePagination } from "@/utils/hooks"
import matchingFilter from "@/packageTasks/components/matchingFilter.vue"
import personnelItem from "@/packageTasks/components/personnelItem.vue"
import filterBar from "@/packageTasks/components/filterBar.vue"
import {
  getMissionPersonList,
  createQueryBuilder,
} from "@/packageTasks/utils/api/missionPerson"

const props = defineProps({
  type: {
    type: String,
    default: "physical_check", // physical_check=体检, physical_recheck=复检, physical_spot_check=抽检
  },
  missionId: {
    type: [String, Number],
    default: 0,
  },
})

const list = ref([])

const keyword = ref("")
const filterVisible = ref(false)

const openFilter = () => {
  filterVisible.value = true
}

const closeFilter = () => {
  filterVisible.value = false
}

const filterOptions = ref([
  {
    title: "体检结果",
    key: "physical_check",
    options: Object.values(personPoliticalExamResultMap).map(
      (item) => item.text
    ),
    value: Object.values(personPoliticalExamResultMap).map(
      (item) => item.status
    ),
  },
  {
    title: "复检结果",
    key: "physical_recheck",
    options: Object.values(personPoliticalExamResultMap).map(
      (item) => item.text
    ),
    value: Object.values(personPoliticalExamResultMap).map(
      (item) => item.status
    ),
  },
  {
    title: "抽检结果",
    key: "physical_spot_check",
    options: Object.values(personPoliticalExamResultMap).map(
      (item) => item.text
    ),
    value: Object.values(personPoliticalExamResultMap).map(
      (item) => item.status
    ),
  },
  {
    title: "学业情况",
    key: "graduate",
    options: Object.values(personGraduateMap).map((item) => item.text),
    value: Object.values(personGraduateMap).map((item) => item.status),
  },
  {
    title: "文化程度",
    key: "education",
    options: Object.values(personEducationMap).map((item) => item.text),
    value: Object.values(personEducationMap).map((item) => item.status),
  },
  {
    title: "取得学位",
    key: "degree",
    options: ["学士", "硕士", "博士"],
    value: ["学士", "硕士", "博士"],
  },
])

const filterValues = ref({
  physical_check: [],
  physical_recheck: [],
  physical_spot_check: [],
  graduate: [],
  education: [],
  degree: [],
  isContact: [],
  priority: [],
})

const filterActive = computed(() => {
  return Object.values(filterValues.value).some((val) => val && val.length > 0)
})

// API调用逻辑
const loadData = (options = {}) => {
  return new Promise((resolve, reject) => {
    // 使用查询构建器构建参数
    const queryBuilder = createQueryBuilder(props.missionId).page(
      options.page || 1
    )

    // 根据tab类型设置不同的筛选条件
    if (props.type === "physical_check") {
      queryBuilder.physicalCheck("1,2")
    } else if (props.type === "physical_recheck") {
      queryBuilder.physicalRecheck("1,2")
    } else if (props.type === "physical_spot_check") {
      queryBuilder.physicalSpotCheck("1,2")
    }

    // 添加搜索关键词
    if (keyword.value.trim()) {
      queryBuilder.keyword(keyword.value.trim())
    }

    // 添加筛选条件
    Object.keys(filterValues.value).forEach((key) => {
      if (filterValues.value[key] && filterValues.value[key].length > 0) {
        // 根据不同筛选条件转换参数
        switch (key) {
          default:
            queryBuilder.params[key] = filterValues.value[key].join(",")
            break
        }
      }
    })

    const params = queryBuilder.build()

    // 调试信息
    console.log("宣传比对搜索参数:", params)
    console.log("Tab类型:", props.type)
    console.log("关键词:", keyword.value)
    console.log("筛选条件:", filterValues.value)

    getMissionPersonList(params)
      .then((res) => {
        console.log("宣传比对API响应:", res)
        if (res.code === 200) {
          list.value.push(...(res.result?.data || []))
          resolve(res)
        } else {
          reject(res)
        }
      })
      .catch(reject)
  })
}

const { page, loadPage, refresh, loadMoreStatus } = usePagination({
  loadData,
  list,
})

const onFilterChange = (value) => {
  filterValues.value = value
  filterVisible.value = false
  refresh() // 应用筛选后刷新列表
}

const confirm = () => {
  refresh() // 搜索后刷新列表
}

const handleClick = (item) => {
  uni.navigateTo({
    url: `/packageTasks/pages/physicalExamination/detail?id=${item.id}`,
  })
}

// 监听tab类型变化，重新加载数据
watch(
  () => props.type,
  () => {
    refresh()
  }
)

// 向父组件暴露方法
defineExpose({
  refresh,
  loadPage,
})

// 组件挂载时加载数据
onMounted(() => {
  if (props.missionId) {
    loadPage()
  }
})
</script>

<template>
  <view class="people-content">
    <filterBar
      v-model:keyword="keyword"
      :filterActive="filterActive"
      placeholder="输入姓名或身份证号码搜索"
      @openFilter="openFilter"
      @closeFilter="closeFilter"
      @confirm="confirm"
    />

    <view class="list">
      <personnelItem
        v-for="item in list"
        :key="item.id"
        :item="item"
        @click="handleClick(item)"
      >
        <template #right>
          <view class="result">
            <text
              :style="{
                color: personPoliticalExamResultMap[item.physical_check]?.color,
              }"
              v-if="type === 'physical_check'"
            >
              {{ personPoliticalExamResultMap[item.physical_check].text || "" }}
            </text>
            <text
              :style="{
                color:
                  personPoliticalExamResultMap[item.physical_recheck]?.color,
              }"
              v-if="type === 'physical_recheck'"
            >
              {{
                personPoliticalExamResultMap[item.physical_recheck].text || ""
              }}
            </text>
            <text
              :style="{
                color:
                  personPoliticalExamResultMap[item.physical_spot_check]?.color,
              }"
              v-if="type === 'physical_spot_check'"
            >
              {{
                personPoliticalExamResultMap[item.physical_spot_check].text ||
                ""
              }}
            </text>
          </view>
        </template>
      </personnelItem>

      <!-- 空状态 -->
      <empty
        v-if="list.length === 0 && loadMoreStatus === 'nomore'"
        theme="2"
        text="暂无数据"
      />

      <!-- 加载更多状态 -->
      <up-loadmore v-if="list.length" :status="loadMoreStatus" />
    </view>
  </view>

  <up-popup
    closeable
    :show="filterVisible"
    @close="closeFilter"
    :round="10"
    mode="bottom"
  >
    <matchingFilter
      :options="filterOptions"
      v-model="filterValues"
      @change="onFilterChange"
    />
  </up-popup>
</template>

<style lang="scss" scoped>
.list {
  .result {
    height: 92rpx;
    display: flex;
    flex-direction: column;
    align-items: flex-end;

    text {
      font-size: 24rpx;
    }
  }
}
</style>
