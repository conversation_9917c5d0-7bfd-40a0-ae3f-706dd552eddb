<script setup>
import dayjs from "dayjs"

const props = defineProps({
  list: {
    type: Array,
    default: () => [],
  },
})

const emit = defineEmits(['showPersonList'])

const handleShowPersonList = (item) => {
  emit('showPersonList', item)
}
</script>

<template>
  <view class="plan-list">
    <view class="plan-item" v-for="item in list" :key="item.id">
      <view class="plan-info">
        <view class="title">{{ item.district_name }}</view>
        <view class="num-time">
          <text class="num">人数：{{ item.person_count }}</text>
          <text class="time"
            >体检时间：{{ dayjs(item.time).format("YYYY-MM-DD") }}</text
          >
        </view>
      </view>
      <button class="btn" @click="handleShowPersonList(item)">名单</button>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.plan-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: rgba(255, 255, 255, 0.94);
  box-shadow: 0rpx 6rpx 13rpx 1rpx rgba(0, 0, 0, 0.04);
  border-radius: 20rpx;
  border: 2rpx solid #ffffff;
  padding: 33rpx 26rpx 32rpx 49rpx;
  margin-bottom: 20rpx;
  position: relative;
  gap: 26rpx;

  .plan-info {
    flex: 1;

    .title {
      -webkit-line-clamp: 2;
      line-clamp: 2;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
      font-size: 32rpx;
      color: #333;
    }

    .num-time {
      flex-shrink: 0;
      font-size: 24rpx;
      color: #999999;
      margin-top: 18rpx;
      display: flex;
      align-items: center;
      gap: 10rpx;
    }
  }

  .btn {
    width: 120rpx;
    height: 60rpx;
    line-height: 60rpx;
    background: #577f49;
    color: #fff;
    font-weight: 500;
    font-size: 26rpx;
    border-radius: 31rpx;
  }
}
</style>
