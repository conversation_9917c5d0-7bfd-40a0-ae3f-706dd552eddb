<script setup>
import { ref, computed } from "vue"
import { onLoad } from "@dcloudio/uni-app"
import personBaseInfo from "@/packageTasks/components/personBaseInfo.vue"
import { getMissionPersonDetail } from "@/packageTasks/utils/api/missionPerson"
import { personPoliticalExamResultMap } from "@/packageTasks/utils/consts"
import dayjs from "dayjs"

// 人员详情
const peopleDetail = ref({})

// 当前人员 id
const id = ref("")

// 获取人员详情
const getPersonDetail = async () => {
  if (!id.value) return
  try {
    const res = await getMissionPersonDetail(id.value)
    if (res.code === 200 && res.result) {
      peopleDetail.value = res.result
    }
  } catch (err) {
    console.error("获取体检详情失败", err)
  }
}

// 进入页面加载数据
onLoad((options) => {
  id.value = options.id || ""
  getPersonDetail()
})

// 结果映射
const physicalCheckResult = computed(() => personPoliticalExamResultMap[peopleDetail.value.physical_check] || {})
const physicalRecheckResult = computed(() => personPoliticalExamResultMap[peopleDetail.value.physical_recheck] || {})
const physicalSpotCheckResult = computed(() => personPoliticalExamResultMap[peopleDetail.value.physical_spot_check] || {})

// 宣传比对结果（保留原有展示）
const promoteCompareResultMap = {
  1: { title: "有意向未报名", color: "#ea2b2b" },
  2: { title: "无意向已报名", color: "#FF6A28" },
  3: { title: "有意向已报名", color: "#577f49" },
}
const compareResult = computed(() => promoteCompareResultMap[peopleDetail.value.promote_compare_result] || null)

const baseInfoExpand = ref(false)

// 格式化时间 YYYY/MM/DD HH:mm
const formatDateTime = (val) => {
  if (!val) return ""
  return dayjs(val).format("YYYY/MM/DD HH:mm")
}
</script>

<template>
  <navHeader
    title="体格检查"
    bg="/static/image/bg.png"
    showLeft
    :clickLeft="clickLeft"
  ></navHeader>
  <view class="people-detail">
    <personBaseInfo
      v-model:baseInfoExpand="baseInfoExpand"
      :peopleDetail="peopleDetail"
    />

    <view class="fields">
      <!-- 比对结果 -->
      <view class="field-item">
        <view class="field-item-label"> 比对结果 </view>
        <view class="field-item-value">
          <text
            v-if="compareResult"
            :style="{ color: compareResult.color }"
          >
            {{ compareResult.title }}
          </text>
          <text v-else style="color: #999"> 暂无比对结果 </text>
        </view>
      </view>

      <!-- 体检结果 -->
      <view class="field-item">
        <view class="field-item-label">
          体检结果
          <text v-if="peopleDetail.physical_check_at" class="time">
            {{ formatDateTime(peopleDetail.physical_check_at) }}
          </text>
        </view>
        <view class="field-item-value">
          <text
            v-if="physicalCheckResult.text"
            :style="{ color: physicalCheckResult.color }"
          >
            {{ physicalCheckResult.text }}
          </text>
          <text v-else style="color: #999"> 暂无结果 </text>
        </view>
        <view
          class="field-item-desc"
          v-if="peopleDetail.physical_check_remark"
        >
          <text>{{ peopleDetail.physical_check_remark }}</text>
        </view>
      </view>

      <!-- 复查结果 -->
      <view class="field-item">
        <view class="field-item-label">
          复查结果
          <text v-if="peopleDetail.physical_recheck_at" class="time">
            {{ formatDateTime(peopleDetail.physical_recheck_at) }}
          </text>
        </view>
        <view class="field-item-value">
          <text
            v-if="physicalRecheckResult.text"
            :style="{ color: physicalRecheckResult.color }"
          >
            {{ physicalRecheckResult.text }}
          </text>
          <text v-else style="color: #999"> 暂无结果 </text>
        </view>
        <view
          class="field-item-desc"
          v-if="peopleDetail.physical_recheck_remark"
        >
          <text>{{ peopleDetail.physical_recheck_remark }}</text>
        </view>
      </view>

      <!-- 抽查结果 -->
      <view class="field-item">
        <view class="field-item-label">
          抽查结果
          <text v-if="peopleDetail.physical_spot_check_at" class="time">
            {{ formatDateTime(peopleDetail.physical_spot_check_at) }}
          </text>
        </view>
        <view class="field-item-value">
          <text
            v-if="physicalSpotCheckResult.text"
            :style="{ color: physicalSpotCheckResult.color }"
          >
            {{ physicalSpotCheckResult.text }}
          </text>
          <text v-else style="color: #999"> 暂无结果 </text>
        </view>
        <view
          class="field-item-desc"
          v-if="peopleDetail.physical_spot_check_remark"
        >
          <text>{{ peopleDetail.physical_spot_check_remark }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<style>
page {
  background-color: #ffffff;
}
</style>

<style lang="scss" scoped>
.people-detail {
  display: flex;
  flex-direction: column;
  padding: 220rpx 45rpx 0;

  .name-call {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .name {
      display: flex;
      align-items: center;
      font-size: 52rpx;
      font-weight: bold;
      color: #21232c;

      .gender {
        width: 49rpx;
        height: 49rpx;
        margin-left: 22rpx;
      }
    }

    .call {
      width: 56rpx;
      height: 56rpx;
      border: 1rpx solid #c3cbd6;
      padding: 8rpx;
      border-radius: 50%;
    }
  }

  .base-info {
    margin-top: 64rpx;
    border-bottom: 2rpx solid #0ba133;

    .base-info-expand {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-bottom: 30rpx;

      .base-info-expand-title {
        font-size: 32rpx;
        color: #111111;
      }

      .base-info-expand-content {
        display: flex;
        align-items: center;
        gap: 4rpx;
        font-size: 26rpx;
        color: #989898;
      }
    }

    .info {
      .item {
        padding: 20rpx 0;
        border-bottom: 2rpx solid #f6f6f6;

        .label {
          font-size: 28rpx;
          color: #9ca3b5;
          margin-bottom: 7rpx;
        }

        .value {
          font-size: 32rpx;
          color: #111111;
        }

        .another-phone {
          display: flex;
          align-items: center;
          gap: 120rpx;

          .another-phone-item {
            display: flex;
            align-items: center;
            gap: 20rpx;

            .another-phone-item-label {
              font-size: 32rpx;
              color: #111111;
              margin-right: 50rpx;
            }

            .another-phone-item-call {
              display: flex;
              align-items: center;
              justify-content: center;
              border: 1rpx solid #c3cbd6;
              border-radius: 50%;
              overflow: hidden;

              .call {
                width: 24rpx;
                height: 24rpx;
              }
            }
          }
        }
      }
    }
  }

  .fields {
    margin-top: 30rpx;

    .field-item {
      padding: 30rpx 0;
      border-bottom: 2rpx solid #f6f6f6;

      .field-item-label {
        font-size: 32rpx;
        color: #111111;
        margin-bottom: 10rpx;

        .time {
          font-size: 24rpx;
          color: #9ca3b5;
          margin-left: 8rpx;
        }
      }
      .field-item-value {
        margin-bottom: 9rpx;
      }
      .field-item-desc {
        font-size: 32rpx;
        color: #111111;
      }
    }
  }

  .btn-wrapper {
    display: flex;
    justify-content: center;
    margin-top: 50rpx;
    margin-bottom: 40rpx;
    .submit {
      width: 702rpx;
      height: 100rpx;
      line-height: 100rpx;
      font-size: 34rpx;
      color: #ffffff;
      background: #577f49;
      box-shadow: 0rpx 20rpx 48rpx 1rpx rgba(87, 127, 73, 0.2);
      border-radius: 50rpx 50rpx 50rpx 50rpx;
    }
  }
}
</style>
