<script setup>
import { ref } from "vue"
import { onLoad } from "@dcloudio/uni-app"
import TabHeader from "./components/TabHeader.vue"
import PhysicalExaminationTabs from "./components/PhysicalExaminationTabs.vue"

const missionId = ref(0)
const currentIndex = ref(0)

const tabs = ref([
  { name: "plan", title: "计划", show: true },
  { name: "check", title: "结果", show: true },
  { name: "recheck", title: "复查", show: true },
  { name: "spot_check", title: "抽查", show: true },
])

const changeTab = (index) => {
  currentIndex.value = index
}

const clickLeft = () => {
  uni.navigateBack()
}

onLoad((options) => {
  missionId.value = options.missionId
})
</script>

<template>
  <navHeader
    title="体格检查"
    bg="/static/image/bg.png"
    showLeft
    :clickLeft="clickLeft"
  ></navHeader>
  <view class="page-physical-examination">
    <TabHeader
      :tabs="tabs"
      :currentIndex="currentIndex"
      @changeTab="changeTab"
    />
    <PhysicalExaminationTabs
      :currentIndex="currentIndex"
      :missionId="missionId"
    />
  </view>
</template>

<style lang="scss" scoped>
.page-physical-examination {
  padding: 24rpx;
  padding-top: 220rpx;
}
</style>
