<script setup>
import { ref } from "vue"
import { onLoad, onPullDownRefresh, onReachBottom } from "@dcloudio/uni-app"
import TabHeader from "./components/TabHeader.vue"
import PhysicalExaminationTabs from "./components/PhysicalExaminationTabs.vue"

const missionId = ref(0)
const currentIndex = ref(0)
const tabsRef = ref(null)

const tabs = ref([
  { name: "plan", title: "计划", show: true },
  { name: "check", title: "结果", show: true },
  { name: "recheck", title: "复查", show: true },
  { name: "spot_check", title: "抽查", show: true },
])

const changeTab = (index) => {
  currentIndex.value = index
}

const clickLeft = () => {
  uni.navigateBack()
}

onLoad((options) => {
  missionId.value = options.missionId

  tabsRef.value?.loadPage()
})

// 下拉刷新
onPullDownRefresh(() => {
  if (tabsRef.value) {
    tabsRef.value
      .refresh()
      .then(() => {
        uni.stopPullDownRefresh()
      })
      .catch(() => {
        uni.stopPullDownRefresh()
      })
  } else {
    uni.stopPullDownRefresh()
  }
})

// 上拉加载更多
onReachBottom(() => {
  if (tabsRef.value) {
    tabsRef.value.loadPage()
  }
})
</script>

<template>
  <navHeader
    title="体格检查"
    bg="/static/image/bg.png"
    showLeft
    :clickLeft="clickLeft"
  ></navHeader>
  <view class="page-physical-examination">
    <TabHeader
      :tabs="tabs"
      :currentIndex="currentIndex"
      @changeTab="changeTab"
    />
    <PhysicalExaminationTabs
      ref="tabsRef"
      :currentIndex="currentIndex"
      :missionId="missionId"
    />
  </view>
</template>

<style lang="scss" scoped>
.page-physical-examination {
  padding: 24rpx;
  padding-top: 220rpx;
}
</style>
