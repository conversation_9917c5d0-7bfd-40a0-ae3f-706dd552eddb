<script setup>
import { ref, computed, watch } from "vue"
import request from "@/utils/request"
import { onLoad, onPullDownRefresh, onReachBottom } from "@dcloudio/uni-app"
import planList from "./components/planList.vue"
import peopleContent from "./components/peopleContent.vue"
import dataContent1 from "./components/dataContent1.vue"
import dataContent23 from "./components/dataContent23.vue"
import {
  getStatisticsMissionPhysicalCheckList,
  getStatisticsMissionPhysicalRecheckList,
  getStatisticsMissionPhysicalSpotCheckList,
} from "@/packageTasks/utils/api/missionPerson"

const missionId = ref(0)
const peopleContentRef = ref(null)

const physicalExamPlanData = ref([])

// 数据状态
const physicalCheckData = ref([])
const physicalRecheckData = ref([])
const physicalSpotCheckData = ref([])
const loading = ref(false)

// 汇总数据状态
const physicalCheckTotalData = ref({})
const physicalRecheckTotalData = ref({})
const physicalSpotCheckTotalData = ref({})

// 筛选参数
const filterParams = ref({
  keyword: "",
  department_codes: [],
})

const currentIndex = ref(0)

const activeTab = computed(() => tabs.value[currentIndex.value])

// 计算属性 - 体格检查汇总数据
const physicalCheckSummary = computed(() => {
  const data = physicalCheckTotalData.value
  const passed_count = data.passed_count || 0
  const task_num_actual = data.task_num_actual || 0
  const task_num_should = data.task_num_should || 0
  const un_passed_count = data.un_passed_count || 0
  const station_rate = data.station_rate || 0

  // 计算合格率
  const pass_rate =
    task_num_actual > 0 ? Math.round((passed_count / task_num_actual) * 100) : 0

  // 计算上站体检率（与子组件逻辑保持一致）
  const station_ratio = `1:${station_rate / 100}`

  return {
    pass_rate: `${pass_rate}%`,
    total_count: task_num_actual,
    passed_count,
    un_passed_count,
    task_num_should,
    station_ratio,
  }
})

// 计算属性 - 复查汇总数据
const physicalRecheckSummary = computed(() => {
  const data = physicalRecheckTotalData.value
  const passed_count = data.passed_count || 0
  const un_passed_count = data.un_passed_count || 0
  const total_count = passed_count + un_passed_count

  return {
    total_count,
    passed_count,
    un_passed_count,
  }
})

// 计算属性 - 抽查汇总数据
const physicalSpotCheckSummary = computed(() => {
  const data = physicalSpotCheckTotalData.value
  const passed_count = data.passed_count || 0
  const un_passed_count = data.un_passed_count || 0
  const total_count = passed_count + un_passed_count

  // 计算合格率
  const pass_rate =
    total_count > 0 ? Math.round((passed_count / total_count) * 100) : 0

  return {
    pass_rate: `${pass_rate}%`,
    total_count,
    passed_count,
    un_passed_count,
  }
})

// 加载体格检查计划数据
const loadPhysicalExamPlanData = async () => {
  if (!missionId.value) return Promise.resolve()

  loading.value = true
  try {
    const params = {
      mission_id: missionId.value,
      page: 1,
      per_page: 10000,
    }

    const response = await request({
      url: "/mini/mission_physical_examination",
      method: "GET",
      data: params,
    })

    if (response.code === 200) {
      physicalExamPlanData.value = response.result?.data || []
    } else {
      throw new Error(response.message || "加载失败")
    }
  } catch (error) {
    console.error("加载体格检查计划数据失败:", error)
    uni.showToast({
      title: error.message || "加载失败",
      icon: "none",
    })
  } finally {
    loading.value = false
  }
}

// 加载体格检查数据
const loadPhysicalCheckData = async () => {
  if (!missionId.value) return Promise.resolve()

  loading.value = true
  try {
    const params = {
      mission_id: missionId.value,
      page: 1,
      per_page: 10000,
    }

    // 添加筛选参数
    if (filterParams.value.keyword && filterParams.value.keyword.trim()) {
      params.keyword = filterParams.value.keyword.trim()
    }
    if (filterParams.value.department_codes.length > 0) {
      params.department_codes = filterParams.value.department_codes.join(",")
    }

    const response = await getStatisticsMissionPhysicalCheckList(params)

    if (response.code === 200) {
      physicalCheckData.value = response.result?.data || []
      physicalCheckTotalData.value = response.result?.total_data || {}
    } else {
      throw new Error(response.message || "加载失败")
    }
  } catch (error) {
    console.error("加载体格检查数据失败:", error)
    uni.showToast({
      title: error.message || "加载失败",
      icon: "none",
    })
  } finally {
    loading.value = false
  }
}

// 加载复查数据
const loadPhysicalRecheckData = async () => {
  if (!missionId.value) return Promise.resolve()

  loading.value = true
  try {
    const params = {
      mission_id: missionId.value,
      page: 1,
      per_page: 10000,
    }

    // 添加筛选参数
    if (filterParams.value.keyword && filterParams.value.keyword.trim()) {
      params.keyword = filterParams.value.keyword.trim()
    }
    if (filterParams.value.department_codes.length > 0) {
      params.department_codes = filterParams.value.department_codes.join(",")
    }

    const response = await getStatisticsMissionPhysicalRecheckList(params)

    if (response.code === 200) {
      physicalRecheckData.value = response.result?.data || []
      physicalRecheckTotalData.value = response.result?.total_data || {}
    } else {
      throw new Error(response.message || "加载失败")
    }
  } catch (error) {
    console.error("加载复查数据失败:", error)
    uni.showToast({
      title: error.message || "加载失败",
      icon: "none",
    })
  } finally {
    loading.value = false
  }
}

// 加载抽查数据
const loadPhysicalSpotCheckData = async () => {
  if (!missionId.value) return Promise.resolve()

  loading.value = true
  try {
    const params = {
      mission_id: missionId.value,
      page: 1,
      per_page: 10000,
    }

    // 添加筛选参数
    if (filterParams.value.keyword && filterParams.value.keyword.trim()) {
      params.keyword = filterParams.value.keyword.trim()
    }
    if (filterParams.value.department_codes.length > 0) {
      params.department_codes = filterParams.value.department_codes.join(",")
    }

    const response = await getStatisticsMissionPhysicalSpotCheckList(params)

    if (response.code === 200) {
      physicalSpotCheckData.value = response.result?.data || []
      physicalSpotCheckTotalData.value = response.result?.total_data || {}
    } else {
      throw new Error(response.message || "加载失败")
    }
  } catch (error) {
    console.error("加载抽查数据失败:", error)
    uni.showToast({
      title: error.message || "加载失败",
      icon: "none",
    })
  } finally {
    loading.value = false
  }
}

const tabs = ref([
  {
    name: "plan",
    title: "计划",
    show: true,
    dataView: false,
    loadData: loadPhysicalExamPlanData,
  },
  {
    name: "check",
    title: "结果",
    show: true,
    dataView: false,
    loadData: loadPhysicalCheckData,
  },
  {
    name: "recheck",
    title: "复查",
    show: true,
    dataView: false,
    loadData: loadPhysicalRecheckData,
  },
  {
    name: "spot_check",
    title: "抽查",
    show: true,
    dataView: false,
    loadData: loadPhysicalSpotCheckData,
  },
])

const changeTab = (index) => {
  currentIndex.value = index
  activeTab.value.loadData()
}

const onResultCardClick = () => {
  activeTab.value.dataView = true
  loadPhysicalCheckData()
}

const onRecheckCardClick = () => {
  activeTab.value.dataView = true
  loadPhysicalRecheckData()
}

const onSpotCheckCardClick = () => {
  activeTab.value.dataView = true
  loadPhysicalSpotCheckData()
}

const clickLeft = () => {
  if (activeTab.value.dataView) {
    activeTab.value.dataView = false
  } else {
    uni.navigateBack()
  }
}

// 处理子组件的部门筛选变化
const onDepartmentChange = (departmentCodes) => {
  filterParams.value.department_codes = departmentCodes
  activeTab.value.loadData()
}

// 处理关键词搜索
const onKeywordSearch = (keyword) => {
  filterParams.value.keyword = keyword
  activeTab.value.loadData()
}

onLoad((options) => {
  missionId.value = options.missionId
})

// 下拉刷新
onPullDownRefresh(() => {
  // 刷新人员列表
  if (
    (currentIndex.value === 0 || currentIndex.value === 1) &&
    peopleContentRef.value
  ) {
    peopleContentRef.value
      .refresh()
      .then(() => {
        uni.stopPullDownRefresh()
      })
      .catch(() => {
        uni.stopPullDownRefresh()
      })
  } else {
    uni.stopPullDownRefresh()
  }
})

// 上拉加载更多
onReachBottom(() => {
  if (
    (currentIndex.value === 0 || currentIndex.value === 1) &&
    peopleContentRef.value
  ) {
    peopleContentRef.value.loadPage()
  }
})
</script>

<template>
  <navHeader
    title="体格检查"
    bg="/static/image/bg.png"
    showLeft
    :clickLeft="clickLeft"
  ></navHeader>
  <view class="page-physical-examination">
    <view class="header">
      <view class="title">
        <template v-for="(tab, index) in tabs" :key="index">
          <view
            v-if="tab.show"
            :class="currentIndex == index ? 'checked' : ''"
            @click="changeTab(index)"
          >
            <view class="">
              {{ tab.title }}
            </view>
            <view class="green" v-if="currentIndex == index"> </view>
          </view>
        </template>
      </view>
    </view>

    <view class="content">
      <template v-if="activeTab.name === 'plan'">
        <planList :list="physicalExamPlanData" />
      </template>
      <template v-if="activeTab.name === 'check'">
        <template v-if="activeTab.dataView">
          <dataContent1
            :tableData="physicalCheckData"
            @departmentChange="onDepartmentChange"
            @keywordSearch="onKeywordSearch"
          />
        </template>
        <template v-else>
          <view class="header-box" @click="onResultCardClick">
            <view v-if="loading" class="loading">加载中...</view>
            <template v-else>
              <view class="header-box-title">
                <text class="title-text"> 合格率： </text>
                <text class="title-text" style="color: #577f49">
                  {{ physicalCheckSummary.pass_rate }}
                </text>
              </view>
              <view class="header-box-content">
                <view class="item">
                  总人数：{{ physicalCheckSummary.total_count }}人
                </view>
                <view class="item">
                  合格人数：{{ physicalCheckSummary.passed_count }}人
                </view>
                <view class="item">
                  不合格人数：{{ physicalCheckSummary.un_passed_count }}人
                </view>
              </view>

              <view class="divider"></view>

              <view class="header-box-title">
                <text class="title-text"> 上站体检率： </text>
                <text class="title-text" style="color: #577f49">
                  {{ physicalCheckSummary.station_ratio }}
                </text>
              </view>
              <view class="header-box-content row">
                <view class="item">
                  任务数：{{ physicalCheckSummary.task_num_should }}人
                </view>
                <view class="item">
                  体检人员：{{ physicalCheckSummary.total_count }}人
                </view>
              </view>
            </template>
          </view>
          <peopleContent
            ref="peopleContentRef"
            type="physical_check"
            :missionId="missionId"
          />
        </template>
      </template>
      <template v-if="activeTab.name === 'recheck'">
        <template v-if="activeTab.dataView">
          <dataContent23
            :tableData="physicalRecheckData"
            dataType="recheck"
            @departmentChange="onDepartmentChange"
            @keywordSearch="onKeywordSearch"
          />
        </template>
        <template v-else>
          <view class="header-box" @click="onRecheckCardClick">
            <view v-if="loading" class="loading">加载中...</view>
            <template v-else>
              <view class="header-box-content">
                <view class="item">
                  总人数：{{ physicalRecheckSummary.total_count }}人
                </view>
                <view class="item">
                  合格人数：{{ physicalRecheckSummary.passed_count }}人
                </view>
                <view class="item">
                  不合格人数：{{ physicalRecheckSummary.un_passed_count }}人
                </view>
              </view>
            </template>
          </view>
          <peopleContent
            ref="peopleContentRef"
            type="physical_recheck"
            :missionId="missionId"
          />
        </template>
      </template>
      <template v-if="activeTab.name === 'spot_check'">
        <template v-if="activeTab.dataView">
          <dataContent23
            :tableData="physicalSpotCheckData"
            dataType="spot_check"
            @departmentChange="onDepartmentChange"
            @keywordSearch="onKeywordSearch"
          />
        </template>
        <template v-else>
          <view class="header-box" @click="onSpotCheckCardClick">
            <view v-if="loading" class="loading">加载中...</view>
            <template v-else>
              <view class="header-box-title">
                <text class="title-text"> 合格率： </text>
                <text class="title-text" style="color: #577f49">
                  {{ physicalSpotCheckSummary.pass_rate }}
                </text>
              </view>
              <view class="header-box-content">
                <view class="item">
                  总人数：{{ physicalSpotCheckSummary.total_count }}人
                </view>
                <view class="item">
                  合格人数：{{ physicalSpotCheckSummary.passed_count }}人
                </view>
                <view class="item">
                  不合格人数：{{ physicalSpotCheckSummary.un_passed_count }}人
                </view>
              </view>
            </template>
          </view>
          <peopleContent
            ref="peopleContentRef"
            type="physical_spot_check"
            :missionId="missionId"
          />
        </template>
      </template>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.page-physical-examination {
  padding: 24rpx;
  padding-top: 220rpx;

  .title {
    font-size: 32rpx;
    color: #7e808a;

    text-align: center;
    font-style: normal;
    text-transform: none;
    display: flex;
    text-align: center;
    padding-top: 50rpx;
    justify-content: space-evenly;

    .checked {
      font-weight: bold;
      font-size: 32rpx;
      color: #577f49;
    }

    .green {
      width: 25rpx;
      height: 0rpx;
      background: #ffffff;
      border-bottom: 6rpx solid #577f49;
      border-radius: 6rpx;
      margin: 10rpx auto;
    }
  }
}

.content {
  margin-top: 20rpx;

  .header-box {
    background: rgba(255, 255, 255, 0.94);
    box-shadow: 0rpx 6rpx 13rpx 0rpx rgba(0, 0, 0, 0.04);
    border-radius: 20rpx 20rpx 20rpx 20rpx;
    border: 2rpx solid #ffffff;
    padding: 44rpx 49rpx;
    margin-bottom: 40rpx;

    &-title {
      font-size: 32rpx;
      color: #333333;
    }

    &-content {
      font-size: 26rpx;
      color: #8c9198;
      margin-top: 10rpx;
      display: flex;
      flex-direction: column;
      gap: 18rpx;

      &.row {
        flex-direction: row;
      }
    }

    .divider {
      width: 100%;
      height: 2rpx;
      background: #e5e5e5;
      margin: 20rpx 0;
    }

    .loading {
      text-align: center;
      padding: 40rpx;
      color: #999;
    }
  }
}
</style>
