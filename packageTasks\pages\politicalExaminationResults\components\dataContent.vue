<script setup>
import { ref, computed, watch, onMounted } from "vue"
import { getStatisticsMissionPoliticalExamList } from "@/packageTasks/utils/api/missionPerson"
import filterBar from "@/packageTasks/components/filterBar.vue"
import departmentPicker from "@/components/departmentPicker/departmentPicker.vue"
import request from "@/utils/request"
import { useUserStore } from "@/store/user"

const userStore = useUserStore()

const props = defineProps({
  missionId: {
    type: [String, Number],
    default: "",
  },
})

const columns = ref([
  {
    title: "单位",
    key: "department_name",
    width: "220rpx",
  },
  {
    title: "总人数",
    key: "total_count",
    width: "120rpx",
  },
  {
    title: "合格数",
    key: "qualified_count",
    width: "120rpx",
  },
  {
    title: "不合格数",
    key: "unqualified_count",
    width: "150rpx",
  },
])

const data = ref([])
const loading = ref(false)

const keyword = ref("")

const treeData = ref([])
const departmentLoading = ref(false)

// 加载部门树数据
const loadDepartmentTree = async () => {
  departmentLoading.value = true
  try {
    const response = await request({
      url: "/mini/department_tree",
      method: "get",
      data: {
        p_code: userStore.userInfo.district_code,
      },
    })
    treeData.value = response.result || []
  } catch (error) {
    console.error('加载部门树失败:', error)
    uni.showToast({
      title: '加载部门数据失败',
      icon: 'none',
      duration: 2000
    })
  } finally {
    departmentLoading.value = false
  }
}
const selectedDepartmentCodeList = ref([])

const filterActive = computed(() => {
  return selectedDepartmentCodeList.value.length > 0
})

const filterVisible = ref(false)

// 加载数据
const loadData = async () => {
  if (!props.missionId) return Promise.resolve()
  
  loading.value = true
  try {
    const params = {
      mission_id: props.missionId,
      // 不分页，获取所有数据
      page: 1,
      per_page: 10000
    }
    
    // 添加搜索关键词
    if (keyword.value && keyword.value.trim()) {
      params.keyword = keyword.value.trim()
    }
    
    // 添加部门筛选
    if (selectedDepartmentCodeList.value.length > 0) {
      params.department_codes = selectedDepartmentCodeList.value.join(',')
    }
    
    const response = await getStatisticsMissionPoliticalExamList(params)
    
    if (response.code === 200) {
      // 字段映射
      const mappedData = (response.result?.data || []).map(item => ({
        ...item,
        department_name: item.name,
        total_count: item.political_exam,
        qualified_count: item.political_exam_passed,
        unqualified_count: item.political_exam_failed
      }))
      data.value = mappedData
    } else {
      throw new Error(response.message || "加载失败")
    }
  } catch (error) {
    console.error("加载数据失败:", error)
    uni.showToast({
      title: error.message || "加载失败",
      icon: "none",
    })
    throw error
  } finally {
    loading.value = false
  }
}

// 监听missionId变化
watch(
  () => props.missionId,
  (newVal) => {
    if (newVal) {
      loadData()
    }
  },
  { immediate: true }
)



// 刷新数据
const refresh = () => {
  return loadData()
}

const openFilter = () => {
  filterVisible.value = true
}

const closeFilter = () => {
  filterVisible.value = false
}

const confirm = () => {
  console.log(keyword.value)
  loadData()
}

const onDepartmentChange = (selectedCodes) => {
  selectedDepartmentCodeList.value = selectedCodes
  closeFilter()
  loadData()
}

// 组件挂载时加载部门树数据
onMounted(() => {
  loadDepartmentTree()
})

// 导出方法供父组件使用
defineExpose({
  refresh,
  loadData,
})
</script>

<template>
  <view class="data-content">
    <filterBar
      v-model:keyword="keyword"
      :filterActive="filterActive"
      placeholder="输入单位名称"
      @openFilter="openFilter"
      @closeFilter="closeFilter"
      @confirm="confirm"
    />
    <view v-if="loading" class="loading">加载中...</view>
    <up-table2 v-else :columns="columns" :data="data" />
  </view>

  <up-popup
    closeable
    :show="filterVisible"
    @close="closeFilter"
    :round="10"
    mode="bottom"
  >
    <departmentPicker
      title="筛选"
      :nodes="treeData"
      :selectedData="selectedDepartmentCodeList"
      @change="onDepartmentChange"
    />
  </up-popup>
</template>

<style lang="scss" scoped>
.data-content {
  .loading {
    text-align: center;
    padding: 40rpx;
    color: #999;
  }
}
</style>
