<script setup>
import { ref, computed, onMounted } from "vue"
import {
  personEducationMap,
  personGraduateMap,
  personPoliticalExamResultMap,
} from "@/packageTasks/utils/consts"
import { usePagination } from "@/utils/hooks"
import matchingFilter from "@/packageTasks/components/matchingFilter.vue"
import personnelItem from "@/packageTasks/components/personnelItem.vue"
import filterBar from "@/packageTasks/components/filterBar.vue"
import empty from "@/components/empty/empty.vue"
import {
  getMissionPersonList,
  createQueryBuilder,
} from "@/packageTasks/utils/api/missionPerson"

const props = defineProps({
  missionId: {
    type: [String, Number],
    default: 0,
  },
})

const list = ref([])

const keyword = ref("")
const filterVisible = ref(false)

const openFilter = () => {
  filterVisible.value = true
}

const closeFilter = () => {
  filterVisible.value = false
}

const filterOptions = ref([
  {
    title: "政考结果",
    key: "political_examination",
    options: Object.values(personPoliticalExamResultMap)
      .filter((item) => item.status != -1)
      .map((item) => item.text),
    value: Object.values(personPoliticalExamResultMap)
      .filter((item) => item.status != -1)
      .map((item) => item.status),
  },
  {
    title: "学业情况",
    key: "graduate",
    options: Object.values(personGraduateMap).map((item) => item.text),
    value: Object.values(personGraduateMap).map((item) => item.status),
  },
  {
    title: "文化程度",
    key: "education",
    options: Object.values(personEducationMap).map((item) => item.text),
    value: Object.values(personEducationMap).map((item) => item.status),
  },
  {
    title: "取得学位",
    key: "degree",
    options: ["学士", "硕士", "博士"],
    value: ["学士", "硕士", "博士"],
  },
])

const filterValues = ref({
  political_examination: [],
  graduate: [],
  education: [],
  degree: [],
})

const filterActive = computed(() => {
  return Object.values(filterValues.value).some((val) => val && val.length > 0)
})

const loadData = (options = {}) => {
  return new Promise((resolve, reject) => {
    const queryBuilder = createQueryBuilder(props.missionId).page(
      options.page || 1
    )

    queryBuilder.politicalExamination("1,2")

    if (keyword.value.trim()) {
      queryBuilder.keyword(keyword.value.trim())
    }

    Object.keys(filterValues.value).forEach((key) => {
      if (filterValues.value[key] && filterValues.value[key].length > 0) {
        queryBuilder.params[key] = filterValues.value[key].join(",")
      }
    })

    const params = queryBuilder.build()

    console.log("政考结果搜索参数:", params)
    console.log("关键词:", keyword.value)
    console.log("筛选条件:", filterValues.value)

    getMissionPersonList(params)
      .then((res) => {
        console.log("政考结果API响应:", res)
        if (res.code === 200) {
          list.value.push(...(res.result?.data || []))
          resolve(res)
        } else {
          reject(res)
        }
      })
      .catch(reject)
  })
}

const { page, loadPage, refresh, loadMoreStatus } = usePagination({
  loadData,
  list,
})

const onFilterChange = (value) => {
  filterValues.value = value
  filterVisible.value = false
  refresh()
}

const confirm = () => {
  refresh()
}

const handleClick = (item) => {
  console.log("点击人员:", item)
}

defineExpose({
  refresh,
  loadPage,
})

onMounted(() => {
  if (props.missionId) {
    loadPage()
  }
})
</script>

<template>
  <view class="people-content">
    <filterBar
      v-model:keyword="keyword"
      :filterActive="filterActive"
      placeholder="输入姓名或身份证号码搜索"
      @openFilter="openFilter"
      @closeFilter="closeFilter"
      @confirm="confirm"
    />

    <view class="list">
      <personnelItem
        v-for="item in list"
        :key="item.id"
        :item="item"
        @click="handleClick(item)"
      >
        <template #right>
          <view class="result">
            <text
              v-if="personPoliticalExamResultMap[item.political_examination]"
              :style="{
                color:
                  personPoliticalExamResultMap[item.political_examination]
                    .color,
              }"
            >
              {{
                personPoliticalExamResultMap[item.political_examination].text
              }}
            </text>
          </view>
        </template>
      </personnelItem>

      <empty
        v-if="list.length === 0 && loadMoreStatus === 'nomore'"
        theme="2"
        text="暂无数据"
      />

      <up-loadmore v-if="list.length" :status="loadMoreStatus" />
    </view>
  </view>

  <up-popup
    closeable
    :show="filterVisible"
    @close="closeFilter"
    :round="10"
    mode="bottom"
  >
    <matchingFilter
      :options="filterOptions"
      v-model="filterValues"
      @change="onFilterChange"
    />
  </up-popup>
</template>

<style lang="scss" scoped>
.list {
  .result {
    height: 92rpx;
    display: flex;
    flex-direction: column;
    align-items: flex-end;

    text {
      font-size: 24rpx;
    }
  }
}
</style>
