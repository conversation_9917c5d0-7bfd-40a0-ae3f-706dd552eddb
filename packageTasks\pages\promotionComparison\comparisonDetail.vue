<script setup>
import { ref, computed } from "vue"
import { onLoad } from "@dcloudio/uni-app"
import ReadMore from "@/pages/matching/components/readMore.vue"
import {
  updateMissionPersonDetail,
  getMissionPersonDetail,
} from "@/packageTasks/utils/api/missionPerson"
import {
  personIsEngineeringMap,
  personIntentionMap,
  personStudyFormMap,
  personPromoteCompareResultMap,
} from "@/packageTasks/utils/consts"

const fieldList = [
  {
    label: "民族",
    key: "nation",
  },
  {
    label: "政治面貌",
    key: "political_outlook",
  },
  {
    label: "所在区",
    key: "district_name",
  },
  {
    label: "户籍地(高校或街道)",
    key: "address",
  },
  {
    label: "通讯地址",
    key: "address",
  },
  {
    label: "所学专业",
    key: "major",
  },
  {
    label: "学习类型",
    key: "study_form",
    render: (value) => {
      return personStudyFormMap[value]?.text || "未知"
    },
  },
  {
    label: "是否理工类",
    key: "is_engineering",
    render: (value) => {
      return personIsEngineeringMap[value]?.text || "未知"
    },
  },
  {
    label: "是否有报名意向？",
    key: "intention",
    render: (value) => {
      return personIntentionMap[value]?.text || "未知"
    },
  },
]

const type = ref("school")
const id = ref("")

const peopleDetail = ref({})

const baseInfoExpand = ref(false)

// 解析手机号字段，用空格分隔
const phoneNumbers = computed(() => {
  if (!peopleDetail.value.phone) return []
  return peopleDetail.value.phone.split(" ").filter((phone) => phone.trim())
})

const call = (phoneNumber = null) => {
  const number = phoneNumber || peopleDetail.value.phone
  if (number) {
    uni.makePhoneCall({
      phoneNumber: number,
    })
  } else {
    uni.showToast({
      title: "无联系方式",
      icon: "none",
    })
  }
}

const result = computed(() => {
  return peopleDetail.value.promote_compare_result
})

const remark = ref("")

// 获取人员详情
const getPersonDetail = async () => {
  try {
    if (!id.value) {
      uni.showToast({
        title: "缺少人员ID",
        icon: "none",
      })
      return
    }

    const response = await getMissionPersonDetail(id.value)

    if (response.code === 200 && response.result) {
      peopleDetail.value = response.result

      // 初始化表单数据（如果已有宣传比对数据）
      if (
        response.result.promote_compare_result !== undefined &&
        response.result.promote_compare_result !== null &&
        response.result.promote_compare_result !== -1
      ) {
        result.value = response.result.promote_compare_result
      }

      if (response.result.promote_compare_remark) {
        remark.value = response.result.promote_compare_remark
      }
    }
  } catch (error) {
    console.error("获取人员详情失败:", error)
  }
}

const submit = async () => {
  try {
    if (!remark.value.trim()) {
      uni.showToast({
        title: "请填写备注",
        icon: "none",
      })
      return
    }

    // 构建请求数据
    const requestData = {
      promote_compare_remark: remark.value.trim(),
    }

    // 调用更新接口
    const response = await updateMissionPersonDetail(id.value, requestData)

    if (response.code === 200) {
      uni.showToast({
        title: "提交成功",
        icon: "success",
      })

      // 延迟返回上一页
      setTimeout(() => {
        uni.navigateBack()
      }, 1500)
    }
  } catch (error) {
    console.error("提交失败:", error)
    // request.js 中已经处理了错误提示，这里不需要额外处理
  }
}

onLoad((options) => {
  console.log(options)
  type.value = options.type
  id.value = options.id

  // 获取人员详情
  if (options.id) {
    getPersonDetail()
  }
})
</script>

<template>
  <navHeader title="宣传比对" bg="/static/image/bg.png"></navHeader>
  <view class="people-detail">
    <view class="name-call">
      <view class="name">
        {{ peopleDetail.name }}
        <image
          v-if="peopleDetail.sex"
          class="gender"
          :src="
            peopleDetail.sex == '男'
              ? '/static/image/boy.png'
              : '/static/image/girl.png'
          "
        ></image>
      </view>
      <image
        src="/static/image/call.svg"
        mode=""
        class="call"
        @click="call(phoneNumbers[0])"
      ></image>
    </view>

    <view class="base-info">
      <view class="base-info-expand">
        <view class="base-info-expand-title"> 基础信息 </view>

        <view
          class="base-info-expand-content"
          @click="baseInfoExpand = !baseInfoExpand"
        >
          <text>{{ baseInfoExpand ? "收起" : "展开" }}</text>

          <up-icon
            v-if="!baseInfoExpand"
            @click="baseInfoExpand = !baseInfoExpand"
            name="arrow-down"
          ></up-icon>
          <up-icon
            v-if="baseInfoExpand"
            @click="baseInfoExpand = !baseInfoExpand"
            name="arrow-up"
          ></up-icon>
        </view>
      </view>
      <read-more :expand="baseInfoExpand" :max-height="0">
        <view class="info">
          <view class="item" v-if="phoneNumbers.length > 1">
            <view class="label"> 其他手机号 </view>
            <view class="value">
              <view class="another-phone">
                <view
                  class="another-phone-item"
                  v-for="(phone, index) in phoneNumbers.slice(1)"
                  :key="index"
                >
                  <view class="another-phone-item-label">
                    手机号{{ index + 2 }}
                  </view>
                  <view class="another-phone-item-call">
                    <image
                      src="/static/image/call.svg"
                      mode=""
                      class="call"
                      @click="call(phone)"
                    ></image>
                  </view>
                </view>
              </view>
            </view>
          </view>

          <view class="item" v-for="field in fieldList" :key="field.key">
            <view class="label">
              {{ field.label }}
            </view>
            <view class="value">
              <template v-if="field.key === 'skill_certificate'">
                <view class="skill-certificate">
                  <view
                    class="skill-certificate-item"
                    v-for="item in peopleDetail[field.key]"
                    :key="item.name"
                  >
                    <view class="skill-certificate-item-name">
                      {{ item.name }}
                    </view>
                    <view class="skill-certificate-item-number">
                      {{ item.number }}
                    </view>
                  </view>
                </view>
              </template>
              <template v-else>
                {{
                  field.render
                    ? field.render(peopleDetail[field.key])
                    : peopleDetail[field.key]
                }}
              </template>
            </view>
          </view>
        </view>
      </read-more>
    </view>

    <view class="fields">
      <view class="field-item">
        <view class="field-item-label"> 比对结果 </view>
        <view class="field-item-value">
          <text
            v-if="result && personPromoteCompareResultMap[result]"
            :style="{ color: personPromoteCompareResultMap[result].color }"
          >
            {{ personPromoteCompareResultMap[result].text }}
          </text>
          <text v-else style="color: #999"> 暂无比对结果 </text>
        </view>
      </view>

      <view class="field-item">
        <view class="field-item-label"> 备注 </view>
        <view class="field-item-value">
          <up-textarea v-model="remark" placeholder="请输入备注" />
        </view>
      </view>
    </view>

    <view class="btn-wrapper">
      <button class="submit" @click="submit">提交</button>
    </view>
  </view>
</template>

<style>
page {
  background-color: #ffffff;
}
</style>

<style lang="scss" scoped>
.people-detail {
  display: flex;
  flex-direction: column;
  padding: 220rpx 45rpx 0;

  .name-call {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .name {
      display: flex;
      align-items: center;
      font-size: 52rpx;
      font-weight: bold;
      color: #21232c;

      .gender {
        width: 49rpx;
        height: 49rpx;
        margin-left: 22rpx;
      }
    }

    .call {
      width: 56rpx;
      height: 56rpx;
      border: 1rpx solid #c3cbd6;
      padding: 8rpx;
      border-radius: 50%;
    }
  }

  .base-info {
    margin-top: 64rpx;
    border-bottom: 2rpx solid #0ba133;

    .base-info-expand {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-bottom: 30rpx;

      .base-info-expand-title {
        font-size: 32rpx;
        color: #111111;
      }

      .base-info-expand-content {
        display: flex;
        align-items: center;
        gap: 4rpx;
        font-size: 26rpx;
        color: #989898;
      }
    }

    .info {
      .item {
        padding: 20rpx 0;
        border-bottom: 2rpx solid #f6f6f6;

        .label {
          font-size: 28rpx;
          color: #9ca3b5;
          margin-bottom: 7rpx;
        }

        .value {
          font-size: 32rpx;
          color: #111111;
        }

        .another-phone {
          display: flex;
          align-items: center;
          gap: 120rpx;

          .another-phone-item {
            display: flex;
            align-items: center;
            gap: 20rpx;

            .another-phone-item-label {
              font-size: 32rpx;
              color: #111111;
              margin-right: 50rpx;
            }

            .another-phone-item-call {
              display: flex;
              align-items: center;
              justify-content: center;
              border: 1rpx solid #c3cbd6;
              border-radius: 50%;
              overflow: hidden;

              .call {
                width: 24rpx;
                height: 24rpx;
              }
            }
          }
        }

        .skill-certificate {
          display: flex;
          flex-direction: column;
          gap: 10rpx;

          .skill-certificate-item {
            display: flex;
            flex-direction: column;
            gap: 10rpx;

            .skill-certificate-item-name {
              font-size: 32rpx;
              color: #111111;
            }

            .skill-certificate-item-number {
              font-size: 32rpx;
              color: #111111;
            }
          }
        }
      }
    }
  }

  .fields {
    margin-top: 30rpx;
    .field-item {
      margin-bottom: 30rpx;
      .field-item-label {
        font-size: 32rpx;
        color: #111111;
        margin-bottom: 10rpx;
      }
      .field-item-value {
      }
    }
  }

  .btn-wrapper {
    display: flex;
    justify-content: center;
    margin-top: 50rpx;
    margin-bottom: 40rpx;
    .submit {
      width: 702rpx;
      height: 100rpx;
      line-height: 100rpx;
      font-size: 34rpx;
      color: #ffffff;
      background: #577f49;
      box-shadow: 0rpx 20rpx 48rpx 1rpx rgba(87, 127, 73, 0.2);
      border-radius: 50rpx 50rpx 50rpx 50rpx;
    }
  }
}
</style>
