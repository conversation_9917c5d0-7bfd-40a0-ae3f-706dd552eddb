<script setup>
import { ref, computed, onMounted, watch } from "vue"
import {
  personPromoteCompareResultMap,
  personEducationMap,
  personGraduateMap,
  politicalOutlookArr,
  personStudyFormMap,
  personPriorityMap,
} from "@/packageTasks/utils/consts"
import { usePagination } from "@/utils/hooks"
import matchingFilter from "@/packageTasks/components/matchingFilter.vue"
import personnelItem from "@/packageTasks/components/personnelItem.vue"
import filterBar from "@/packageTasks/components/filterBar.vue"
import empty from "@/components/empty/empty.vue"
import {
  getMissionPersonList,
  createQueryBuilder,
} from "@/packageTasks/utils/api/missionPerson"

const props = defineProps({
  missionId: {
    type: [String, Number],
    default: 0,
  },
})

const list = ref([])

const keyword = ref("")
const filterVisible = ref(false)

const openFilter = () => {
  filterVisible.value = true
}

const closeFilter = () => {
  filterVisible.value = false
}

const filterOptions = ref([
  {
    title: "比对结果",
    key: "promote_compare_result",
    options: Object.values(personPromoteCompareResultMap).map(
      (item) => item.text
    ),
    value: Object.values(personPromoteCompareResultMap).map(
      (item) => item.status
    ),
  },
  {
    title: "年龄",
    key: "age",
    options: ["18", "19", "20"],
    value: [18, 19, 20],
  },
  {
    title: "政治面貌",
    key: "politics",
    options: politicalOutlookArr,
    value: politicalOutlookArr.map((item) => item),
  },
  {
    title: "学业情况",
    key: "graduate",
    options: Object.values(personGraduateMap).map((item) => item.text),
    value: Object.values(personGraduateMap).map((item) => item.status),
  },
  {
    title: "学习类型",
    key: "study_form",
    options: Object.values(personStudyFormMap).map((item) => item.text),
    value: Object.values(personStudyFormMap).map((item) => item.status),
  },
  {
    title: "文化程度",
    key: "education",
    options: Object.values(personEducationMap).map((item) => item.text),
    value: Object.values(personEducationMap).map((item) => item.status),
  },
  {
    title: "是否有高级以上职业技能等级证书(职业资格证书)",
    key: "skillCertificate",
    options: ["有", "没有"],
    value: [1, 0],
  },
  {
    title: "取得学位",
    key: "degree",
    options: ["学士", "硕士", "博士"],
    value: ["学士", "硕士", "博士"],
  },
  {
    title: "是否联系",
    key: "isContact",
    options: ["是", "否"],
    value: [1, 0],
  },
  {
    title: "优先条件",
    key: "priority",
    options: Object.values(personPriorityMap).map((item) => item.text),
    value: Object.values(personPriorityMap).map((item) => item.status),
  },
])

const filterValues = ref({
  promote_compare_result: [],
  age: [],
  politics: [],
  graduate: [],
  study_form: [],
  education: [],
  skillCertificate: [],
  degree: [],
  isContact: [],
  priority: [],
})

const filterActive = computed(() => {
  return Object.values(filterValues.value).some((val) => val && val.length > 0)
})

// API调用逻辑
const loadData = (options = {}) => {
  return new Promise((resolve, reject) => {
    // 使用查询构建器构建参数
    const queryBuilder = createQueryBuilder(props.missionId).page(
      options.page || 1
    )

    queryBuilder.promoteCompareResult("1,2,3,4,5")

    // 添加搜索关键词
    if (keyword.value.trim()) {
      queryBuilder.keyword(keyword.value.trim())
    }

    // 添加筛选条件
    Object.keys(filterValues.value).forEach((key) => {
      if (filterValues.value[key] && filterValues.value[key].length > 0) {
        // 根据不同筛选条件转换参数
        switch (key) {
          default:
            queryBuilder.params[key] = filterValues.value[key].join(",")
            break
        }
      }
    })

    const params = queryBuilder.build()

    // 调试信息
    console.log("比对详情搜索参数:", params)
    console.log("关键词:", keyword.value)
    console.log("筛选条件:", filterValues.value)

    getMissionPersonList(params)
      .then((res) => {
        console.log("比对详情API响应:", res)
        if (res.code === 200) {
          list.value.push(...(res.result?.data || []))
          resolve(res)
        } else {
          reject(res)
        }
      })
      .catch(reject)
  })
}

const { page, loadPage, refresh, loadMoreStatus } = usePagination({
  loadData,
  list,
})

const onFilterChange = (value) => {
  filterValues.value = value
  filterVisible.value = false
  refresh() // 应用筛选后刷新列表
}

const confirm = () => {
  refresh() // 搜索后刷新列表
}

const callPhone = (phone, id) => {
  if (!phone) {
    uni.showToast({
      title: "暂无联系电话",
      icon: "none",
    })
    return
  }
  uni.makePhoneCall({
    phoneNumber: phone,
  })
}

const handleClick = (item) => {
  uni.navigateTo({
    url: `/packageTasks/pages/promotionComparison/comparisonDetail?id=${item.id}`,
  })
}

// 向父组件暴露方法
defineExpose({
  refresh,
  loadPage,
})

// 组件挂载时加载数据
onMounted(() => {
  if (props.missionId) {
    loadPage()
  }
})
</script>

<template>
  <view class="people-content">
    <filterBar
      v-model:keyword="keyword"
      :filterActive="filterActive"
      placeholder="输入姓名或身份证号码搜索"
      @openFilter="openFilter"
      @closeFilter="closeFilter"
      @confirm="confirm"
    />

    <view class="list">
      <personnelItem
        v-for="item in list"
        :key="item.id"
        :item="item"
        @click="handleClick(item)"
      >
        <template #extra-info>
          <text
            v-if="personPromoteCompareResultMap[item.promote_compare_result]"
            :style="{
              color:
                personPromoteCompareResultMap[item.promote_compare_result]
                  .color,
            }"
          >
            {{
              personPromoteCompareResultMap[item.promote_compare_result].text
            }}
          </text>
        </template>
        <template #right>
          <image
            src="/static/image/call.svg"
            mode=""
            style="width: 56rpx; height: 56rpx"
            @click.stop="callPhone(item.phone, item.id)"
          ></image>
        </template>
      </personnelItem>

      <empty
        v-if="list.length === 0 && loadMoreStatus === 'nomore'"
        theme="2"
        text="暂无数据"
      />

      <up-loadmore v-if="list.length" :status="loadMoreStatus" />
    </view>
  </view>

  <up-popup
    closeable
    :show="filterVisible"
    @close="closeFilter"
    :round="10"
    mode="bottom"
  >
    <matchingFilter
      :options="filterOptions"
      v-model="filterValues"
      @change="onFilterChange"
    />
  </up-popup>
</template>

<style lang="scss" scoped>
.people-content {
  .list {
    margin-top: 20rpx;
  }
}
</style>
