<script setup>
import { ref, watch } from "vue"
import { getStatisticsMissionPromoteCompareList } from "@/packageTasks/utils/api/missionPerson"

const props = defineProps({
  missionId: {
    type: [String, Number],
    default: "",
  },
})

const columns = ref([
  {
    title: "单位",
    key: "name",
    width: "220rpx",
    fixed: "left",
  },
  {
    title: "有意向",
    key: "intention_yes",
    width: "120rpx",
  },
  {
    title: "已报名",
    key: "sign_count",
    width: "120rpx",
  },
  {
    title: "有意向已报名",
    key: "intention_sign",
    width: "160rpx",
  },
])

const data = ref([])
const loading = ref(false)

// 加载数据
const loadData = async () => {
  if (!props.missionId) return
  
  loading.value = true
  try {
    const params = {
      mission_id: props.missionId,
      // 不分页，获取所有数据
      page: 1,
      per_page: 10000
    }
    
    const response = await getStatisticsMissionPromoteCompareList(params)
    
    if (response.code === 200) {
      data.value = response.result?.data || []
    } else {
      throw new Error(response.message || "加载失败")
    }
  } catch (error) {
    console.error("加载数据失败:", error)
    uni.showToast({
      title: error.message || "加载失败",
      icon: "none",
    })
  } finally {
    loading.value = false
  }
}

// 监听missionId变化
watch(
  () => props.missionId,
  (newVal) => {
    if (newVal) {
      loadData()
    }
  },
  { immediate: true }
)

// 刷新数据
const refresh = () => {
  loadData()
}

// 导出方法供父组件使用
defineExpose({
  refresh,
  loadData,
})
</script>

<template>
  <view class="data-content">
    <view v-if="loading" class="loading">加载中...</view>
    <up-table2
      v-else
      :columns="columns"
      :data="data"
    />
  </view>
</template>

<style lang="scss" scoped>
.data-content {
  .loading {
    text-align: center;
    padding: 40rpx;
    color: #999;
  }
}
</style>
