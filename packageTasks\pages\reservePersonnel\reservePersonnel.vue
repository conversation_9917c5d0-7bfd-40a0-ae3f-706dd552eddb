<script setup>
import personnelItem from "@/packageTasks/components/personnelItem.vue"
import { ref, computed, onMounted } from "vue"
import { onLoad, onPullDownRefresh, onReachBottom } from "@dcloudio/uni-app"
import {
  personEducationMap,
  personGraduateMap,
  personPreTypeMap,
} from "@/packageTasks/utils/consts"
import { usePagination } from "@/utils/hooks"
import filterBar from "@/packageTasks/components/filterBar.vue"
import matchingFilter from "@/packageTasks/components/matchingFilter.vue"
import empty from "@/components/empty/empty.vue"
import {
  getMissionPersonList,
  createQueryBuilder,
} from "@/packageTasks/utils/api/missionPerson"
import { useUserStore } from "@/store/user"

const missionId = ref(0)

const list = ref([])

const keyword = ref("")
const filterVisible = ref(false)

const userStore = useUserStore()
const roleSlugs = computed(() => userStore.roleSlugs)

const filterOptions = ref([
  // {
  //   title: "预储类型",
  //   key: "pre_type",
  //   options: Object.values(personPreTypeMap)
  //     .filter((item) => item.status != -1)
  //     .map((item) => item.text),
  //   value: Object.values(personPreTypeMap)
  //     .filter((item) => item.status != -1)
  //     .map((item) => item.status),
  // },
  {
    title: "学业情况",
    key: "graduate",
    options: Object.values(personGraduateMap).map((item) => item.text),
    value: Object.values(personGraduateMap).map((item) => item.status),
  },
  {
    title: "文化程度",
    key: "education",
    options: Object.values(personEducationMap).map((item) => item.text),
    value: Object.values(personEducationMap).map((item) => item.status),
  },
  {
    title: "取得学位",
    key: "degree",
    options: ["学士", "硕士", "博士"],
    value: ["学士", "硕士", "博士"],
  },
])

const filterValues = ref({
  pre_type: [],
  graduate: [],
  education: [],
  degree: [],
  department: [],
})

const filterActive = computed(() => {
  return Object.values(filterValues.value).some((val) => val && val.length > 0)
})

const loadData = (options = {}) => {
  return new Promise((resolve, reject) => {
    console.log("开始加载预储人员数据...")
    console.log("missionId:", missionId.value)

    if (!missionId.value) {
      console.error("missionId 为空，无法查询")
      reject(new Error("missionId 为空"))
      return
    }

    const queryBuilder = createQueryBuilder(missionId.value).page(
      options.page || 1
    )

    queryBuilder.preType("1,2")

    if (keyword.value.trim()) {
      queryBuilder.keyword(keyword.value.trim())
    }

    Object.keys(filterValues.value).forEach((key) => {
      if (filterValues.value[key] && filterValues.value[key].length > 0) {
        if (key === "department") {
          queryBuilder.params["department_codes"] =
            filterValues.value[key].join(",")
        } else {
          queryBuilder.params[key] = filterValues.value[key].join(",")
        }
      }
    })

    const params = queryBuilder.build()

    console.log("预储人员搜索参数:", params)
    console.log("关键词:", keyword.value)
    console.log("筛选条件:", filterValues.value)

    getMissionPersonList(params)
      .then((res) => {
        console.log("预储人员API响应:", res)
        console.log("返回数据条数:", res.result?.data?.length || 0)
        if (res.code === 200) {
          list.value.push(...(res.result?.data || []))
          resolve(res)
        } else {
          console.error("API返回错误:", res)
          reject(res)
        }
      })
      .catch((error) => {
        console.error("API调用失败:", error)
        reject(error)
      })
  })
}

const { page, loadPage, refresh, loadMoreStatus } = usePagination({
  loadData,
  list,
})

const openFilter = () => {
  filterVisible.value = true
}

const closeFilter = () => {
  filterVisible.value = false
}

const onFilterChange = (value) => {
  console.log("筛选条件变化:", value)
  console.log("部门筛选数据:", value.department)
  filterValues.value = value
  filterVisible.value = false
  refresh()
}

const confirm = () => {
  refresh()
}

const handleClick = (item) => {
  console.log("点击人员:", item)
}

onLoad((options) => {
  console.log("onLoad options:", options)
  missionId.value = parseInt(options.missionId) || 0
  console.log("设置 missionId:", missionId.value)
})

onPullDownRefresh(() => {
  refresh()
    .then(() => {
      uni.stopPullDownRefresh()
    })
    .catch(() => {
      uni.stopPullDownRefresh()
    })
})

onReachBottom(() => {
  loadPage()
})

onMounted(() => {
  if (missionId.value) {
    loadPage()
  }
})
</script>

<template>
  <navHeader title="预储人员" bg="/static/image/bg.png"></navHeader>
  <view class="page-reserve-personnel">
    <filterBar
      v-model:keyword="keyword"
      :filterActive="filterActive"
      placeholder="输入姓名或身份证号码搜索"
      @openFilter="openFilter"
      @closeFilter="closeFilter"
      @confirm="confirm"
    />
    <view class="list">
      <personnelItem
        v-for="item in list"
        :key="item.id"
        :item="item"
        @click="handleClick(item)"
      >
        <!-- <template #right>
          <view class="result">
            <text
              v-if="personPreTypeMap[item.pre_type]"
              :style="{
                color: personPreTypeMap[item.pre_type].color,
              }"
            >
              {{ personPreTypeMap[item.pre_type].text }}
            </text>
          </view>
        </template> -->
      </personnelItem>

      <empty
        v-if="list.length === 0 && loadMoreStatus === 'nomore'"
        theme="2"
        text="暂无数据"
      />

      <up-loadmore v-if="list.length" :status="loadMoreStatus" />
    </view>
  </view>

  <up-popup
    closeable
    :show="filterVisible"
    @close="closeFilter"
    :round="10"
    mode="bottom"
  >
    <matchingFilter
      :options="filterOptions"
      v-model="filterValues"
      :showDepartment="roleSlugs.includes('district_admin')"
      @change="onFilterChange"
    />
  </up-popup>
</template>

<style lang="scss" scoped>
.page-reserve-personnel {
  padding: 24rpx;
  padding-top: 220rpx;

  .list {
    .result {
      height: 92rpx;
      display: flex;
      flex-direction: column;
      align-items: flex-end;

      text {
        font-size: 24rpx;
      }
    }
  }
}
</style>
