<script setup>
import personnelItem from "/packageTasks/components/personnelItem.vue"
import { ref, computed, onMounted } from "vue"
import filterBar from "@/packageTasks/components/filterBar.vue"
import matchingFilter from "@/packageTasks/components/matchingFilter.vue"
import request from "@/utils/request"
import { useUserStore } from "@/store/user"

const userStore = useUserStore()

const list = ref([
  {
    id: 1,
    name: "张三",
    sex: "男",
    age: 20,
    id_card: "123456789012345678",
    education: 1,
    graduate: 1,
    is_engineering: 1,
    priority: 1,
  },
  {
    id: 2,
    name: "李四",
    sex: "女",
    age: 20,
    id_card: "123456789012345678",
    education: 1,
    graduate: 1,
    is_engineering: 1,
    priority: 1,
  },
])

const keyword = ref("")
const filterVisible = ref(false)

const treeData = ref([])
const departmentLoading = ref(false)

// 加载部门树数据
const loadDepartmentTree = async () => {
  departmentLoading.value = true
  try {
    const response = await request({
      url: "/mini/department_tree",
      method: "get",
      data: {
        p_code: userStore.userInfo.district_code,
      },
    })
    treeData.value = response.result || []
  } catch (error) {
    console.error('加载部门树失败:', error)
    uni.showToast({
      title: '加载部门数据失败',
      icon: 'none',
      duration: 2000
    })
  } finally {
    departmentLoading.value = false
  }
}
const selectedDepartmentCodeList = ref([])

const filterActive = computed(() => {
  return selectedDepartmentCodeList.value.length > 0
})

const openFilter = () => {
  filterVisible.value = true
}

const closeFilter = () => {
  filterVisible.value = false
}

const confirm = () => {
  console.log(keyword.value)
}

const goDetail = (item) => {
  uni.navigateTo({
    url: `/packageTasks/pages/returnPersonnel/returnPersonnelDetail?id=${item.id}`,
  })
}

const onDepartmentChange = (data) => {
  selectedDepartmentCodeList.value = data
}

// 组件挂载时加载部门树数据
onMounted(() => {
  loadDepartmentTree()
})
</script>

<template>
  <navHeader title="退兵人员" bg="/static/image/bg.png"></navHeader>
  <view class="page-return-personnel">
    <filterBar
      v-model:keyword="keyword"
      :filterActive="filterActive"
      placeholder="输入姓名或身份证号码搜索"
      @openFilter="openFilter"
      @closeFilter="closeFilter"
      @confirm="confirm"
    />
    <view class="list">
      <personnelItem
        v-for="item in list"
        :key="item.id"
        :item="item"
        @click="goDetail(item)"
      />
    </view>
  </view>

  <up-popup
    closeable
    :show="filterVisible"
    @close="closeFilter"
    :round="10"
    mode="bottom"
  >
    <departmentPicker
      title="筛选"
      :nodes="treeData"
      :selectedData="selectedDepartmentCodeList"
      @change="onDepartmentChange"
    />
  </up-popup>
</template>

<style lang="scss" scoped>
.page-return-personnel {
  padding: 24rpx;
  padding-top: 220rpx;

  .list {
  }
}
</style>
