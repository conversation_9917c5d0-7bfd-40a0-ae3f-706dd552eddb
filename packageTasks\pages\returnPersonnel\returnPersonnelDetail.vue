<script setup>
import { ref, computed } from "vue"
import { onLoad } from "@dcloudio/uni-app"
import personBaseInfo from "@/packageTasks/components/personBaseInfo.vue"
import { getMissionPersonDetail } from "@/packageTasks/utils/api/missionPerson"
import dayjs from "dayjs"

// 人员详情
const peopleDetail = ref({})

// 当前人员 id
const id = ref("")

// 获取人员详情
const getPersonDetail = async () => {
  if (!id.value) return
  try {
    const res = await getMissionPersonDetail(id.value)
    if (res.code === 200 && res.result) {
      peopleDetail.value = res.result
    }
  } catch (err) {
    console.error("获取退兵详情失败", err)
  }
}

// 进入页面加载数据
onLoad((options) => {
  id.value = options.id || ""
  getPersonDetail()
})

const baseInfoExpand = ref(false)

// 格式化时间 YYYY/MM/DD HH:mm
const formatDateTime = (val) => {
  if (!val) return ""
  return dayjs(val).format("YYYY/MM/DD HH:mm")
}
</script>

<template>
  <navHeader title="退兵详情" bg="/static/image/bg.png"></navHeader>
  <view class="people-detail">
    <personBaseInfo
      v-model:baseInfoExpand="baseInfoExpand"
      :peopleDetail="peopleDetail"
    />

    <view class="fields">
      <view class="field-item">
        <view class="field-item-label">
          退兵原因
          <text v-if="peopleDetail.return_reason_at" class="time">
            {{ formatDateTime(peopleDetail.return_reason_at) }}
          </text>
        </view>
        <view class="field-item-value">
          <text :style="{ color: '#EA2B2B' }">
            {{ peopleDetail.return_reason || "暂无原因" }}
          </text>
        </view>
      </view>
    </view>
  </view>
</template>

<style>
page {
  background-color: #ffffff;
}
</style>

<style lang="scss" scoped>
.people-detail {
  display: flex;
  flex-direction: column;
  padding: 220rpx 45rpx 0;

  .fields {
    margin-top: 30rpx;
    .field-item {
      margin-bottom: 30rpx;
      .field-item-label {
        font-size: 32rpx;
        color: #111111;
        margin-bottom: 10rpx;

        .time {
          font-size: 24rpx;
          color: #9ca3b5;
          margin-left: 8rpx;
        }
      }
      .field-item-value {
      }
    }
  }

  .btn-wrapper {
    display: flex;
    justify-content: center;
    margin-top: 50rpx;
    margin-bottom: 40rpx;
    .submit {
      width: 702rpx;
      height: 100rpx;
      line-height: 100rpx;
      font-size: 34rpx;
      color: #ffffff;
      background: #577f49;
      box-shadow: 0rpx 20rpx 48rpx 1rpx rgba(87, 127, 73, 0.2);
      border-radius: 50rpx 50rpx 50rpx 50rpx;
    }
  }
}
</style>
