<script setup>
import { ref, computed, onMounted } from "vue"
import { onLoad, onPullDownRefresh } from "@dcloudio/uni-app"
import request from "@/utils/request"

const missionId = ref("")
const keyword = ref("")
const list = ref([])
const loading = ref(false)

// 表格列配置
const columns = ref([
  {
    title: "单位",
    key: "name",
    width: 200,
    align: "left",
  },
  {
    title: "任务数",
    key: "task_num",
    width: 100,
    align: "center",
  },
  {
    title: "毕业生任务数",
    key: "task_graduate_num",
    width: 120,
    align: "center",
  },
  {
    title: "大学生征集指导比例",
    key: "before_graduate_guide_ratio",
    width: 150,
    align: "center",
  },
  {
    title: "大学毕业生征集指导比例",
    key: "graduate_guide_ratio",
    width: 170,
    align: "center",
  },
])

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    const params = {
      mission_id: missionId.value,
      per_page: 10000, // 设置大的值以获取所有数据
    }

    // 添加搜索关键词
    if (keyword.value.trim()) {
      params.keyword = keyword.value.trim()
    }

    const response = await request({
      url: "/mini/statistics/mission_task_num_list",
      method: "GET",
      data: params,
    })

    if (response.code === 200) {
      list.value = response.result?.data || []
    } else {
      throw new Error(response.message || "加载失败")
    }
  } catch (error) {
    console.error("加载任务数数据失败:", error)
    uni.showToast({
      title: error.message || "加载失败",
      icon: "none",
    })
    list.value = []
  } finally {
    loading.value = false
  }
}

// 搜索确认
const confirm = () => {
  loadData()
}

// 处理表格数据显示
const tableData = computed(() => {
  return list.value.map((item) => ({
    ...item,
    task_num: item.task_num || 0,
    task_graduate_num: item.task_graduate_num || 0,
    before_graduate_guide_ratio: item.before_graduate_guide_ratio
      ? `${item.before_graduate_guide_ratio}%`
      : "0%",
    graduate_guide_ratio: item.graduate_guide_ratio
      ? `${item.graduate_guide_ratio}%`
      : "0%",
  }))
})

// 表格单元格样式函数
const cellStyleFunc = (scope) => {
  return {
    padding: "20rpx 10rpx",
  }
}

onLoad((options) => {
  missionId.value = options.missionId
})

onMounted(() => {
  if (missionId.value) {
    loadData()
  }
})

onPullDownRefresh(() => {
  loadData()
    .then(() => {
      uni.stopPullDownRefresh()
    })
    .catch(() => {
      uni.stopPullDownRefresh()
    })
})
</script>

<template>
  <navHeader title="任务数" bg="/static/image/bg.png"></navHeader>
  <view class="page-task-number">
    <view class="search-bar">
      <up-search
        :show-action="false"
        placeholder="输入单位名称搜索"
        placeholderColor="#bdc4ce"
        searchIconColor="#bdc4ce"
        v-model="keyword"
        bgColor="#ffffff"
        :searchIconSize="28"
        :inputStyle="{ height: '80rpx' }"
        @search="confirm"
        @clear="confirm"
      ></up-search>
    </view>

    <view class="table-container">
      <view v-if="loading" class="loading">加载中...</view>
      <up-table2
        v-else
        :columns="columns"
        :data="tableData"
        :cellStyle="cellStyleFunc"
      />
    </view>
  </view>
</template>

<style lang="scss" scoped>
.page-task-number {
  padding: 24rpx;
  padding-top: 220rpx;

  .search-bar {
    margin-bottom: 35rpx;
  }

  .table-container {
    background: rgba(255, 255, 255, 0.94);
    box-shadow: 0rpx 6rpx 13rpx 0rpx rgba(0, 0, 0, 0.04);
    border-radius: 20rpx;
    border: 2rpx solid #ffffff;
    padding: 20rpx;

    .loading {
      text-align: center;
      padding: 40rpx;
      color: #999;
    }
  }
}
</style>
