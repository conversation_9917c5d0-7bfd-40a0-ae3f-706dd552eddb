import request from "@/utils/request"
import {
  personSexMap,
  personEducationMap,
  personStudyFormMap,
  personPriorityMap,
  personPreTypeMap,
  personIsBeijingMap,
  personIsEngineeringMap,
  personGraduateMap,
  personTypeMap,
  personIntentionMap,
  personSignStatusMap,
  personPromoteCompareResultMap,
  personPoliticalExamResultMap,
} from "@/packageTasks/utils/consts"

/**
 * 获取任务人员列表
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export const getMissionPersonList = (params = {}) => {
  return request({
    url: "/mini/mission_person",
    method: "GET",
    data: params,
  })
}

/**
 * 获取任务人员详情
 * @param {Number|String} id 人员ID
 * @returns {Promise}
 */
export const getMissionPersonDetail = (id) => {
  return request({
    url: `/mini/mission_person/${id}`,
    method: "GET",
  })
}

/**
 * 任务人员详情修改
 * @param {Number|String} id 人员ID
 * @returns {Promise}
 */
export const updateMissionPersonDetail = (id, data) => {
  return request({
    url: `/mini/mission_person/${id}`,
    method: "PUT",
    data,
  })
}

/**
 * 任务人员参数构建器
 * 帮助构建标准的查询参数
 */
export class MissionPersonQueryBuilder {
  constructor() {
    this.params = {}
  }

  // 设置任务ID
  missionId(id) {
    if (id) {
      this.params.mission_id = id
    }
    return this
  }

  // 设置人员类型 (1=高校人员, 2=社会适龄青年)
  type(type) {
    if (type === "school" || type === 1) {
      this.params.type = 1
    } else if (type === "social" || type === 2) {
      this.params.type = 2
    }
    return this
  }

  // 设置姓名搜索
  name(name) {
    if (name && name.trim()) {
      this.params.name = name.trim()
    }
    return this
  }

  // 设置身份证号搜索
  idCard(idCard) {
    if (idCard && idCard.trim()) {
      this.params.id_card = idCard.trim()
    }
    return this
  }

  // 设置关键词搜索
  keyword(keyword) {
    if (keyword && keyword.trim()) {
      this.params.keyword = keyword.trim()
    }
    return this
  }

  // 设置电话搜索
  phone(phone) {
    if (phone && phone.trim()) {
      this.params.phone = phone.trim()
    }
    return this
  }

  // 设置年龄筛选 - 支持逗号分隔的多个值
  age(age) {
    if (age) {
      this.params.age = age
    }
    return this
  }

  // 设置年龄范围
  ageRange(min, max) {
    if (min !== undefined && min !== null) {
      this.params.age_min = min
    }
    if (max !== undefined && max !== null) {
      this.params.age_max = max
    }
    return this
  }

  // 设置性别 (1=男, 2=女)
  sex(sex) {
    if (sex) {
      this.params.sex = sex
    }
    return this
  }

  // 设置意向状态 (1=有意向, -1=无意向)
  intention(intention) {
    if (intention !== undefined && intention !== null) {
      this.params.intention = intention
    }
    return this
  }

  // 设置学历 - 支持逗号分隔的多个值
  education(education) {
    if (education) {
      this.params.education = education
    }
    return this
  }

  // 设置学业情况 (1=在校生, 2=应届毕业生, 3=毕业生, 4=毕业班生)
  graduate(graduate) {
    if (graduate) {
      this.params.graduate = graduate
    }
    return this
  }

  // 设置政治面貌 - 支持逗号分隔的多个值
  politicalOutlook(outlook) {
    if (outlook) {
      this.params.political_outlook = outlook
    }
    return this
  }

  // 设置学习形式 - 支持逗号分隔的多个值
  studyForm(form) {
    if (form) {
      this.params.study_form = form
    }
    return this
  }

  // 设置学位
  degree(degree) {
    if (degree) {
      this.params.degree = degree
    }
    return this
  }

  // 设置优先级 (1-5类)
  priority(priority) {
    if (priority) {
      this.params.priority = priority
    }
    return this
  }

  // 设置预储类型 (1=双合格, 2=教育)
  preType(preType) {
    if (preType !== undefined && preType !== null) {
      this.params.pre_type = preType
    }
    return this
  }

  // 设置是否京籍 (1=京籍, 2=非京籍)
  isBeijing(isBeijing) {
    if (isBeijing !== undefined && isBeijing !== null) {
      this.params.is_beijing = isBeijing
    }
    return this
  }

  // 设置是否理工类 (1=理工类, 2=非理工类)
  isEngineering(isEngineering) {
    if (isEngineering !== undefined && isEngineering !== null) {
      this.params.is_engineering = isEngineering
    }
    return this
  }

  // 设置学制
  educationSystem(system) {
    if (system) {
      this.params.education_system = system
    }
    return this
  }

  // 设置录取年份
  admissionYear(year) {
    if (year) {
      this.params.admission_year = year
    }
    return this
  }

  // 设置毕业年份
  graduationYear(year) {
    if (year) {
      this.params.graduation_year = year
    }
    return this
  }

  // 设置专业
  major(major) {
    if (major && major.trim()) {
      this.params.major = major.trim()
    }
    return this
  }

  // 设置民族
  nation(nation) {
    if (nation && nation.trim()) {
      this.params.nation = nation.trim()
    }
    return this
  }

  // 设置区域代码
  districtCode(code) {
    if (code) {
      this.params.district_code = code
    }
    return this
  }

  // 设置区域名称
  districtName(name) {
    if (name && name.trim()) {
      this.params.district_name = name.trim()
    }
    return this
  }

  // 设置街道代码
  streetCode(code) {
    if (code) {
      this.params.street_code = code
    }
    return this
  }

  // 设置街道名称
  streetName(name) {
    if (name && name.trim()) {
      this.params.street_name = name.trim()
    }
    return this
  }

  // 设置学校代码
  schoolCode(code) {
    if (code) {
      this.params.school_code = code
    }
    return this
  }

  // 设置学校名称
  schoolName(name) {
    if (name && name.trim()) {
      this.params.school_name = name.trim()
    }
    return this
  }

  // 设置地址
  address(address) {
    if (address && address.trim()) {
      this.params.address = address.trim()
    }
    return this
  }

  // 设置征兵网报名状态 (1=已报名)
  sign(sign) {
    if (sign !== undefined && sign !== null) {
      this.params.sign = sign
    }
    return this
  }

  // 设置宣传比对结果
  promoteCompareResult(result) {
    if (result !== undefined && result !== null) {
      this.params.promote_compare_result_mini = result
    }
    return this
  }

  // 设置体检状态
  physicalCheck(status) {
    if (status !== undefined && status !== null) {
      this.params.physical_check = status
    }
    return this
  }

  // 设置体检复查状态
  physicalRecheck(status) {
    if (status !== undefined && status !== null) {
      this.params.physical_recheck = status
    }
    return this
  }

  // 设置体检抽查状态
  physicalSpotCheck(status) {
    if (status !== undefined && status !== null) {
      this.params.physical_spot_check = status
    }
    return this
  }

  // 设置体检结果
  physicalResult(result) {
    if (result !== undefined && result !== null) {
      this.params.physical_result = result
    }
    return this
  }

  // 设置政考结果
  politicalExamination(result) {
    if (result !== undefined && result !== null) {
      this.params.political_examination = result
    }
    return this
  }

  // 设置役前教育结果
  educationResult(result) {
    if (result !== undefined && result !== null) {
      this.params.education_result = result
    }
    return this
  }

  // 设置时间范围筛选
  createdAtRange(start, end) {
    if (start) {
      this.params.created_at_start = start
    }
    if (end) {
      this.params.created_at_end = end
    }
    return this
  }

  // 设置意向时间范围
  intentionAtRange(start, end) {
    if (start) {
      this.params.intention_at_start = start
    }
    if (end) {
      this.params.intention_at_end = end
    }
    return this
  }

  // 设置排序
  orderBy(field, direction = "desc") {
    const allowedFields = [
      "id",
      "created_at",
      "updated_at",
      "name",
      "age",
      "priority",
      "intention_at",
      "promote_compare_at",
      "physical_check_at",
    ]

    if (allowedFields.includes(field)) {
      this.params.order_by = field
      this.params.order_direction =
        direction.toLowerCase() === "asc" ? "asc" : "desc"
    }
    return this
  }

  // 设置分页
  page(page, pageSize = 15) {
    if (page > 0) {
      this.params.page = page
    }
    if (pageSize > 0) {
      this.params.page_size = pageSize
    }
    return this
  }

  // 清空参数
  clear() {
    this.params = {}
    return this
  }

  // 获取当前参数
  getParams() {
    return { ...this.params }
  }

  // 构建最终参数
  build() {
    return { ...this.params }
  }
}

/**
 * 实用工具方法
 */

/**
 * 验证身份证号格式
 * @param {String} idCard 身份证号
 * @returns {Boolean}
 */
export const validateIdCard = (idCard) => {
  if (!idCard) return false
  const pattern =
    /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/
  return pattern.test(idCard.toString())
}

/**
 * 验证手机号格式
 * @param {String} phone 手机号
 * @returns {Boolean}
 */
export const validatePhone = (phone) => {
  if (!phone) return false
  const pattern = /^1[3-9]\d{9}$/
  return pattern.test(phone.toString())
}

/**
 * 根据身份证号获取年龄
 * @param {String} idCard 身份证号
 * @returns {Number|null}
 */
export const getAgeFromIdCard = (idCard) => {
  if (!validateIdCard(idCard)) return null

  const birth = idCard.substring(6, 14)
  const birthYear = parseInt(birth.substring(0, 4))
  const birthMonth = parseInt(birth.substring(4, 6))
  const birthDay = parseInt(birth.substring(6, 8))

  const today = new Date()
  const age = today.getFullYear() - birthYear

  if (
    today.getMonth() + 1 < birthMonth ||
    (today.getMonth() + 1 === birthMonth && today.getDate() < birthDay)
  ) {
    return age - 1
  }

  return age
}

/**
 * 根据身份证号获取性别
 * @param {String} idCard 身份证号
 * @returns {Number|null} 1=男, 2=女
 */
export const getSexFromIdCard = (idCard) => {
  if (!validateIdCard(idCard)) return null

  const sexCode = parseInt(idCard.charAt(16))
  return sexCode % 2 === 1 ? personSexMap[1]?.status : personSexMap[2]?.status
}

/**
 * 根据身份证号获取出生日期
 * @param {String} idCard 身份证号
 * @returns {String|null} YYYY-MM-DD格式
 */
export const getBirthdayFromIdCard = (idCard) => {
  if (!validateIdCard(idCard)) return null

  const birth = idCard.substring(6, 14)
  const year = birth.substring(0, 4)
  const month = birth.substring(4, 6)
  const day = birth.substring(6, 8)

  return `${year}-${month}-${day}`
}

/**
 * 身份证号脱敏
 * @param {String} idCard 身份证号
 * @returns {String}
 */
export const maskIdCard = (idCard) => {
  if (!idCard) return ""
  if (idCard.length < 8) return idCard

  return idCard.substring(0, 6) + "****" + idCard.substring(idCard.length - 4)
}

/**
 * 手机号脱敏
 * @param {String} phone 手机号
 * @returns {String}
 */
export const maskPhone = (phone) => {
  if (!phone) return ""
  if (phone.length < 7) return phone

  return phone.substring(0, 3) + "****" + phone.substring(phone.length - 4)
}

/**
 * 格式化意向状态文本
 * @param {Number} intention 意向状态
 * @returns {String}
 */
export const formatIntentionText = (intention) => {
  return personIntentionMap[intention]?.text || "未联系"
}

/**
 * 格式化人员类型文本
 * @param {Number} type 人员类型
 * @returns {String}
 */
export const formatPersonTypeText = (type) => {
  return personTypeMap[type]?.text || "未知类型"
}

/**
 * 格式化性别文本
 * @param {Number} sex 性别
 * @returns {String}
 */
export const formatSexText = (sex) => {
  return personSexMap[sex]?.text || "未知"
}

/**
 * 格式化学历文本
 * @param {Number} education 学历
 * @returns {String}
 */
export const formatEducationText = (education) => {
  return personEducationMap[education]?.text || "未知"
}

/**
 * 格式化学业情况文本
 * @param {Number} graduate 学业情况
 * @returns {String}
 */
export const formatGraduateText = (graduate) => {
  return personGraduateMap[graduate]?.text || "未知"
}

/**
 * 格式化学习形式文本
 * @param {Number} studyForm 学习形式
 * @returns {String}
 */
export const formatStudyFormText = (studyForm) => {
  return personStudyFormMap[studyForm]?.text || "未知"
}

/**
 * 格式化优先级文本
 * @param {Number} priority 优先级
 * @returns {String}
 */
export const formatPriorityText = (priority) => {
  return personPriorityMap[priority]?.text || "未知"
}

/**
 * 格式化预储类型文本
 * @param {Number} preType 预储类型
 * @returns {String}
 */
export const formatPreTypeText = (preType) => {
  return personPreTypeMap[preType]?.text || "无"
}

/**
 * 格式化京籍状态文本
 * @param {Number} isBeijing 是否京籍
 * @returns {String}
 */
export const formatBeijingText = (isBeijing) => {
  return personIsBeijingMap[isBeijing]?.text || "未知"
}

/**
 * 格式化理工类状态文本
 * @param {Number} isEngineering 是否理工类
 * @returns {String}
 */
export const formatEngineeringText = (isEngineering) => {
  return personIsEngineeringMap[isEngineering]?.text || "未知"
}

/**
 * 格式化报名状态文本
 * @param {Number} sign 报名状态
 * @returns {String}
 */
export const formatSignText = (sign) => {
  return personSignStatusMap[sign]?.text || "未报名"
}

/**
 * 格式化宣传比对结果文本
 * @param {Number} result 宣传比对结果
 * @returns {String}
 */
export const formatPromoteCompareResultText = (result) => {
  return personPromoteCompareResultMap[result]?.text || "未联系"
}

/**
 * 格式化政考结果文本
 * @param {Number} result 政考结果
 * @returns {String}
 */
export const formatPoliticalExamResultText = (result) => {
  return personPoliticalExamResultMap[result]?.text || "未设置"
}

/**
 * 创建快速查询构建器
 * @param {Number|String} missionId 任务ID
 * @returns {MissionPersonQueryBuilder}
 */
export const createQueryBuilder = (missionId) => {
  return new MissionPersonQueryBuilder().missionId(missionId)
}

/**
 * 创建高校人员查询构建器
 * @param {Number|String} missionId 任务ID
 * @returns {MissionPersonQueryBuilder}
 */
export const createSchoolPersonQuery = (missionId) => {
  return createQueryBuilder(missionId).type(personTypeMap[1]?.status)
}

/**
 * 创建社会人员查询构建器
 * @param {Number|String} missionId 任务ID
 * @returns {MissionPersonQueryBuilder}
 */
export const createSocialPersonQuery = (missionId) => {
  return createQueryBuilder(missionId).type(personTypeMap[2]?.status)
}

/**
 * 创建有意向人员查询构建器
 * @param {Number|String} missionId 任务ID
 * @returns {MissionPersonQueryBuilder}
 */
export const createIntentionPersonQuery = (missionId) => {
  return createQueryBuilder(missionId).intention(personIntentionMap[1]?.status)
}

/**
 * 创建已报名人员查询构建器
 * @param {Number|String} missionId 任务ID
 * @returns {MissionPersonQueryBuilder}
 */
export const createSignedPersonQuery = (missionId) => {
  return createQueryBuilder(missionId).sign(personSignStatusMap[1]?.status)
}

/**
 * 获取高校人员统计列表
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export const getStatisticsMissionSchoolPersonList = (params = {}) => {
  return request({
    url: "/mini/statistics/mission_school_person_list",
    method: "GET",
    data: params,
  })
}

/**
 * 获取社会适龄青年统计列表
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export const getStatisticsMissionSocialPersonList = (params = {}) => {
  return request({
    url: "/mini/statistics/mission_social_person_list",
    method: "GET",
    data: params,
  })
}

/**
 * 统计查询参数构建器
 */
export class StatisticsQueryBuilder {
  constructor() {
    this.params = {}
  }

  // 设置任务ID
  missionId(id) {
    if (id) {
      this.params.mission_id = id
    }
    return this
  }

  // 设置维度 (person=人数, age=年龄, education=文化程度)
  dimension(dimension) {
    if (dimension) {
      this.params.dimension = dimension
    }
    return this
  }

  // 设置部门代码
  departmentCode(code) {
    if (code) {
      this.params.department_code = code
    }
    return this
  }

  // 设置文化程度 (当dimension为education时)
  education(education) {
    if (education) {
      this.params.education = education
    }
    return this
  }

  // 设置关键词搜索
  keyword(keyword) {
    if (keyword && keyword.trim()) {
      this.params.keyword = keyword.trim()
    }
    return this
  }

  // 设置分页
  page(page, pageSize = 10) {
    if (page) {
      this.params.page = page
    }
    if (pageSize) {
      this.params.page_size = pageSize
    }
    return this
  }

  // 获取参数
  getParams() {
    return { ...this.params }
  }

  // 构建查询
  build() {
    return this.getParams()
  }
}

/**
 * 创建统计查询构建器
 * @param {Number} missionId 任务ID
 * @returns {StatisticsQueryBuilder}
 */
export const createStatisticsQueryBuilder = (missionId) => {
  return new StatisticsQueryBuilder().missionId(missionId)
}

/**
 * 获取意向统计列表
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export const getStatisticsMissionIntentionList = (params = {}) => {
  return request({
    url: "/mini/statistics/mission_intention_list",
    method: "GET",
    data: params,
  })
}

/**
 * 获取推广对比统计列表
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export const getStatisticsMissionPromoteCompareList = (params = {}) => {
  return request({
    url: "/mini/statistics/mission_promote_compare_list",
    method: "GET",
    data: params,
  })
}

/**
 * 获取政治考察结果统计列表
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export const getStatisticsMissionPoliticalExamList = (params = {}) => {
  return request({
    url: "/mini/statistics/mission_political_exam_list",
    method: "GET",
    data: params,
  })
}

/**
 * 获取教育统计列表
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export const getStatisticsMissionEducationList = (params = {}) => {
  return request({
    url: "/mini/statistics/mission_education_list",
    method: "GET",
    data: params,
  })
}

/**
 * 获取完成数统计列表
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export const getStatisticsMissionGoList = (params = {}) => {
  return request({
    url: "/mini/statistics/mission_go_list",
    method: "GET",
    data: params,
  })
}

/**
 * 获取体格检查结果统计列表
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export const getStatisticsMissionPhysicalCheckList = (params = {}) => {
  return request({
    url: "/mini/statistics/mission_physical_check_list",
    method: "GET",
    data: params,
  })
}

/**
 * 获取体格检查复查统计列表
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export const getStatisticsMissionPhysicalRecheckList = (params = {}) => {
  return request({
    url: "/mini/statistics/mission_physical_recheck_list",
    method: "GET",
    data: params,
  })
}

/**
 * 获取体格检查抽查统计列表
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export const getStatisticsMissionPhysicalSpotCheckList = (params = {}) => {
  return request({
    url: "/mini/statistics/mission_physical_spot_check_list",
    method: "GET",
    data: params,
  })
}
