export const personSexMap = {
  "-1": {
    status: -1,
    text: "未知",
    color: "#909399",
  },
  1: {
    status: 1,
    text: "男",
    color: "#409EFF",
  },
  2: {
    status: 2,
    text: "女",
    color: "#F56C6C",
  },
}

export const personEducationMap = {
  "-1": {
    status: -1,
    text: "未知",
    color: "#909399",
    slug: "unknow",
  },
  1: {
    status: 1,
    text: "高中",
    color: "#E6A23C",
    slug: "gaozhong",
  },
  2: {
    status: 2,
    text: "专科",
    color: "#409EFF",
    slug: "zhuan",
  },
  3: {
    status: 3,
    text: "本科",
    color: "#67C23A",
    slug: "ben",
  },
  4: {
    status: 4,
    text: "本科以上",
    color: "#F56C6C",
    slug: "ben_plus",
  },
}

export const personStudyFormMap = {
  1: {
    status: 1,
    text: "全日制",
    color: "#409EFF",
  },
  2: {
    status: 2,
    text: "非全日制",
    color: "#F56C6C",
  },
  "-1": {
    status: -1,
    text: "未知",
  },
}

export const personPriorityMap = {
  "-1": {
    color: "#67C23A",
    status: -1,
    text: "无",
  },
  1: {
    color: "#67C23A",
    status: 1,
    text: "1类",
  },
  2: {
    color: "#67C23A",
    status: 2,
    text: "2类",
  },
  3: {
    color: "#67C23A",
    status: 3,
    text: "3类",
  },
  4: {
    color: "#67C23A",
    status: 4,
    text: "4类",
  },
  5: {
    color: "#67C23A",
    status: 5,
    text: "5类",
  },
  10: {
    color: "#67C23A",
    status: 10,
    text: "其他",
  },
}
export const personPreTypeMap = {
  "-1": {
    status: -1,
    text: "无",
    color: "#999",
  },
  1: {
    status: 1,
    text: "双合格",
    color: "#67C23A",
  },
  2: {
    status: 2,
    text: "教育",
    color: "#5ba2f3",
  },
}
export const personIsBeijingMap = {
  "-1": {
    status: -1,
    text: "未知",
    color: "#999999",
  },
  1: {
    status: 1,
    text: "京籍",
    color: "#67C23A",
  },
  2: {
    status: 2,
    text: "非京籍",
    color: "#e88a3e",
  },
}
export const personIsEngineeringMap = {
  "-1": {
    status: -1,
    text: "未知",
  },
  1: {
    status: 1,
    text: "理工类",
  },
  2: {
    status: 2,
    text: "非理工类",
  },
}

export const personGraduateMap = {
  1: {
    status: 1,
    text: "在校生",
    color: "#5ba2f3",
  },
  2: {
    status: 2,
    text: "应届毕业生",
    color: "#67C23A",
  },
  3: {
    status: 3,
    text: "毕业生",
    color: "#5352ed",
  },
  4: {
    status: 4,
    text: "毕业班生",
    color: "#999",
  },
}

export const personTypeMap = {
  1: {
    status: 1,
    text: "高校人员",
  },
  2: {
    status: 2,
    text: "社会适龄青年",
  },
}

export const personIntentionMap = {
  1: {
    status: 1,
    text: "有意向",
    color: "#67C23A",
  },
  2: {
    status: 2,
    text: "无意向",
    color: "#F56C6C",
  },
  "-1": {
    status: -1,
    text: "未联系",
    color: "#999",
  },
}

export const personSignStatusMap = {
  1: {
    status: 1,
    text: "已报名",
    color: "#67C23A",
  },
  2: {
    status: 2,
    text: "已报名本系统中不存在此人",
    color: "#F56C6C",
  },
  3: {
    status: 3,
    text: "已报名待完善信息",
    color: "#E6A23C",
  },
}

export const personPromoteCompareResultMap = {
  1: {
    status: 1,
    text: "有意向已报名",
    color: "#67C23A",
  },
  2: {
    status: 2,
    text: "有意向未报名",
    color: "#E6A23C",
  },
  3: {
    status: 3,
    text: "无意向已报名",
    color: "#00A1EB",
  },
  4: {
    status: 4,
    text: "无意向未报名",
    color: "#999",
  },
  5: {
    status: 5,
    text: "未联系已报名",
    color: "#EE82EE", // 紫色
  },
  "-1": {
    status: -1,
    text: "未联系",
    color: "#999",
  },
}

export const personPoliticalExamResultMap = {
  1: {
    status: 1,
    text: "通过",
    color: "#67C23A",
  },
  "-1": {
    status: -1,
    text: "未考核",
    color: "#999",
  },
  2: {
    status: 2,
    text: "未通过",
    color: "#F56C6C",
  },
}

export const personPreFixedMap = {
  "-1": {
    status: -1,
    text: "无",
    color: "#999",
  },
  1: {
    status: 1,
    text: "预定兵",
    color: "#67C23A",
  },
}

export const personListSceneMap = {
  mission_add_person: {
    text: "征兵任务新增人员",
    status: "mission_add_person",
  },
  double_passed: {
    text: "双合格",
    status: "double_passed",
  },
  signed: {
    text: "已报名",
    status: "signed",
  },
  mission_education_add_person: {
    text: "教育任务新增人员",
    status: "mission_education_add_person",
  },
  mission_can_fixed_person: {
    text: "可定兵人员",
    status: "mission_can_fixed_person",
  },
  mission_can_go_person: {
    text: "可以起运人员",
    status: "mission_can_go_person",
  },
  mission_went_person: {
    text: "已起运人员",
    status: "mission_went_person",
  },
  mission_pre_store_person: {
    text: "预储人员",
    status: "mission_pre_store_person",
  },
  mission_physical_examination_add_person: {
    text: "体检计划上报选择人员",
    status: "mission_physical_examination_add_person",
  },
  mission_physical_examination_recheck_add_person: {
    text: "体检计划上报选择人员",
    status: "mission_physical_examination_recheck_add_person",
  },
  mission_physical_check_result: {
    text: "体检结果列表",
    status: "mission_physical_check_result",
  },
  mission_physical_recheck_result: {
    text: "体检复查结果列表",
    status: "mission_physical_recheck_result",
  },
  mission_physical_spot_check_person_list: {
    text: "抽查人员列表",
    status: "mission_physical_spot_check_person_list",
  },
}

export const personEducationOutMap = {
  1: {
    text: "已淘汰",
    status: 1,
    color: "#F56C6C",
  },
  "-1": {
    text: "正常",
    status: -1,
    color: "#67C23A",
  },
}

export const missionPhysicalCheckMap = {
  1: {
    text: "合格",
    status: 1,
    color: "#67C23A",
  },
  2: {
    text: "不合格",
    status: 2,
    color: "#F56C6C",
  },
  "-1": {
    text: "未体检",
    status: -1,
    color: "#909399",
  },
}
export const missionPhysicalRecheckMap = {
  1: {
    text: "合格",
    status: 1,
    color: "#67C23A",
  },
  2: {
    text: "不合格",
    status: 2,
    color: "#F56C6C",
  },
  "-1": {
    text: "未复查",
    status: -1,
    color: "#909399",
  },
}

export const missionPhysicalResultMap = {
  1: {
    text: "合格",
    status: 1,
    color: "#67C23A",
  },
  2: {
    text: "不合格",
    status: 2,
    color: "#F56C6C",
  },
  "-1": {
    text: "无",
    status: -1,
    color: "#909399",
  },
}

export const missionPhysicalSpotCheckMap = {
  1: {
    text: "正常",
    status: 1,
    color: "#67C23A",
  },
  2: {
    text: "异常",
    status: 2,
    color: "#F56C6C",
  },
  "-1": {
    text: "未检查",
    status: -1,
    color: "#9E9E9E",
  },
}

// 政考结果
export const missionPoliticalExaminationMap = {
  "-1": {
    text: "未完成",
    status: -1,
    color: "#9E9E9E",
  },
  1: {
    text: "通过",
    status: 1,
    color: "#67C23A",
  },
  2: {
    text: "未通过",
    status: 2,
    color: "#F56C6C",
  },
}

// 役前教育结果
export const personEducationResultMap = {
  1: {
    text: "通过",
    status: 1,
    color: "#67C23A",
  },
  2: {
    text: "未通过",
    status: 2,
    color: "#F56C6C",
  },
  "-1": {
    text: "未教育",
    status: -1,
    color: "#909399",
  },
}

export const politicalOutlookArr = [
  "中共党员",
  "中共预备党员",
  "共青团员",
  "民革党员",
  "民盟盟员",
  "民建会员",
  "民进会员",
  "农工党党员",
  "致公党党员",
  "九三学社社员",
  "台盟盟员",
  "无党派人士",
  "群众",
]

export const degreeArr = ["无", "学士", "硕士", "博士"]
export const nationArr = [
  "汉族",
  "蒙古族",
  "回族",
  "藏族",
  "维吾尔族",
  "苗族",
  "彝族",
  "壮族",
  "布依族",
  "朝鲜族",
  "满族",
  "侗族",
  "瑶族",
  "白族",
  "土家族",
  "哈尼族",
  "哈萨克族",
  "傣族",
  "黎族",
  "傈僳族",
  "佤族",
  "畲族",
  "高山族",
  "拉祜族",
  "水族",
  "东乡族",
  "纳西族",
  "景颇族",
  "柯尔克孜族",
  "土族",
  "达斡尔族",
  "仫佬族",
  "羌族",
  "布朗族",
  "撒拉族",
  "毛南族",
  "仡佬族",
  "锡伯族",
  "阿昌族",
  "普米族",
  "塔吉克族",
  "怒族",
  "乌孜别克族",
  "俄罗斯族",
  "鄂温克族",
  "德昂族",
  "保安族",
  "裕固族",
  "京族",
  "塔塔尔族",
  "独龙族",
  "鄂伦春族",
  "赫哲族",
  "门巴族",
  "珞巴族",
  "基诺族",
]
