{
	"easycom": {
		"autoscan": true,
		// 注意一定要放在custom里，否则无效，https://ask.dcloud.net.cn/question/131175
		"custom": {
			"^u--(.*)": "uview-plus/components/u-$1/u-$1.vue",
			"^up-(.*)": "uview-plus/components/u-$1/u-$1.vue",
			"^u-([^-].*)": "uview-plus/components/u-$1/u-$1.vue",
			"^customRadioGroup$": "@/components/customRadioGroup/customRadioGroup.vue",
			"^customRadioItem$": "@/components/customRadioItem/customRadioItem.vue"
			// "^l--(.*)": "uni_modules/lime-circle/components/l-circle/l-circle.vue"
		}
	},
	"pages": [
		//pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages
		{
			"path": "pages/index/index",
			"style": {
				"navigationBarTitleText": "首页",
				"navigationStyle": "custom",
				"navigationBarTextStyle": "black"
			}
		},
		{
			"path": "pages/login/login",
			"style": {
				"navigationBarTitleText": "北京征兵办公云平台",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom",
				"navigationBarTextStyle": "white"
			}
		},
		{
			"path": "pages/register/register",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/resetPassword/resetPassword",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/my/changePassword",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/result/accountResult",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/policy/policy",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": true,
				"navigationStyle": "custom",
				"navigationBarTextStyle": "white"
			}
		},
		{
			"path": "pages/policy/detail",
			"style": {
				"navigationBarTitleText": "详情",
				"navigationBarBackgroundColor": "#fff",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/notify/notify",
			"style": {
				"navigationBarTitleText": "通知",
				"enablePullDownRefresh": true,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/draftNotice/draftNotice",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": true
			}
		},
		{
			"path": "pages/noticeDetail/noticeDetail",
			"style": {
				"navigationBarTitleText": "",
				"navigationBarBackgroundColor": "#fff",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/article/article",
			"style": {
				"navigationBarTitleText": "",
				"navigationBarBackgroundColor": "#fff",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/article/page",
			"style": {
				"navigationBarTitleText": "",
				"navigationBarBackgroundColor": "#fff",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/article/category",
			"style": {
				"navigationBarTitleText": "文章分类",
				"enablePullDownRefresh": true
			}
		},
		{
			"path": "pages/course/course",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": true,
				"navigationStyle": "custom",
				"navigationBarTextStyle": "white"
			}
		},
		{
			"path": "pages/course/chapter",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": true,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/course/learnCourse",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/exam/exam",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/result/exercise",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/result/exam",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/exam/analysis",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/exam/examList",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom",
				"navigationBarTextStyle": "white"
			}
		},
		{
			"path": "pages/exam/examTips",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom",
				"navigationBarTextStyle": "white"
			}
		},
		{
			"path": "pages/report/report",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false,
				"navigationBarTextStyle": "white",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/result/report",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/conscript/list",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": true,
				"navigationBarTextStyle": "white",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/conscript/conscript",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": true,
				"navigationStyle": "custom",
				"navigationBarTextStyle": "white"
			}
		},
		{
			"path": "pages/matching/matching",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": true,
				"navigationStyle": "custom",
				"navigationBarTextStyle": "white"
			}
		},
		{
			"path": "pages/my/my",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/score/score",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/report/list",
			"style": {
				"navigationBarTitleText": "我的举报",
				"enablePullDownRefresh": false,
				"navigationBarBackgroundColor": "#fff",
				"backgroundColor": "#fff"
			}
		},
		{
			"path": "pages/report/detail",
			"style": {
				"navigationBarTitleText": "举报详情",
				"enablePullDownRefresh": false,
				"navigationBarBackgroundColor": "#fff",
				"backgroundColor": "#fff"
			}
		},
		{
			"path": "pages/report/reply",
			"style": {
				"navigationBarTitleText": "举报回复",
				"enablePullDownRefresh": false,
				"navigationBarBackgroundColor": "#577F49",
				"navigationBarTextStyle": "white"
			}
		},
		{
			"path": "pages/message/message",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false,
				"navigationBarBackgroundColor": "#fff",
				"backgroundColor": "#fff"
			}
		},
		{
			"path": "pages/message/detail",
			"style": {
				"navigationBarTitleText": "消息详情",
				"enablePullDownRefresh": false,
				"navigationBarBackgroundColor": "#fff",
				"backgroundColor": "#fff"
			}
		},
		{
			"path": "pages/my/personalData",
			"style": {
				"navigationBarTitleText": "个人资料",
				"enablePullDownRefresh": false,
				"navigationBarBackgroundColor": "#fff",
				"backgroundColor": "#fff"
			}
		},
		{
			"path": "pages/my/more",
			"style": {
				"navigationBarTitleText": "更多",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/office/office",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": true,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/office/postTask",
			"style": {
				"navigationBarTitleText": "发布任务",
				"enablePullDownRefresh": false,
				"navigationBarBackgroundColor": "#577F49",
				"navigationBarTextStyle": "white"
			}
		},
		{
			"path": "pages/office/postNotice",
			"style": {
				"navigationBarTitleText": "发布通知",
				"enablePullDownRefresh": false,
				"navigationBarBackgroundColor": "#577F49",
				"navigationBarTextStyle": "white"
			}
		},
		{
			"path": "pages/office/taskDetail",
			"style": {
				"navigationBarTitleText": "任务详情",
				"enablePullDownRefresh": false,
				"navigationBarBackgroundColor": "#fff"
			}
		},
		{
			"path": "pages/office/postWork",
			"style": {
				"navigationBarTitleText": "上报",
				"enablePullDownRefresh": false,
				"navigationBarBackgroundColor": "#577F49",
				"navigationBarTextStyle": "white"
			}
		},
		{
			"path": "pages/office/questionnaireDetail",
			"style": {
				"navigationBarTitleText": "问卷详情",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom",
				"navigationBarTextStyle": "white"
			}
		},
		{
			"path": "pages/result/questionnaire",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/todoTask/todoTask",
			"style": {
				"navigationBarTitleText": "待办",
				"enablePullDownRefresh": true,
				"navigationBarBackgroundColor": "#fff"
			}
		},
		{
			"path": "pages/data/data",
			"style": {
				"navigationBarTitleText": "数据",
				"enablePullDownRefresh": true,
				"navigationBarBackgroundColor": "#fff"
			}
		},
		{
			"path": "pages/favorites/favorites",
			"style": {
				"navigationBarTitleText": "收藏夹",
				"enablePullDownRefresh": true
			}
		},
		{
			"path": "pages/electronicCertificate/electronicCertificate",
			"style": {
				"navigationBarTitleText": "电子证书",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom",
				"navigationBarTextStyle": "white"
			}
		},
		{
			"path": "pages/aibot/aibot",
			"style": {
				"navigationBarTitleText": "智能助手",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/errorNotebook/errorNotebook",
			"style": {
				"navigationBarTitleText": "错题本",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/errorNotebook/errorDetail",
			"style": {
				"navigationBarTitleText": "错题详情",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/errorNotebook/practice",
			"style": {
				"navigationBarTitleText": "错题练习",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/recruitFaceAuth/recruitFaceAuth",
			"style": {
				"navigationBarTitleText": "人脸识别",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}
		}
	],
	"subPackages": [
		{
			"root": "packageTasks",
			"pages": [
				{
					"path": "pages/index/index",
					"style": {
						"navigationBarTitleText": "ZB任务",
						"enablePullDownRefresh": false,
						"navigationStyle": "custom",
						"navigationBarTextStyle": "white"
					}
				},
				{
					"path": "pages/index/links",
					"style": {
						"navigationBarTitleText": "ZB任务",
						"enablePullDownRefresh": false,
						"navigationStyle": "custom"
					}
				},
				{
					"path": "pages/data/data",
					"style": {
						"navigationBarTitleText": "人员库数据汇总",
						"enablePullDownRefresh": false,
						"navigationStyle": "custom"
					}
				},
				{
					"path": "pages/intentionStatistics/intentionStatistics",
					"style": {
						"navigationBarTitleText": "意向统计",
						"enablePullDownRefresh": false,
						"navigationStyle": "custom"
					}
				},
				{
					"path": "pages/intentionStatistics/personDetail",
					"style": {
						"navigationBarTitleText": "意向统计",
						"enablePullDownRefresh": false,
						"navigationStyle": "custom"
					}
				},
				{
					"path": "pages/promotionComparison/promotionComparison",
					"style": {
						"navigationBarTitleText": "宣传比对",
						"enablePullDownRefresh": false,
						"navigationStyle": "custom"
					}
				},
				{
					"path": "pages/promotionComparison/comparisonDetail",
					"style": {
						"navigationBarTitleText": "宣传比对",
						"enablePullDownRefresh": false,
						"navigationStyle": "custom"
					}
				},
				{
					"path": "pages/physicalExamination/physicalExamination",
					"style": {
						"navigationBarTitleText": "体格检查",
						"enablePullDownRefresh": false,
						"navigationStyle": "custom"
					}
				},
				{
					"path": "pages/physicalExamination/detail",
					"style": {
						"navigationBarTitleText": "体格检查",
						"enablePullDownRefresh": false,
						"navigationStyle": "custom"
					}
				},
				{
					"path": "pages/politicalExaminationResults/politicalExaminationResults",
					"style": {
						"navigationBarTitleText": "政考结果",
						"enablePullDownRefresh": false,
						"navigationStyle": "custom"
					}
				},
				{
					"path": "pages/educationList/educationList",
					"style": {
						"navigationBarTitleText": "教育名单",
						"enablePullDownRefresh": false,
						"navigationStyle": "custom"
					}
				},
				{
					"path": "pages/numberOfCompleted/numberOfCompleted",
					"style": {
						"navigationBarTitleText": "完成数",
						"enablePullDownRefresh": false,
						"navigationStyle": "custom"
					}
				},
				{
					"path": "pages/reservePersonnel/reservePersonnel",
					"style": {
						"navigationBarTitleText": "预储人员",
						"enablePullDownRefresh": false,
						"navigationStyle": "custom"
					}
				},
				{
					"path": "pages/returnPersonnel/returnPersonnel",
					"style": {
						"navigationBarTitleText": "退兵人员",
						"enablePullDownRefresh": false,
						"navigationStyle": "custom"
					}
				},
				{
					"path": "pages/returnPersonnel/returnPersonnelDetail",
					"style": {
						"navigationBarTitleText": "退兵详情",
						"enablePullDownRefresh": false,
						"navigationStyle": "custom"
					}
				},
				{
					"path": "pages/taskNumber/taskNumber",
					"style": {
						"navigationBarTitleText": "任务数",
						"enablePullDownRefresh": true,
						"navigationStyle": "custom"
					}
				}
			]
		}
	],
	"globalStyle": {
		"navigationBarTextStyle": "black",
		"navigationBarTitleText": "uni-app",
		"navigationBarBackgroundColor": "#F7F8FA",
		"backgroundColor": "#F7F8FA"
	},
	"tabBar": {
		"color": "#9CA3B5",
		"borderStyle": "white",
		"backgroundColor": "#fff",
		"selectedColor": "#39455B",
		"list": [
			{
				"pagePath": "pages/index/index",
				"iconPath": "static/tabbar/home.png",
				"selectedIconPath": "static/tabbar/home-active.png",
				"text": "主页"
			},
			{
				"pagePath": "pages/todoTask/todoTask",
				"iconPath": "static/tabbar/daiban.png",
				"selectedIconPath": "static/tabbar/daiban-active.png",
				"text": "待办"
			},
			{
				"pagePath": "pages/data/data",
				"iconPath": "static/tabbar/data.png",
				"selectedIconPath": "static/tabbar/data-active.png",
				"text": "数据"
			},
			{
				"pagePath": "pages/my/my",
				"iconPath": "static/tabbar/my.png",
				"selectedIconPath": "static/tabbar/my-active.png",
				"text": "我的"
			}
		]
	},
	"uniIdRouter": {}
}