import { useCommonStore } from "@/store/common.js"

export default class MySocket {
  constructor(config) {
    this.isConnecting = false
    this.socketTask = null
    this.store = useCommonStore()
    this.config = config
  }

  async init() {
    const onMessageCallback = this.config.onMessage

    return new Promise((resolve, reject) => {
      this.socketTask = uni.connectSocket({
        url: `${this.store.socketUrl}${
          this.token ? `?token=${this.store.token}` : ""
        }`,
        success: (res) => {
          console.log("WebSocket连接创建成功")
          resolve(res)
        },
        fail: (err) => {
          console.log("WebSocket连接创建失败", err)
          reject(err)
        },
      })

      this.socketTask.onOpen(() => {
        this.isConnecting = true
        console.log("WebSocket连接打开")
        this.socketTask.onMessage((res) => {
          onMessageCallback(JSON.parse(res.data))
        })
      })

      this.socketTask.onClose(() => {
        console.log("WebSocket连接关闭")
        this.isConnecting = false
        this.init()
      })

      this.socketTask.onError((err) => {
        console.log("WebSocket连接错误", err)
      })
    })
  }

  send(data) {
    console.log("发送内容：" + JSON.stringify(data))
    return new Promise((resolve, reject) => {
      this.socketTask.send({
        data: JSON.stringify(data),
        success: (res) => {
          console.log("发送成功")
          resolve(res)
        },
        fail: (err) => {
          console.log("发送失败")
          this.isConnecting = false
          this.init()
          reject(err)
        },
      })
    })
  }

  close() {
    return new Promise((resolve, reject) => {
      this.socketTask.close({
        success: (res) => {
          console.log("关闭成功")
          resolve(res)
        },
        fail: (err) => {
          console.log("关闭失败")
          reject(err)
        },
      })
    })
  }
}
