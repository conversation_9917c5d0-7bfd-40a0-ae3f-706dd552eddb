<script setup>
import { ref, watch, computed } from "vue"
import { onLoad, onShow } from "@dcloudio/uni-app"
import { useCommonStore } from "@/store/common.js"
import { useUserStore } from "@/store/user.js"
import MySocket from "./MySocket"
import request from "@/utils/request"

const commonStore = useCommonStore()
const userStore = useUserStore()

const isGenerating = ref(false)
const messagesList = ref([])
const onMessage = (res) => {
  const { output, request_id } = res
  const lastMessage = messagesList.value[messagesList.value.length - 1]

  lastMessage.status = "output"
  lastMessage.content = output.text

  if (output.finish_reason === "stop") {
    isGenerating.value = false
    lastMessage.status = "done"
    lastMessage.record_id = request_id
  }
}
const mySocket = new MySocket({
  onMessage,
})

const onConfirm = (text) => {
  mySocket
    .send({
      action: "question",
      prompt: text,
    })
    .then(() => {
      const groupId = Date.now()

      messagesList.value.push({
        groupId,
        role: "user",
        content: text,
      })

      messagesList.value.push({
        groupId,
        role: "assistant",
        content: "",
        status: "generating",
      })
      isGenerating.value = true

      getConsultArticle(text, groupId)
    })
    .catch(() => {
      uni.showToast({
        title: "发送失败, 请重试",
        icon: "none",
      })
    })
}

const headerHeight = computed(() => commonStore.navHeight + "px")

const messageRef = ref(null)
const onInputFocus = () => {
  messageRef.value.scrollIntoBottom()
}
const onInputBlur = () => {
  messageRef.value.updateScrollTop("lower")
}

const init = () => {
  mySocket.init()
}

const getConsultArticle = (keyword, groupId) => {
  request({
    url: "/mini/consult_article",
    method: "get",
    data: {
      keyword,
    },
  }).then((res) => {
    console.log(res)
    const lastAiMessage = messagesList.value.find((item) => {
      return item.groupId === groupId && item.role === "assistant"
    })
    if (lastAiMessage) {
      lastAiMessage.articles = res.result || []
    }
  })
}

watch(
  () => userStore.isLogin,
  (value) => {
    if (value) {
      init()
    }
  },
  { immediate: true }
)

onLoad((options) => {
  console.log("index onLoad")
})

onShow(() => {
  console.log("index onShow")
})
</script>

<template>
  <navHeader
    :title="title"
    bg="/static/image/bg.png"
    :showLeft="true"
  ></navHeader>

  <bottomPanel
    :isGenerating="isGenerating"
    @confirm="onConfirm"
    @inputFocus="onInputFocus"
    @inputBlur="onInputBlur"
  />

  <view class="index-content" @click="onContentClick">
    <messages ref="messageRef" v-if="userStore.isLogin" :list="messagesList" />
  </view>
</template>

<style lang="scss" scoped>
.index-bg {
  width: 100vw;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  z-index: -1;
}

.index-content {
  position: absolute;
  top: v-bind(headerHeight);
  left: 0;
  z-index: 0;
  width: 100vw;
  height: calc(100vh - v-bind(headerHeight));
  padding-bottom: env(safe-area-inset-bottom);
  box-sizing: border-box;
  overflow: hidden;
}
</style>
