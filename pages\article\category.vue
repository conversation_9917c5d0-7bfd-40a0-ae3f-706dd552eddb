<script setup>
import { ref, watch, computed } from "vue"
import {
  onLoad,
  onShow,
  onPullDownRefresh,
  onReachBottom,
} from "@dcloudio/uni-app"
import request from "@/utils/request"
import { usePagination } from "@/utils/hooks"

const categoryId = ref(0)
const title = ref("")

const goDetail = (item) => {
  console.log(item)
  navTo(`/pages/article/article?articleId=${item.id}`)
}

const list = ref([])
const loadData = (options) => {
  return new Promise((resolve) => {
    request({
      url: "/mini/articles",
      method: "GET",
      data: Object.assign(
        {
          category_id: categoryId.value,
        },
        options
      ),
    }).then((res) => {
      list.value.push(...(res.result?.data || []))
      resolve(res)
    })
  })
}

const { page, loadPage, refresh, loadMoreStatus } = usePagination({
  loadData,
  list,
})

onLoad((options) => {
  console.log(options)
  categoryId.value = options.categoryId ? parseInt(options.categoryId) : 0
  loadPage()
})
onPullDownRefresh(() => {
  refresh()
})
onReachBottom(() => {
  loadPage()
})
</script>

<template>
  <view class="page">
    <navHeader
      :title="title"
      bg="/static/image/bg.png"
      :showLeft="true"
    ></navHeader>
    <view class="body">
      <notifyItem
        v-for="item in list"
        :key="item.id"
        :item="item"
        @openDetail="goDetail(item)"
      />
    </view>
  </view>
</template>

<style lang="scss" scoped>
.body {
  margin-top: 200rpx;
}
</style>
