<script setup>
import { ref, watch, computed } from "vue"
import { onLoad, onShow } from "@dcloudio/uni-app"
import request from "@/utils/request"
import dayjs from "dayjs"

const pageId = ref(0)

const detailData = ref({})

const title = ref("")
const content = ref("")
const datetime = ref("")

const renderString = (html) => {
  content.value = html.replace(
    /<p>/g,
    `<p style="margin-bottom: 16px;">&emsp;&emsp;`
  )

  content.value = html.replace(
    /<img /g,
    '<img style="max-width:100%;height:auto;" '
  )
}

const loadData = () => {
  request({
    url: `/mini/page/${pageId.value}`,
    method: "get",
  }).then((res) => {
    if (res.code === 200) {
      console.log("res", res)
      detailData.value = res.result
      title.value = res.result.title
      renderString(res.result?.content || "")
      datetime.value = dayjs(res.result.created_at).format("YYYY-MM-DD HH:mm")
    }
  })
}

onLoad((options) => {
  console.log("onLoad", options)
  pageId.value = options.pageId
  loadData()
})
</script>

<template>
  <view class="page">
    <view class="title">
      {{ title }}
    </view>
    <view class="datetime">
      <image src="/static/image/time.png" mode=""></image>
      <text>
        {{ datetime }}
      </text>
    </view>
    <view class="content">
      <mp-html
        :content="content"
        :tag-style="{
          p: 'margin-bottom: 42rpx; font-size: 34rpx; color: #111111;',
        }"
      ></mp-html>
    </view>

    <view
      class="files"
      v-if="
        detailData.file_ids_attachments &&
        detailData.file_ids_attachments.length > 0
      "
    >
      <fileList :fileList="detailData.file_ids_attachments" />
    </view>
  </view>
</template>

<style>
page {
  background-color: #ffffff;
}
</style>

<style lang="scss" scoped>
.page {
  padding: 50rpx;
  padding-bottom: 150rpx;

  .title {
    font-weight: bold;
    font-size: 42rpx;
    color: #111111;
    text-align: left;
    font-style: normal;
    margin-bottom: 24rpx;
  }
  .datetime {
    display: flex;
    align-items: center;
    margin-bottom: 64rpx;
    image {
      width: 26rpx;
      height: 26rpx;
    }
    text {
      font-size: 26rpx;
      color: #bdc4ce;
      margin-left: 12rpx;
    }
  }
  .content {
    font-size: 34rpx;
    color: #111111;
    margin-bottom: 35rpx;
  }

  .files {
    margin-top: 20rpx;
  }
}
</style>
