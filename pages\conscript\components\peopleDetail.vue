<script setup>
import { ref, watch, computed } from "vue"
import taskProgress from "./taskProgress.vue"
import dayjs from "dayjs"

const props = defineProps({
  peopleDetail: {
    type: Object,
    default: () => ({}),
  },
})

const emit = defineEmits(["finishDialogOpen", "call"])

const fieldList = [
  {
    label: "身份证号",
    key: "id_card",
  },
  {
    label: "入伍时间",
    key: "enlistment_time",
  },
  {
    label: "入伍所在区",
    key: "enlistment_district_name",
  },
  {
    label: "入伍街道/院校",
    key: "enlistment_street_name",
  },
  {
    label: "文化程度",
    key: "education",
  },
  {
    label: "文科理科",
    key: "major_type",
  },
  {
    label: "政治面貌",
    key: "political",
  },
  {
    label: "服现役大单位",
    key: "service_unit",
  },
  {
    label: "服现役旅营级单位",
    key: "service_unit_secondary",
  },
  {
    label: "专业岗位",
    key: "service_job",
  },
  {
    label: "专业类别",
    key: "service_job_category",
  },
  {
    label: "退役时间",
    key: "retirement_time",
  },
  {
    label: "退役的军衔",
    key: "retirement_rank",
  },
  {
    label: "入伍前所属省份",
    key: "enlistment_province",
  },
  {
    label: "家庭住址",
    key: "family_address",
  },
  {
    label: "现工作单位及职务",
    key: "now_working_unit",
  },
  {
    label: "现从事专业（或大学、高级工专业）",
    key: "now_major",
  },
  {
    label: "联系方式",
    key: "contact",
  },
  {
    label: "家人联系方式",
    key: "family_contact",
  },
  {
    label: "是否结婚",
    key: "married",
    render: (value) => (value ? "已婚" : "未婚"),
  },
  {
    label: "是否独生子女",
    key: "single",
    render: (value) => (value ? "独生子女" : "非独生子女"),
  },
  {
    label: "是否生育",
    key: "birth",
    render: (value) => (value ? "已生育" : "未生育"),
  },
]

const logTypeMap = {
  sms: "已发送短信",
  phone: "已播打电话",
}

const call = () => {
  emit("call", props.peopleDetail.contact)
}

const finishDialogOpen = (stageName) => {
  emit("finishDialogOpen", stageName)
}
</script>

<template>
  <view class="people-detail">
    <view class="name-call">
      <view class="name">
        {{ peopleDetail.name }}
        <image
          v-if="peopleDetail.sex"
          class="gender"
          :src="
            peopleDetail.sex == '男'
              ? '/static/image/boy.png'
              : '/static/image/girl.png'
          "
        ></image>
      </view>
      <image
        src="/static/image/call.svg"
        mode=""
        class="call"
        @click="call"
      ></image>
    </view>
    <view class="progress-wrapper">
      <taskProgress
        :item="peopleDetail"
        @finishDialogOpen="(stageName) => emit('finishDialogOpen', stageName)"
      />
    </view>
    <view class="log">
      <view
        class="log-item"
        v-for="log in peopleDetail.out_notices"
        :key="log.id"
      >
        <text>
          {{ dayjs(log.start_at).format("YYYY/MM/DD HH:mm") }}
        </text>
        <text>
          {{ logTypeMap[log.type] }}
        </text>
      </view>
    </view>
    <view class="info">
      <view class="item" v-for="field in fieldList" :key="field.key">
        <view class="label">
          {{ field.label }}
        </view>
        <view class="value">
          {{
            field.render
              ? field.render(peopleDetail[field.key])
              : peopleDetail[field.key]
          }}
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.people-detail {
  display: flex;
  flex-direction: column;
  max-height: 75vh;
  padding: 45rpx 45rpx 0;

  .name-call {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .name {
      display: flex;
      align-items: center;
      font-size: 52rpx;
      font-weight: bold;
      color: #21232c;

      .gender {
        width: 49rpx;
        height: 49rpx;
        margin-left: 22rpx;
      }
    }

    .call {
      width: 56rpx;
      height: 56rpx;
      border: 1rpx solid #c3cbd6;
      padding: 8rpx;
      border-radius: 50%;
    }
  }

  .progress-wrapper {
    margin-top: 30rpx;
  }

  .log {
    padding: 30rpx;
    border-bottom: 1px solid #f0f0f0;

    .log-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 20rpx;

      text {
        font-size: 28rpx;
        color: #666666;
      }
    }
  }

  .info {
    margin-top: 64rpx;
    flex: 1;
    overflow-y: auto;

    .item {
      padding: 20rpx 0;
      border-bottom: 2rpx solid #f6f6f6;

      .label {
        font-size: 28rpx;
        color: #9ca3b5;
        margin-bottom: 7rpx;
      }

      .value {
        font-size: 32rpx;
        color: #111111;
      }
    }
  }
}
</style>
