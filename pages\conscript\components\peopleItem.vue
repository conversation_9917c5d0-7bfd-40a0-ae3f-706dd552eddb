<script setup>
import { ref } from "vue"
import taskProgress from "./taskProgress.vue"

const props = defineProps({
  item: Object,
})

const emit = defineEmits(["finishDialogOpen", "openDetail", "call"])

const openDetail = () => {
  emit("openDetail", props.item)
}

const callPhone = (phone) => {
  emit("call", phone)
}
</script>

<template>
  <view class="box" @click="openDetail">
    <view class="info">
      <view class="base">
        <view class="title">
          {{ item.name }}
          <image
            v-if="item.sex"
            :src="
              item.sex == '男'
                ? `/static/image/boy.png`
                : `/static/image/girl.png`
            "
            mode=""
            class="icon"
          ></image>
        </view>
        <view class="desc">
          <text v-if="item.age">{{ item.age }}岁 · </text>
          <text>{{ item.id_card }} </text>
        </view>
      </view>
      <image
        src="/static/image/call.svg"
        mode=""
        class="call"
        @click.stop="callPhone(item.contact)"
      ></image>
    </view>
    <taskProgress
      :item="item"
      v-if="item.task_id"
      @finishDialogOpen="(stageName) => emit('finishDialogOpen', stageName)"
    />
  </view>
</template>

<style lang="scss" scoped>
.box {
  width: 702rpx;
  background: rgba(255, 255, 255, 0.94);
  box-shadow: 0rpx 6rpx 13rpx 1rpx rgba(0, 0, 0, 0.04);
  border-radius: 20rpx 20rpx 20rpx 20rpx;
  border: 2rpx solid #ffffff;
  padding: 28rpx;
  margin-bottom: 20rpx;

  .info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 18rpx 0;

    .base {
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .title {
        font-weight: bold;
        font-size: 34rpx;
        color: #333333;
        display: flex;
        align-items: center;
        gap: 18rpx;

        .icon {
          width: 30rpx;
          height: 30rpx;
        }
      }
      .desc {
        margin-top: 12rpx;
        font-size: 26rpx;
        color: #8c9198;
      }
    }

    .call {
      width: 56rpx;
      height: 56rpx;
    }
  }
}
</style>
