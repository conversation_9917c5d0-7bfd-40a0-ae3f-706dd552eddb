<script setup>
import { ref, toRef, computed } from "vue"
import dayjs from "dayjs"

const props = defineProps({
  item: Object,
})

const emit = defineEmits(["finishDialogOpen"])

const item = toRef(props, "item")

const progress = computed(() => [
  {
    title: "人脸识别",
    isFinish: item.value.face_complete,
    finishedTime: dayjs(item.value.face_complete_at).format("YYYY.MM.DD"),
    stageName: "face",
  },
  {
    title: "体检",
    isFinish: item.value.body_examination_complete,
    finishedTime: dayjs(item.value.body_examination_complete_at).format(
      "YYYY.MM.DD"
    ),
    stageName: "body_examination",
  },
  {
    title: "政考",
    isFinish: item.value.political_examination_complete,
    finishedTime: dayjs(item.value.political_examination_complete_at).format(
      "YYYY.MM.DD"
    ),
    stageName: "political_examination",
  },
  {
    title: "招录",
    isFinish: item.value.transport_complete,
    finishedTime: dayjs(item.value.transport_complete_at).format("YYYY.MM.DD"),
    stageName: "transport",
  },
])

const finishDialogOpen = (stageName) => {
  emit("finishDialogOpen",stageName)
}



</script>

<template>
  <view class="progress">
    <view class="item" v-for="(item, index) in progress" :key="index">
      <image
        class="icon"
        :src="
          item.isFinish
            ? '/static/image/finish.png'
            : '/static/image/unfinish.png'
        "
      />
      <view class="title">{{ item.title }}</view>
      <view class="bottom">
        <view class="desc" v-if="item.isFinish">
          {{ item.finishedTime }}
        </view>
        <button
          class="finish-btn"
          v-if="!item.isFinish"
          @click.stop="finishDialogOpen(item.stageName)"
        >
          完成
        </button>
      </view>
    </view>
  </view>


</template>

<style lang="scss" scoped>
.progress {
  height: 184rpx;
  background: #f7f8fa;
  border-radius: 14rpx 14rpx 14rpx 14rpx;
  padding: 35rpx;
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 30rpx;

  .item {
    width: 116rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;

    &::after {
      content: "";
      width: 82rpx;
      height: 0rpx;
      border: 1rpx dashed #bdc4ce;
      position: absolute;
      left: 88rpx;
      top: 13rpx;
    }

    &:last-child::after {
      display: none;
    }

    .icon {
      width: 26rpx;
      height: 26rpx;
    }

    .title {
      font-size: 28rpx;
      color: #39455b;
      margin-top: 10rpx;
      font-weight: 500;
    }

    .bottom {
      margin-top: 10rpx;
      display: flex;
      align-items: center;
    }

    .desc {
      font-size: 22rpx;
      color: #bdc4ce;
    }

    .finish-btn {
      font-size: 22rpx;
      color: #577f49;
      border: 1rpx solid #577f49;
      line-height: 42rpx;
      padding: 0 16rpx;
      border-radius: 31rpx 31rpx 31rpx 31rpx;
    }
  }
}
</style>
