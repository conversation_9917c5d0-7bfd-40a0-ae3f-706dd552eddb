<script setup>
import { ref, watch, computed } from "vue"
import {
  onLoad,
  onShow,
  onPullDownRefresh,
  onReachBottom,
} from "@dcloudio/uni-app"
import request from "@/utils/request"
import dayjs from "dayjs"
import { useCommonStore } from "@/store/common"
import { usePagination } from "@/utils/hooks"
import taskProgress from "./components/taskProgress.vue"
import peopleDetail from "./components/peopleDetail.vue"
import peopleItem from "./components/peopleItem.vue"

const commonStore = useCommonStore()
const navHeight = computed(() => commonStore.navHeight + "px")

const taskId = ref(0)

const keyword = ref("")
const list = ref([])
const loadData = (options) => {
  return new Promise((resolve) => {
    request({
      url: "/mini/recall_task_soldier",
      method: "GET",
      data: Object.assign(
        {
          keyword: keyword.value,
          task_id: taskId.value,
        },
        options
      ),
    }).then((res) => {
      list.value.push(...(res.result?.data || []))
      resolve(res)
    })
  })
}

const taskDetail = ref({})
const loadTaskDetail = async () => {
  const res = await request({
    url: `/mini/recall_task/${taskId.value}`,
    method: "GET",
  })
  console.log(res)
  taskDetail.value = res.result
}

const confirmFinishStage = () => {
  request({
    url: `/mini/recall_task_soldier_stage_finish/${curItem.value.id}`,
    method: "POST",
    data: {
      task_id: curItem.value.task_id,
      stage_name: curStageName.value,
    },
  }).then((res) => {
    console.log(res)
    if (res.code === 200) {
      curItemObject[`${curStageName.value}_complete`] = 1
      curItemObject[`${curStageName.value}_complete_at`] = dayjs().format(
        "YYYY-MM-DD HH:mm:ss"
      )
      finishDialogClose()
      uni.showToast({
        title: "操作成功",
        icon: "success",
      })
    }
  })
}

const optPeople = ref({})
const detailVisible = ref(false)
const openDetail = async (item) => {
  const res = await request({
    url: `/mini/recall_task_soldier/${item.id}`,
    method: "GET",
    data: {
      task_id: taskId.value,
    },
  })
  optPeople.value = res.result
  detailVisible.value = true
}
const closeDetail = () => {
  detailVisible.value = false
}

const { page, loadPage, refresh, loadMoreStatus } = usePagination({
  loadData,
  list,
})

// 完成阶段弹窗
const finishDialogVisible = ref(false)
const curItem = ref({})
let curItemObject = null
const curStageName = ref("")
const finishDialogClose = () => {
  finishDialogVisible.value = false
}
const finishDialogOpen = (item, stageName) => {
  curItem.value = item
  curItemObject = item
  curStageName.value = stageName
  finishDialogVisible.value = true
}

const callPhone = (phone, relationId) => {
  uni.makePhoneCall({
    phoneNumber: phone,
  })

  request({
    url: `/mini/call`,
    method: "POST",
    data: {
      call_type: "recall_task_soldier",
      relation_id: relationId,
    },
  })
}

onLoad((options) => {
  console.log(options)
  taskId.value = options.taskId ? parseInt(options.taskId) : 0
  loadPage()
  loadTaskDetail()
})
onPullDownRefresh(() => {
  refresh()
})
onReachBottom(() => {
  loadPage()
})
</script>

<template>
  <view>
    <navHeader
      bg="/static/image/exam-tips-bg.png"
      :imgHeight="542"
      :showLeft="true"
      color="#fff"
      title="特殊时期工作"
    ></navHeader>

    <view class="header">
      <view class="title"> {{ taskDetail.name }} </view>
      <view class="desc">
        <image src="/static/image/group-light.png" mode="" class="icon"></image>
        <text>
          {{ taskDetail.soldier_finish_count }}人已完成 / 共{{
            taskDetail.soldier_count
          }}人
        </text>
      </view>
    </view>

    <view class="body">
      <view class="search">
        <up-search
          :show-action="false"
          placeholder="输入姓名或身份证号码搜索"
          placeholderColor="#bdc4ce"
          searchIconColor="#bdc4ce"
          v-model="keyword"
          bgColor="#ffffff"
          :searchIconSize="28"
          :inputStyle="{ height: '80rpx' }"
          @search="refresh"
          @clear="refresh"
        ></up-search>
      </view>
      <view class="list">
        <peopleItem
          v-for="item in list"
          :key="item.id"
          :item="item"
          @finishDialogOpen="(stageName) => finishDialogOpen(item, stageName)"
          @openDetail="openDetail"
          @call="(phone) => callPhone(phone, item.id)"
        />

        <up-loadmore v-if="list.length" :status="loadMoreStatus" />
      </view>
    </view>
  </view>

  <up-popup
    :show="detailVisible"
    @close="closeDetail"
    :round="10"
    mode="bottom"
  >
    <peopleDetail
      :peopleDetail="optPeople"
      @finishDialogOpen="(stageName) => finishDialogOpen(optPeople, stageName)"
      @call="(phone) => callPhone(phone, optPeople.id)"
    />
  </up-popup>
  <up-popup
    mode="center"
    :safeAreaInsetBottom="false"
    :show="finishDialogVisible"
    :round="10"
    closeable
    @close="finishDialogClose"
  >
    <view class="dialog green">
      <view class="dialog-title"> 确定完成此阶段？ </view>
      <view class="dialog-btn-group">
        <button class="dialog-btn" @click="finishDialogClose">返回</button>
        <button class="dialog-btn green" @click="confirmFinishStage">
          确定完成
        </button>
      </view>
    </view>
  </up-popup>
</template>

<style lang="scss" scoped>
.header {
  margin-top: v-bind(navHeight);
  padding-top: 30rpx;
  height: 180rpx;
  color: #ffffff;
  text-align: center;

  .title {
    font-size: 42rpx;
    font-weight: 500;
  }

  .desc {
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 24rpx;
    margin-top: 28rpx;
    opacity: 0.8;

    .icon {
      width: 30rpx;
      height: 30rpx;
      margin-right: 18rpx;
    }
  }
}

.body {
  width: 750rpx;
  background: linear-gradient(180deg, #f7f8fa 0%, #f7f8fa 100%);
  min-height: calc(100vh - v-bind(navHeight) - 180rpx);
  padding: 35rpx 24rpx;
}

.search {
  margin-bottom: 24rpx;
}

.list {
}
</style>
