<script setup>
import { ref, watch, computed } from "vue"
import {
  onLoad,
  onShow,
  onPullDownRefresh,
  onReachBottom,
} from "@dcloudio/uni-app"
import request from "@/utils/request"
import { usePagination } from "@/utils/hooks"
import { navTo } from "@/utils/utils"
import peopleItem from "./components/peopleItem.vue"
import peopleDetail from "./components/peopleDetail.vue"
import dayjs from "dayjs"

const searchType = ref("task")
const keyword = ref("")
const isSearchFocus = ref(false)
const onSearchFocus = () => {
  isSearchFocus.value = true
}
const onSearchBlur = () => {
  isSearchFocus.value = false
}

const changeSearchType = (type) => {
  searchType.value = type
  refresh()
}

const list = ref([])
const loadData = (options) => {
  return new Promise((resolve) => {
    request({
      url:
        searchType.value === "task"
          ? "/mini/recall_task"
          : "/mini/recall_task_soldier",
      method: "GET",
      data: Object.assign(
        {
          keyword: keyword.value,
        },
        options
      ),
    }).then((res) => {
      list.value.push(...(res.result?.data || []))
      resolve(res)
    })
  })
}

const { page, loadPage, refresh, loadMoreStatus } = usePagination({
  loadData,
  list,
})

const goDetail = (item) => {
  console.log(item)
  navTo(`/pages/conscript/conscript?taskId=${item.id}`)
}

const confirmFinishStage = () => {
  request({
    url: `/mini/recall_task_soldier_stage_finish/${curItem.value.id}`,
    method: "POST",
    data: {
      task_id: curItem.value.task_id,
      stage_name: curStageName.value,
    },
  }).then((res) => {
    console.log(res)
    if (res.code === 200) {
      console.log(curItemObject)
      curItemObject[`${curStageName.value}_complete`] = 1
      curItemObject[`${curStageName.value}_complete_at`] = dayjs().format(
        "YYYY-MM-DD HH:mm:ss"
      )
      finishDialogClose()
      uni.showToast({
        title: "操作成功",
        icon: "success",
      })
    }
  })
}

const optPeople = ref({})
const detailVisible = ref(false)
const openDetail = async (item) => {
  let data = {}
  if (item.task_id) {
    data.task_id = item.task_id
  }
  const res = await request({
    url: `/mini/recall_task_soldier/${item.id}`,
    method: "GET",
    data,
  })
  optPeople.value = res.result
  detailVisible.value = true
}
const closeDetail = () => {
  detailVisible.value = false
}

// 完成阶段弹窗
const finishDialogVisible = ref(false)
const curItem = ref({})
let curItemObject = null
const curStageName = ref("")
const finishDialogClose = () => {
  finishDialogVisible.value = false
}
const finishDialogOpen = (item, stageName) => {
  curItem.value = item
  curItemObject = item
  curStageName.value = stageName
  finishDialogVisible.value = true
}

const callPhone = (phone, relationId) => {
  uni.makePhoneCall({
    phoneNumber: phone,
  })
  request({
    url: `/mini/call`,
    method: "POST",
    data: {
      call_type: "recall_task_soldier",
      relation_id: relationId,
    },
  })
}

onLoad((options) => {
  console.log(options)
  loadPage()
})
onPullDownRefresh(() => {
  refresh()
})
onReachBottom(() => {
  loadPage()
})
</script>

<template>
  <view>
    <navHeader
      bg="/static/image/conscript-bg.png"
      :imgHeight="374"
      :showLeft="true"
      color="#fff"
    ></navHeader>
    <view class="body">
      <view class="search">
        <view class="search-type">
          <view class="radio" @click="changeSearchType('task')">
            <image
              v-if="searchType === 'task'"
              src="/static/image/radio-checked.svg"
              mode=""
              class="icon"
            ></image>
            <image
              v-else
              src="/static/image/radio-unchecked.svg"
              mode=""
              class="icon"
            ></image>
            搜任务
          </view>

          <view class="radio" @click="changeSearchType('people')">
            <image
              v-if="searchType === 'people'"
              src="/static/image/radio-checked.svg"
              mode=""
              class="icon"
            ></image>
            <image
              v-else
              src="/static/image/radio-unchecked.svg"
              mode=""
              class="icon"
            ></image>
            搜人
          </view>
        </view>

        <view class="divider"></view>
        <up-search
          :show-action="false"
          :searchIcon="isSearchFocus && keyword ? '' : 'search'"
          placeholder="输入关键词"
          placeholderColor="#bdc4ce"
          searchIconColor="#bdc4ce"
          v-model="keyword"
          bgColor="#ffffff"
          :searchIconSize="28"
          :inputStyle="{ height: '78rpx' }"
          @focus="onSearchFocus"
          @blur="onSearchBlur"
          @search="refresh"
          @clear="refresh"
        />
      </view>

      <view class="list">
        <template v-if="searchType === 'task'">
          <view
            v-for="item in list"
            :key="item.id"
            class="item"
            @click="goDetail(item)"
          >
            <view class="title">{{ item.name }}</view>
            <view class="desc">
              <image src="/static/image/group.png" mode="" class="icon"></image>
              <text>
                {{ item.soldier_finish_count }}人已完成 / 共{{
                  item.soldier_count
                }}人
              </text>
            </view>
          </view>
        </template>
        <template v-else>
          <peopleItem
            v-for="item in list"
            :key="item.id"
            :item="item"
            @finishDialogOpen="(stageName) => finishDialogOpen(item, stageName)"
            @openDetail="openDetail"
            @call="(phone) => callPhone(phone, item.id)"
          />
        </template>

        <up-loadmore v-if="list.length" :status="loadMoreStatus" />
      </view>
    </view>
  </view>

  <up-popup
    :show="detailVisible"
    @close="closeDetail"
    :round="10"
    mode="bottom"
  >
    <peopleDetail
      :peopleDetail="optPeople"
      @finishDialogOpen="(stageName) => finishDialogOpen(optPeople, stageName)"
      @call="(phone) => callPhone(phone, optPeople.id)"
    />
  </up-popup>

  <up-popup
    mode="center"
    :safeAreaInsetBottom="false"
    :show="finishDialogVisible"
    :round="10"
    closeable
    @close="finishDialogClose"
  >
    <view class="dialog green">
      <view class="dialog-title"> 确定完成此阶段？ </view>
      <view class="dialog-btn-group">
        <button class="dialog-btn" @click="finishDialogClose">返回</button>
        <button class="dialog-btn green" @click="confirmFinishStage">
          确定完成
        </button>
      </view>
    </view>
  </up-popup>
</template>

<style lang="scss" scoped>
.body {
  width: 750rpx;
  background: rgba(247, 248, 250, 0.65);
  border-radius: 44rpx 44rpx 0rpx 0rpx;
  border: 1rpx solid #fff;
  backdrop-filter: blur(10rpx);
  min-height: calc(100vh - 430rpx - 43rpx);
  margin-top: 331rpx;
  padding: 35rpx 24rpx;
}

.search {
  display: flex;
  align-items: center;
  height: 80rpx;
  background: #ffffff;
  box-shadow: 0rpx 3rpx 40rpx 1rpx rgba(0, 0, 0, 0.04);
  border-radius: 50rpx 50rpx 50rpx 50rpx;
  margin-bottom: 20rpx;
  padding: 0 34rpx;

  .search-type {
    display: flex;
    margin-right: 20rpx;

    .radio {
      display: flex;
      align-items: center;
      font-size: 26rpx;
      color: #bdc4ce;
      margin-right: 8rpx;

      .icon {
        width: 30rpx;
        height: 30rpx;
        margin-right: 8rpx;
      }
    }
  }

  .divider {
    width: 1rpx;
    height: 30rpx;
    background: #bdc4ce;
    margin-right: 20rpx;
  }

  :deep(.u-search__content) {
    position: relative;
    padding-left: 0;
    padding-right: 55rpx;

    .u-search__content__icon {
      position: absolute;
      right: 0rpx;
      z-index: 0;
    }

    .u-search__content__close {
      position: absolute;
      right: 0rpx;
      z-index: 1;
    }
  }
}

.list {
  .item {
    background: rgba(255, 255, 255, 0.94);
    box-shadow: 0rpx 6rpx 13rpx 1rpx rgba(0, 0, 0, 0.04);
    border-radius: 20rpx 20rpx 20rpx 20rpx;
    border: 2rpx solid #ffffff;
    margin-bottom: 20rpx;
    padding: 47rpx;

    .title {
      font-size: 30rpx;
      font-weight: 500;
      color: #333;
    }

    .desc {
      display: flex;
      align-items: center;
      font-size: 24rpx;
      color: #bdc4ce;
      margin-top: 20rpx;

      .icon {
        width: 30rpx;
        height: 30rpx;
        margin-right: 10rpx;
      }
    }
  }
}
</style>
