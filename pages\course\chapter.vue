<script setup>
import { ref, watch, computed } from "vue"
import {
  onLoad,
  onShow,
  onPullDownRefresh,
  onReachBottom,
} from "@dcloudio/uni-app"
import request from "@/utils/request"
import { usePagination } from "@/utils/hooks"
import { navTo, numberToChinese } from "@/utils/utils"

const goDetail = (item) => {
  switch (item.template) {
    case "chapter":
      navTo(
        "/pages/course/chapter",
        {
          parentId: item.parent_id,
          courseId: item.relation_id,
        },
        "redirectTo"
      )
      break
    case "video":
      navTo("/pages/course/learnCourse", {
        parentId: item.parent_id,
        chapterId: item.id,
        courseId: item.course_id,
      })
      break
    case "voice":
      navTo("/pages/course/learnCourse", {
        parentId: item.parent_id,
        chapterId: item.id,
        courseId: item.course_id,
      })
      break
    case "practice":
      navTo("/pages/exam/exam", {
        type: "practice",
        paperId: item.relation_id,
        subChapterId: item.id,
      })
      break
    case "assessment":
      navTo("/pages/exam/examTips", {
        type: "assessment",
        paperId: item.relation_id,
        subChapterId: item.id,
      })
      break
    default:
      break
  }
}

const parentId = ref(0)
const courseId = ref(0)

const list = ref([])
const loadSections = (options) => {
  return new Promise((resolve) => {
    request({
      url: "/mini/chapter",
      method: "GET",
      data: Object.assign(
        {
          parent_id: parentId.value,
          course_id: courseId.value,
        },
        options
      ),
    }).then((res) => {
      list.value.push(...(res.result?.data || []))
      resolve(res)
    })
  })
}

const { page, loadPage, refresh, loadMoreStatus } = usePagination({
  loadData: loadSections,
  list,
})

const chapter = ref({})
const loadChapter = async () => {
  const res = await request({
    url: `/mini/chapter/${parentId.value}`,
    method: "GET",
  })
  chapter.value = res.result
}

onLoad(async (options) => {
  parentId.value = options.parentId ? parseInt(options.parentId) : 0
  courseId.value = options.courseId ? parseInt(options.courseId) : 0

  loadChapter()
})

onShow(() => {
  refresh()
})

onPullDownRefresh(() => {
  refresh()
})
onReachBottom(() => {
  loadPage()
})
</script>

<template>
  <view class="page">
    <navHeader
      bg="/static/image/bg.png"
      :title="`第${numberToChinese(chapter.sort)}章`"
      :showLeft="true"
      color="#000"
    ></navHeader>

    <view class="header">
      <view class="banner">
        <image src="/static/image/chapter-bg.png" mode=""></image>
        <view class="content">
          <view class="no1"> 第{{ numberToChinese(chapter.sort) }}章 </view>
          <view class="title">
              <view>{{ chapter.name }}</view>
          </view>
        </view>
      </view>
    </view>
    <view class="body">
      <view class="title">
        <view class="green"> </view>
        <text></text> 课程目录
      </view>

      <view class="list">
        <courseCatalog :list="list" @clickItem="goDetail" />

        <up-loadmore v-if="list.length" :status="loadMoreStatus" />
      </view>
    </view>

    <view
      class="error-notebook"
      @click="navTo('/pages/errorNotebook/errorNotebook')"
    >
      <image
        src="/static/image/error-notebook.png"
        mode=""
        class="icon"
      ></image>
      <view class="title">错题练习</view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.page {
  background: #f7f8fa;
}
.header {
  margin-top: 200rpx;
  .banner {
    width: 702rpx;
    height: 318rpx;
    margin: 0 auto;
    position: relative;
    padding: 50rpx;
    image {
      width: 100%;
      height: 100%;
      object-fit: cover;
      position: absolute;
      border-radius: 20rpx 20rpx 20rpx 20rpx;
      top: 0;
      left: 0;
      overflow: hidden;
    }
  }
  .content {
    display: flex;
    flex-direction: column;
    width: 602rpx;
    height: 218rpx;

    opacity: 0.9;
    margin: 0 auto;
    padding: 22rpx 0;
    .no1 {
      font-weight: 500;
      font-size: 34rpx;
      color: #ffffff;
      text-align: center;
      font-style: normal;
      text-transform: none;
    }
    .title {
      font-weight: 800;
      font-size: 44rpx;
      color: #ffffff;
      text-align: center;
      display: flex;
      flex-direction: column;
      justify-content: center;
      flex: 1;

      view {
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
}
.body {
  padding: 24rpx;
  background: #f7f8fa;
  .title {
    font-weight: bold;
    font-size: 34rpx;
    color: #333333;
    display: flex;
    .green {
      width: 6rpx;
      height: 34rpx;
      background: #577f49;
      border-radius: 0rpx 16rpx 16rpx 0rpx;
      margin-right: 12rpx;
      margin-top: 6rpx;
    }
  }
  .list {
    padding-top: 20rpx;
  }
}

.error-notebook {
  width: 166rpx;
  height: 166rpx;
  background: #ffffff;
  box-shadow: 0rpx 20rpx 48rpx 1rpx rgba(33, 35, 44, 0.2);
  border-radius: 50rpx 50rpx 50rpx 50rpx;
  border: 1rpx solid #e1e1e1;

  position: fixed;
  z-index: 9;
  left: 42rpx;
  bottom: 140rpx;

  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 17rpx;

  .icon {
    width: 35rpx;
    height: 43rpx;
  }

  .title {
    font-weight: 500;
    font-size: 28rpx;
    color: #21232c;
    text-align: center;
  }
}
</style>
