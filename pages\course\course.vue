<script setup>
import { ref, watch, computed } from "vue"
import {
  onLoad,
  onShow,
  onPullDownRefresh,
  onReachBottom,
} from "@dcloudio/uni-app"
import request from "@/utils/request"
import { usePagination } from "@/utils/hooks"
import { navTo } from "@/utils/utils"
import dayjs from "dayjs"

const goDetail = (index, course) => {
  let chapters = chaptersMap.value[course.id]?.chapters || []
  let currentItem = chapters[index]
  let lastItem = chapters[index - 1]

  let lastItemAllDown =
    lastItem?.sub_chapter_study_count === lastItem?.sub_chapter_count

  if (!lastItemAllDown) {
    uni.showToast({
      title: "请先学习未完成的章节",
      icon: "none",
    })
    return
  }

  console.log(currentItem)
  navTo(
    `/pages/course/chapter?parentId=${currentItem.id}&courseId=${currentItem.course_id}`
  )
}

const list = ref([])
const loadData = (options) => {
  return new Promise((resolve) => {
    request({
      url: "/mini/course",
      method: "GET",
      data: Object.assign({}, options),
    }).then((res) => {
      list.value.push(...(res.result?.data || []))
      resolve(res)
    })
  })
}

const { page, loadPage, refresh, loadMoreStatus } = usePagination({
  loadData,
  list,
})

const chaptersMap = ref({})
const loadCourseData = async (id) => {
  const res = await request({
    url: `/mini/chapter`,
    method: "GET",
    data: {
      parent_id: 0,
      course_id: id,
      page_size: 999,
    },
  })
  chaptersMap.value[id] = {
    expanded: true,
    chapters: res.result?.data || [],
  }
}
const onCourseClick = (id) => {
  if (chaptersMap.value[id]) {
    chaptersMap.value[id].expanded = !chaptersMap.value[id].expanded
    if (!chaptersMap.value[id].expanded) {
      return
    }
  }
  loadCourseData(id)
}

const refreshChapters = () => {
  for (let key in chaptersMap.value) {
    let expanded = chaptersMap.value[key].expanded
    if (expanded) {
      loadCourseData(key)
    }
  }
}

onLoad((options) => {
  // loadPage()
})
onShow(() => {
  refresh()
  refreshChapters()
})
onPullDownRefresh(() => {
  refresh()
  refreshChapters()
})
onReachBottom(() => {
  loadPage()
})
</script>

<template>
  <navHeader
    bg="/static/image/learn-bg.png"
    :imgHeight="374"
    :showLeft="true"
    color="#fff"
  ></navHeader>
  <view class="body">
    <view
      class="course"
      v-for="course in list"
      :key="course.id"
      @click="onCourseClick(course.id)"
    >
      <view class="title-box">
        <view class="title"> {{ course.name }} </view>
        <image
          src="/static/image/right.png"
          class="arrow"
          :class="{ rotate: chaptersMap[course.id]?.expanded }"
        />
      </view>
      <view class="count">
        共{{ course.chapter_count }}章({{ course.sub_chapter_count }}小节)
      </view>
      <view class="info" v-show="chaptersMap[course.id]?.expanded">
        <view class="info-text"> {{ course.description }} </view>
        <view class="chapter-list">
          <view
            class="chapter"
            @click.stop="goDetail(chapterIndex, course)"
            v-for="(chapter, chapterIndex) in chaptersMap[course.id]?.chapters"
            :key="chapter.id"
          >
            <view class="chapter-left">
              <view class="title"> {{ chapter.name }} </view>
              <text class="date" v-if="chapter.last_study_at">
                上次学习：
                {{ dayjs(chapter.last_study_at).format("YYYY/MM/DD HH:mm") }}
              </text>
              <text class="date" v-else> 未学习 </text>
            </view>
            <view class="chapter-right">
              <l-circle
                size="60"
                :percent="
                  (chapter.sub_chapter_study_count /
                    chapter.sub_chapter_count) *
                  100
                "
                canvas
                trailColor="#ffffff"
                strokeColor="#577F49"
              >
                <view>
                  <text class="count">{{
                    chapter.sub_chapter_study_count || "0"
                  }}</text
                  >/
                  <text>{{ chapter.sub_chapter_count }}</text>
                </view>
              </l-circle>
            </view>
          </view>
        </view>
      </view>
    </view>

    <empty v-if="!list.length && loadMoreStatus === 'nomore'" />
    <up-loadmore v-if="list.length" :status="loadMoreStatus" />
  </view>
</template>

<style lang="scss" scoped>
.body {
  width: 750rpx;
  background: rgba(247, 248, 250, 0.65);
  border-radius: 44rpx 44rpx 0rpx 0rpx;
  border: 1rpx solid #fff;
  backdrop-filter: blur(10rpx);
  min-height: calc(100vh - 430rpx - 43rpx);
  margin-top: 331rpx;
  padding: 35rpx 24rpx;
}

.course {
  background: rgba(255, 255, 255, 0.94);
  box-shadow: 0rpx 6rpx 13rpx 1rpx rgba(0, 0, 0, 0.04);
  border-radius: 20rpx 20rpx 20rpx 20rpx;
  border: 2rpx solid #ffffff;
  margin-bottom: 20rpx;
  padding: 30rpx;

  .title-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12rpx 6rpx 0 12rpx;
    margin-bottom: 18rpx;

    .title {
      font-weight: bold;
      font-size: 32rpx;
      color: #333333;
    }

    .arrow {
      width: 8rpx;
      height: 16rpx;

      &.rotate {
        transform: rotate(90deg);
      }
    }
  }

  .count {
    font-size: 26rpx;
    color: #bdc4ce;
    padding: 0 12rpx;
  }

  .info {
    margin-top: 10rpx;
    .info-text {
      font-size: 26rpx;
      color: #9ca3b5;
      padding: 0 12rpx;
      margin-bottom: 30rpx;
    }
  }
}

.chapter {
  height: 175rpx;
  background: #f7f8fa;
  box-shadow: 0rpx 6rpx 13rpx 1rpx rgba(0, 0, 0, 0.04);
  border-radius: 20rpx 20rpx 20rpx 20rpx;
  display: flex;
  justify-content: space-between;
  gap: 35rpx;
  padding: 35rpx;
  margin-bottom: 20rpx;

  .chapter-left {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    overflow: hidden;

    .title {
      font-weight: 500;
      font-size: 32rpx;
      color: #333333;
      text-align: left;
      font-style: normal;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
    }
    .date {
      font-weight: 400;
      font-size: 24rpx;
      color: #bdc4ce;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }
  }
  .chapter-right {
    flex-shrink: 0;
    font-size: 22rpx;
    color: #21232c;
    text-align: center;
    .count {
      font-family: Arial, Arial;
      font-weight: 400;
      font-size: 30rpx;
      color: #577f49;
      text-align: center;
    }
  }
}
</style>
