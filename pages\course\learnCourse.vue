<script setup>
import { ref, watch, computed, nextTick } from "vue"
import { onLoad, onShow, onUnload, onPullDownRefresh } from "@dcloudio/uni-app"
import request from "@/utils/request"
import { navTo, numberToChinese } from "@/utils/utils"

const courseId = ref(0)
const parentId = ref(0)
const chapterId = ref(0)

const popStyle = {
  borderRadius: "20rpx 20rpx 20rpx 20rpx",
  width: "630rpx",
  height: "410rpx",
  background: "linear-gradient( 179deg, #F3FFEF 0%, #FFFFFF 100%)",

  border: "2rpx solid #FFFFFF",
  padding: "0 41rpx",
  boxSizing: "border-box",
  margin: "0 auto",
}

const mediaPlayerRef = ref(null)
const showNextPopup = ref(false)
function openNextPopup() {
  showNextPopup.value = true
}
function closeNextPopup() {
  showNextPopup.value = false
}
const handleReplay = () => {
  closeNextPopup()
  mediaPlayerRef.value.replay()
}

const nextChapter = computed(() => {
  const index = list.value.findIndex(
    (item) => item.id === currentChapter.value?.id
  )
  if (index === -1) {
    return null
  } else if (index === list.value.length - 1) {
    return null
  } else {
    return list.value[index + 1]
  }
})

const handleNext = () => {
  closeNextPopup()
  if (nextChapter.value) {
    onClickItem(nextChapter.value)
  }
}

const back = () => {
  uni.navigateBack({
    delta: 2,
  })
}

const onMediaEnded = (e) => {
  console.log("onMediaEnded====================================", e)
  uploadCourseProcess(true, e.currentTime)

  mediaPlayerRef.value?.stop()
  openNextPopup()
}
const onMediaPause = (e) => {
  console.log("onMediaPause====================================", e)
  uploadCourseProcess(false, e.currentTime)
}
const onMediaTimeUpdate = () => {}

function getTime(time) {
  // 00:00:00
  const duration = parseInt(time)
  const hours = Math.floor(duration / 3600)
  const minutes = Math.floor((duration - hours * 3600) / 60)
  const seconds = duration - hours * 3600 - minutes * 60

  const _hours = hours < 10 ? `0${hours}` : hours
  const _minutes = minutes < 10 ? `0${minutes}` : minutes
  const _seconds = seconds < 10 ? `0${seconds}` : seconds

  return `${_hours}:${_minutes}:${_seconds}`
}
function getTimeNumber(timeText) {
  const [hours, minutes, seconds] = timeText.split(":")
  return parseInt(hours) * 3600 + parseInt(minutes) * 60 + parseInt(seconds)
}

const uploadCourseProcess = async (ended, currentTime) => {
  console.log("上传学习进度", currentChapter.value)
  let _status = currentChapter.value?.process?.status || 0
  if (ended) {
    // currentTime = 0
    _status = 1
  }
  const res = await request({
    url: `/mini/upload_course_process`,
    method: "POST",
    data: {
      chapter_id: currentChapter.value.id,
      current_time: currentTime ? getTime(currentTime) : "00:00:00",
      status: _status,
    },
  })
  if (res.code === 200) {
    console.log("上传学习进度成功")
    loadList()
  }
}

const list = ref([])
const loadList = async () => {
  const res = await request({
    url: `/mini/chapter`,
    method: "GET",
    data: {
      page_size: 999,
      parent_id: parentId.value,
      course_id: courseId.value,
    },
  })
  list.value = res.result?.data || []
}

const currentChapter = ref(null)

const loadDetail = async () => {
  const res = await request({
    url: `/mini/chapter/${chapterId.value}`,
  })
  currentChapter.value = res.result
}

const mediaSrc = ref("")
const mediaStartTime = ref(0)
watch(
  () => currentChapter.value,
  (val) => {
    mediaSrc.value = ""
    mediaStartTime.value = 0
    if (val && ["video", "voice"].includes(val.template)) {
      mediaStartTime.value = getTimeNumber(
        val.process?.current_time || "00:00:00"
      )
      mediaSrc.value =
        val.template === "video" ? val.video?.full_path : val.voice?.full_path
    }
  },
  { immediate: true }
)

const onClickItem = async (item) => {
  if (mediaPlayerRef.value?.playing) {
    if (currentChapter.value?.id === item.id) {
      return
    }

    uploadCourseProcess(false, mediaPlayerRef.value.currentTime)
    mediaPlayerRef.value?.stop()
  }
  mediaStartTime.value = 0

  switch (item.template) {
    case "chapter":
      navTo(
        "/pages/course/chapter",
        {
          parentId: item.parent_id,
          courseId: item.relation_id,
        },
        "redirectTo"
      )
      break
    case "video":
      chapterId.value = item.id
      loadDetail()
      loadList()
      break
    case "voice":
      chapterId.value = item.id
      loadDetail()
      loadList()
      break
    case "practice":
      navTo("/pages/exam/exam", {
        type: "practice",
        paperId: item.relation_id,
        subChapterId: item.id,
      })
      break
    case "assessment":
      navTo("/pages/exam/examTips", {
        type: "assessment",
        paperId: item.relation_id,
        subChapterId: item.id,
      })
      break
    default:
      break
  }
}

onLoad((options) => {
  courseId.value = options.courseId ? parseInt(options.courseId) : 0
  parentId.value = options.parentId ? parseInt(options.parentId) : 0
  chapterId.value = options.chapterId ? parseInt(options.chapterId) : 0
})

onUnload(() => {
  if (
    currentChapter.value &&
    ["video", "voice"].includes(currentChapter.value.template) &&
    mediaPlayerRef.value?.playing
  ) {
    uploadCourseProcess(false, mediaPlayerRef.value.currentTime)
    mediaPlayerRef.value?.stop()
  }
})

onShow(() => {
  loadList()
  loadDetail()
})

onPullDownRefresh(() => {
  list.value = []
  loadList()
})
</script>

<template>
  <view class="page">
    <navHeader
      bg="/static/image/bg.png"
      :title="`第${currentChapter?.sort}节`"
      :showLeft="true"
      color="#000"
    ></navHeader>
    <view class="header">
      <mediaPlayer
        ref="mediaPlayerRef"
        v-if="
          currentChapter &&
          ['video', 'voice'].includes(currentChapter.template) &&
          mediaSrc
        "
        :type="currentChapter.template"
        :src="mediaSrc"
        :startTime="mediaStartTime"
        :title="currentChapter.name"
        @ended="onMediaEnded"
        @pause="onMediaPause"
        @timeupdate="onMediaTimeUpdate"
      />
    </view>

    <view class="body">
      <view class="title">
        <view class="green"> </view>
        <text></text> 课程目录
      </view>
      <view class="list">
        <courseCatalog :list="list" @clickItem="onClickItem" />
      </view>
    </view>
    <up-popup
      :customStyle="popStyle"
      round="20"
      closeable
      :safeAreaInsetBottom="false"
      :show="showNextPopup"
      mode="center"
      @close="closeNextPopup"
      @open="openNextPopup"
    >
      <view class="popbox">
        <image
          class="ribbon"
          src="/static/image/ribbon.png"
          mode="widthFix"
        ></image>
        <image class="hua" src="/static/image/hua.png" mode=""></image>
        <view class="center">
          {{ nextChapter ? "您完成了本节课程" : "您完成了本章课程" }}
        </view>
        <view class="footer">
          <view class="btn" @click="handleReplay"> 再学一遍 </view>

          <template>
            <view class="btn next" @click="handleNext" v-if="nextChapter">
              下一节
            </view>
            <view class="btn next" v-else @click="back"> 返回课程 </view>
          </template>
        </view>
      </view>
    </up-popup>
  </view>
</template>

<style lang="scss" scoped>
.page {
  background: #f7f8fa;
}
.header {
  margin-top: 200rpx;
  height: 422rpx;
  width: 750rpx;
  position: relative;
  video {
    width: 100%;
    height: 100%;
  }
  .video-btn {
    position: absolute;
    bottom: 0rpx;
    left: 1rpx;
  }
}
.body {
  padding: 24rpx;
  background: #f7f8fa;
  .title {
    font-weight: bold;
    font-size: 34rpx;
    color: #333333;
    display: flex;
    .green {
      width: 6rpx;
      height: 34rpx;
      background: #577f49;
      border-radius: 0rpx 16rpx 16rpx 0rpx;
      margin-right: 12rpx;
      margin-top: 6rpx;
    }
  }
  .list {
    padding-top: 20rpx;
  }
}

.popbox {
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  height: 100%;

  .ribbon {
    width: 750rpx;
    position: fixed;
    top: 0;
    left: 0;
    z-index: -1;
  }

  .hua {
    width: 282rpx;
    height: 282rpx;
    margin: 0 auto;
    margin-top: -200rpx;
  }
  .center {
    height: 90rpx;
    font-weight: bold;
    font-size: 38rpx;
    color: #06121e;
    line-height: 40rpx;
    text-align: center;
  }
  .footer {
    display: flex;
    justify-content: space-around;
    .btn {
      width: 248rpx;
      height: 100rpx;
      background: #f7f8fa;
      border-radius: 55rpx 55rpx 55rpx 55rpx;
      color: #434a54;

      font-weight: 500;
      font-size: 32rpx;
      line-height: 100rpx;
      text-align: center;
    }
    .next {
      background: #577f49;
      color: #fff;
    }
  }
}
</style>
