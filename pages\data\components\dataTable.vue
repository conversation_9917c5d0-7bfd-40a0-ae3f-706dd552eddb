<script setup>
import { ref, watch, computed } from "vue"
import dayjs from "dayjs"

const props = defineProps({
  type: {
    type: String,
    default: "",
  },
  source: {
    type: Array,
    default: () => [],
  },
  columns: {
    type: Array,
    default: () => [],
  },
})

const emit = defineEmits(["itemClick"])
</script>

<template>
  <view class="data-table">
    <view class="data-table-header">
      <view
        class="data-table-header-item"
        v-for="column in columns"
        :key="column.key"
        :style="
          column.width ? { maxWidth: column.width, minWidth: column.width } : {}
        "
        >{{ column.title }}</view
      >
    </view>
    <view class="data-table-content">
      <view
        class="data-table-content-item"
        v-for="(item, index) in source"
        :key="item.id"
        @click="emit('itemClick', item, index)"
      >
        <view class="data-table-content-item-row">
          <view
            class="data-table-content-item-col"
            v-for="column in columns"
            :key="column.key"
            :style="
              column.width
                ? { maxWidth: column.width, minWidth: column.width }
                : {}
            "
          >
            <view class="score" v-if="column.key === 'study_count'">
              <text class="left">{{ item.study_count }}</text>
              <text class="right">/{{ item.need_study_count }}</text>
            </view>
            <view class="score" v-else-if="column.key === 'pass_count'">
              <text class="left"
                >{{
                  Math.round((item.pass_count / item.total_count) * 100) || 0
                }}%</text
              >
            </view>
            <view class="score" v-else-if="column.key === 'report_count'">
              <text class="left">{{ item.work_task_done_count }}</text>
              <text class="right">/{{ item.work_task_count }}</text>
            </view>
            <template v-else>{{ item[column.key] }}</template>
          </view>
        </view>
        <view
          class="data-table-content-item-sub"
          v-if="item.expand && item.resultList?.length"
        >
          <scroll-view scroll-y v-if="type === 'paper'" class="paper-result">
            <view
              class="result-item"
              v-for="result in item.resultList"
              :key="result.id"
            >
              <view class="result-info">
                <view class="info-time" v-if="result.end_at">
                  {{ dayjs(result.end_at).format("YYYY/MM/DD HH:mm") }}
                </view>
                <view class="info-title">{{ result.paper_title }}</view>
              </view>
              <view class="result-score">
                {{ result.score }}分 {{ result.is_pass ? "合格" : "不合格" }}
              </view>
            </view>
          </scroll-view>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.data-table {
  background-color: #ffffff;
  box-shadow: 0rpx 6rpx 13rpx 1rpx rgba(0, 0, 0, 0.04);
  border-radius: 16rpx 16rpx 16rpx 16rpx;
  margin: 0 auto;

  .data-table-header {
    display: flex;
    justify-content: space-between;
    padding: 20rpx 30rpx;
    // border-bottom: 1rpx solid #e6e6e6;
    gap: 30rpx;

    .data-table-header-item {
      flex-grow: 1;
      font-weight: 500;
      font-size: 24rpx;
      color: #bdc4ce;
    }
  }
  .data-table-content {
    padding: 0 30rpx;

    .data-table-content-item {
      padding: 20rpx 0;
      border-bottom: 1rpx solid #e6e6e6;

      &:last-child {
        border-bottom: none;
      }

      .data-table-content-item-row {
        display: flex;
        justify-content: space-between;
        gap: 30rpx;

        .data-table-content-item-col {
          flex-grow: 1;
          font-weight: 400;
          font-size: 30rpx;
          color: #21232c;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }

      .data-table-content-item-sub {
        .paper-result {
          margin-top: 24rpx;
          background-color: #f8f9fb;
          border-radius: 14rpx 14rpx 14rpx 14rpx;
          max-height: 432rpx;
          position: relative;
          overflow: visible;

          // 气泡小三角
          &::before {
            content: "";
            position: absolute;
            top: -24rpx;
            left: 24rpx;
            width: 0;
            height: 0;
            border: 12rpx solid transparent;
            border-bottom: 12rpx solid #f8f9fb;
          }
          .result-item {
            display: flex;
            justify-content: space-between;
            padding: 18rpx 24rpx;
            gap: 30rpx;

            .result-info {
              flex: 1;
              overflow: hidden;

              .info-time {
                margin-bottom: 8rpx;
                font-size: 24rpx;
                color: #9ca3b5;
              }
              .info-title {
                font-size: 24rpx;
                color: #39455b;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              }
            }
            .result-score {
              flex-shrink: 0;
              font-size: 24rpx;
              color: #577f49;
            }
          }
        }
      }
    }
  }
}

.score {
  font-weight: 500;

  .left {
    font-size: 30rpx;
    color: #577f49;
  }
  .right {
    font-size: 24rpx;
    color: #21232c;
  }
}
</style>
