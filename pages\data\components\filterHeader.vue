<script setup>
import { ref, watch, computed, toRef, onMounted } from "vue"

const props = defineProps({
  title: {
    type: String,
    default: "",
  },
  filters: {
    type: Array,
    default: () => [],
  },
  departmentData: {
    type: Array,
    default: () => [],
  },
})

const emit = defineEmits(["change", "selectDepartment"])

const _filters = toRef(props, "filters")
const filterLabels = ref([])

const treeRef = ref(null)
const departmentPickerVisible = ref(false)
const closeDepartmentPicker = () => {
  departmentPickerVisible.value = false
}
const openDepartmentPicker = () => {
  departmentType.value = "sub"
  departmentPickerVisible.value = true
}
const onDepartmentChange = (val) => {
  const departmentFilter = _filters.value.find((f) => f.key === "department_code")
  if (departmentFilter) {
    departmentFilter.value = "sub"
    updateFilters()
    const values = _filters.value.map((f) => f.value)
    emit("selectDepartment", val)
    emit("change", values)
    setTimeout(() => {
      closeDepartmentPicker()
      filterRef.value.close()
    }, 300)
  }
}
const confirmDepartment = () => {
  treeRef.value.confirm()
}

const departmentType = ref("sub")
const onDepartmentTypeChange = (type) => {
  const departmentFilter = _filters.value.find(
    (f) => f.key === "department_code"
  )
  departmentType.value = type
  if (type === "current") {
    departmentFilter.value = "current"
    updateFilters()
    const values = _filters.value.map((f) => f.value)
    emit("change", values)

    setTimeout(() => {
      closeDepartmentPicker()
      filterRef.value.close()
    }, 300)
  }
}

const filterRef = ref()

const updateFilters = () => {
  filterLabels.value = _filters.value.map((f, index) => {
    const currentOption = f.options.find((o) => o.value == f.value)
    if (currentOption) {
      return currentOption.label
    } else {
      return f.title
    }
  })
}

const onFilterChange = (val, index) => {
  const filterItem = _filters.value[index]
  console.log(val, index, filterItem)
  if (filterItem.value === val) {
    return
  }
  if (filterItem.key === "department_code" && val === "sub") {
    openDepartmentPicker()
    filterRef.value.close()
  } else {
    filterItem.value = val
    updateFilters()
    const values = _filters.value.map((f) => f.value)
    emit("change", values)
  }
}

const onFilterOpen = () => {
  const departmentFilter = _filters.value.find(
    (f) => f.key === "department_code"
  )
  if (departmentFilter && departmentFilter.value === "sub") {
    openDepartmentPicker()
  }
}

watch(
  _filters,
  (val) => {
    updateFilters()
  },
  { immediate: true, deep: true }
)

onMounted(() => {})
</script>

<template>
  <view class="top">
    <view class="title"> {{ title }} </view>
    <view class="filters">
      <u-dropdown
        ref="filterRef"
        active-color="#577f49"
        inactive-color="#bdc4ce"
        @open="onFilterOpen"
      >
        <u-dropdown-item
          v-for="(item, index) in _filters"
          :key="index"
          :value="item.value"
          :title="filterLabels[index] || item.title"
          :options="item.options || []"
          @change="(e) => onFilterChange(e, index)"
        ></u-dropdown-item>
      </u-dropdown>
    </view>
  </view>

  <up-popup
    closeable
    :show="departmentPickerVisible"
    @close="closeDepartmentPicker"
    :round="10"
    @open="openDepartmentPicker"
    mode="bottom"
  >
    <view class="department-picker">
      <view class="picker-title">筛选</view>
      <view class="type-options">
        <view
          class="type-option"
          :class="{ active: departmentType === 'current' }"
          @click="onDepartmentTypeChange('current')"
          >本单位</view
        >
        <view
          class="type-option"
          :class="{ active: departmentType === 'sub' }"
          @click="onDepartmentTypeChange('sub')"
          >下级单位</view
        >
      </view>
      <view class="picker-content">
        <treePicker
          v-if="departmentData.length"
          ref="treeRef"
          valueKey="code"
          :data="departmentData"
          themeColor="#577F49"
          @change="onDepartmentChange"
        />
      </view>
      <view class="btn-wrapper">
        <button class="btn" @click="confirmDepartment">确定</button>
      </view>
    </view>
  </up-popup>
</template>

<script>
export default {
  options: {
    styleIsolation: "shared",
  },
}
</script>

<style lang="scss">
:deep(.u-dropdown__menu) {
  justify-content: flex-end !important;
  height: 60rpx !important;
  z-index: 0 !important;
  gap: 20rpx !important;
  padding-right: 24rpx !important;

  .u-dropdown__menu__item {
    flex: unset;
    min-width: 165rpx;
    height: 60rpx;
    line-height: 60rpx;
    padding: 0 23rpx;
    background: #ffffff;
    border-radius: 37rpx 37rpx 37rpx 37rpx;
    color: #21232c;
    font-size: 26rpx;
  }
}
</style>

<style lang="scss" scoped>
.top {
  // display: flex;
  margin: 10rpx 0;
  // justify-content: space-between;
  // align-items: center;
  position: relative;
}

.title {
  font-size: 32rpx;
  color: #111111;
  font-weight: bold;
  line-height: 60rpx;
  position: absolute;
  top: 0;
  left: 24rpx;
}

.filters {
  display: flex;
  align-items: center;
  gap: 20rpx;

  .filter-item {
    min-width: 165rpx;
    height: 60rpx;
    background: #ffffff;
    border-radius: 37rpx 37rpx 37rpx 37rpx;
    font-weight: 500;
    font-size: 26rpx;
    color: #577f49;
    text-align: center;
    padding: 0 24rpx;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .down {
      width: 16rpx;
      height: 8rpx;
    }
  }
}

.department-picker {
  padding: 24rpx;
  padding-bottom: 0;
  height: 75vh;
  display: flex;
  flex-direction: column;

  .picker-title {
    font-size: 32rpx;
    color: #111111;
    font-weight: bold;
    margin-bottom: 24rpx;
    text-align: center;
  }

  .type-options {
    display: flex;
    gap: 20rpx;
    .type-option {
      width: 189rpx;
      height: 72rpx;
      line-height: 72rpx;
      text-align: center;
      font-weight: 500;
      background: #f7f8fa;
      font-size: 30rpx;
      color: #434a54;
      padding: 0 32rpx;
      border-radius: 55rpx 55rpx 55rpx 55rpx;

      &.active {
        background: #f3ffef;
        color: #577f49;
        border: 2rpx solid #577f49;
        color: #577f49;
      }
    }
  }

  .picker-content {
    margin-top: 24rpx;
    flex: 1;
    overflow: hidden;
  }

  .btn-wrapper {
    display: flex;
    justify-content: center;
    padding: 0 36rpx;
    margin-top: 24rpx;

    .btn {
      width: 100%;
      height: 100rpx;
      line-height: 100rpx;
      font-size: 34rpx;
      color: #ffffff;
      background: #577f49;
      box-shadow: 0rpx 20rpx 48rpx 1rpx rgba(87, 127, 73, 0.2);
      border-radius: 55rpx 55rpx 55rpx 55rpx;
    }
  }
}
</style>
