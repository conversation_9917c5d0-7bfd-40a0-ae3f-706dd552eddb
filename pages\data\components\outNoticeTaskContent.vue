<script setup>
import { ref, watch, watchEffect, computed } from "vue"
import request from "@/utils/request"
import { usePagination } from "@/utils/hooks"
import filterHeader from "./filterHeader.vue"

const props = defineProps({
  stages: {
    type: Array,
    default: () => [],
  },
  currentStageId: {
    type: Number,
    default: 0,
  },
})

const list = ref([])
const loadData = async (options) => {
  let data = {}
  if (filters.value[0].value) {
    data.stage_id = filters.value[0].value
  }
  return new Promise((resolve) => {
    request({
      url: "/mini/statistics/out_notice_task",
      method: "GET",
      data: Object.assign(data, options),
    }).then((res) => {
      list.value.push(...(res.result?.data || []))
      resolve(res)
    })
  })
}
const { page, loadPage, loadMoreStatus, refresh } = usePagination({
  loadData,
  list,
})

const filters = ref([
  {
    key: "stage_id",
    title: "年份",
    options: [],
    value: "",
  },
])

watchEffect(() => {
  if (props.stages?.length) {
    filters.value[0].options = props.stages
  }
  if (props.currentStageId) {
    filters.value[0].value = props.currentStageId
  }
})

const onFiltersChange = (filterValues) => {
  refresh()
}

defineExpose({
  list,
  loadPage,
  refresh,
})
</script>

<template>
  <filterHeader title="回访" :filters="filters" @change="onFiltersChange" />
  <view class="content">
    <view class="list">
      <view class="list-item" v-for="item in list" :key="item.id">
        <view class="title"> {{ item.name }} </view>
        <view class="item-data">
          <view class="data-item">
            <view class="data-item-value">
              {{ item.total_count }}
            </view>
            <view class="data-item-title"> 回访总人数 </view>
          </view>
          <view class="data-item">
            <view class="data-item-value">
              {{ item.vms_count }}
            </view>
            <view class="data-item-title"> 语音通知数 </view>
          </view>
          <view class="data-item">
            <view class="data-item-value">
              {{ item.robot_count }}
            </view>
            <view class="data-item-title"> 机器人对话数 </view>
          </view>
          <view class="data-item">
            <view class="data-item-value"> {{ item.sms_count }} </view>
            <view class="data-item-title"> 短信次数 </view>
          </view>
        </view>
      </view>
    </view>

    <empty v-if="list.length === 0 && loadMoreStatus === 'nomore'" />
  </view>
</template>

<style lang="scss" scoped>
.content {
  padding: 24rpx;
  .list {
    .list-item {
      background: #ffffff;
      box-shadow: 0rpx 6rpx 13rpx 1rpx rgba(0, 0, 0, 0.04);
      border-radius: 16rpx 16rpx 16rpx 16rpx;
      margin-bottom: 20rpx;
      padding: 52rpx 50rpx;

      .title {
        font-size: 32rpx;
        color: #333333;
        font-weight: bold;
        margin-bottom: 50rpx;
      }

      .item-data {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex: 1;

        .data-item {
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;

          &-value {
            font-size: 30rpx;
            color: #21232c;
            font-weight: bold;
            margin-bottom: 18rpx;
          }

          &-title {
            font-weight: 500;
            font-size: 24rpx;
            color: #9ca3b5;
          }
        }
      }
    }
  }
}
</style>
