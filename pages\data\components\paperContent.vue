<script setup>
import { ref, watch, watchEffect, computed } from "vue"
import request from "@/utils/request"
import { usePagination } from "@/utils/hooks"
import filterHeader from "./filterHeader.vue"
import dataTable from "./dataTable.vue"

const props = defineProps({
  stages: {
    type: Array,
    default: () => [],
  },
  departmentTree: {
    type: Array,
    default: () => [],
  },
  myDepartmentCode: {
    type: String,
    default: "",
  },
  currentStageId: {
    type: Number,
    default: 0,
  },
})

const selectedDepartments = ref([])
const list = ref([])
const loadData = async (options) => {
  let data = {}

  if (filters.value[0].value) {
    data.paper_type = filters.value[0].value
  }
  if (filters.value[1].value) {
    data.stage_id = filters.value[1].value
  }
  if (filters.value[2].value === "current") {
    data.department_code = props.myDepartmentCode
  } else {
    data.department_code = selectedDepartments.value.join(",")
  }

  return new Promise((resolve) => {
    request({
      url: "/mini/statistics/paper",
      method: "GET",
      data: Object.assign(data, options),
    }).then((res) => {
      list.value.push(...(res.result?.data || []))
      resolve(res)
    })
  })
}
const { page, loadPage, loadMoreStatus, refresh } = usePagination({
  loadData,
  list,
})

const columns = ref([
  {
    key: "name",
    title: "姓名",
    width: "160rpx",
  },
  {
    key: "department_name",
    title: "单位",
  },
  {
    key: "pass_count",
    title: "合格率",
    width: "178rpx",
  },
])

const filters = ref([
  {
    key: "paper_type",
    title: "类型",
    options: [
      { label: "正式考核", value: "exam" },
      { label: "小结考核", value: "assessment" },
    ],
    value: "exam",
  },
  {
    key: "stage_id",
    title: "年份",
    options: [],
    value: "",
  },
  {
    key: "department_code",
    title: "单位",
    options: [
      { label: "本单位", value: "current" },
      { label: "下级单位", value: "sub" },
    ],
    value: "current",
  },
])

watchEffect(() => {
  if (props.stages?.length) {
    filters.value[1].options = props.stages
  }
  if (props.currentStageId) {
    filters.value[1].value = props.currentStageId
  }
})

const onFiltersChange = (filterValues) => {
  refresh()
}
const onSelectDepartment = (val) => {
  selectedDepartments.value = val
}

const onItemClick = async (item, index) => {
  console.log(item, index)
  if (!item.resultList?.length) {
    const res = await request({
      url: "/mini/statistics/paper_result",
      method: "GET",
      data: {
        page_size: 999,
        stage_id: filters.value[1].value,
        user_id: item.id,
        paper_type: filters.value[0].value,
      },
    })
    item.resultList = res.result?.data || []
  }
  item.expand = !item.expand
}

defineExpose({
  list,
  loadPage,
  refresh,
})
</script>

<template>
  <filterHeader
    title="考核"
    :filters="filters"
    :departmentData="departmentTree"
    @change="onFiltersChange"
    @selectDepartment="onSelectDepartment"
  />
  <view class="content">
    <dataTable
      v-if="list.length"
      type="paper"
      :columns="columns"
      :source="list"
      @itemClick="onItemClick"
    />

    <empty v-if="list.length === 0 && loadMoreStatus === 'nomore'" />
  </view>
</template>

<style lang="scss" scoped>
.content {
  padding: 24rpx;
}
</style>
