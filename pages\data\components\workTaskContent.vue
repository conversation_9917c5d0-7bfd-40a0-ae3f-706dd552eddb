<script setup>
import { ref, watch, watchEffect, computed } from "vue"
import request from "@/utils/request"
import { usePagination } from "@/utils/hooks"
import filterHeader from "./filterHeader.vue"
import dataTable from "./dataTable.vue"

const props = defineProps({
  stages: {
    type: Array,
    default: () => [],
  },
  departmentTree: {
    type: Array,
    default: () => [],
  },
  myDepartmentCode: {
    type: String,
    default: "",
  },
  currentStageId: {
    type: Number,
    default: 0,
  },
})

const selectedDepartments = ref([])
const list = ref([])
const loadData = async (options) => {
  let data = {}
  if (filters.value[0].value) {
    data.stage_id = filters.value[0].value
  }
  if (filters.value[1].value === "current") {
    data.department_code = props.myDepartmentCode
  } else {
    data.department_code = selectedDepartments.value.join(",")
  }

  return new Promise((resolve) => {
    request({
      url: "/mini/statistics/work_task",
      method: "GET",
      data: Object.assign(data, options),
    }).then((res) => {
      list.value.push(...(res.result?.data || []))
      resolve(res)
    })
  })
}
const { page, loadPage, loadMoreStatus, refresh } = usePagination({
  loadData,
  list,
})

const columns = ref([
  {
    key: "name",
    title: "单位",
  },
  {
    key: "report_count",
    title: "已上报/总任务数",
    width: "180rpx",
  },
])

const filters = ref([
  {
    key: "stage_id",
    title: "年份",
    options: [],
    value: "",
  },
  {
    key: "department_code",
    title: "单位",
    options: [
      { label: "本单位", value: "current" },
      { label: "下级单位", value: "sub" },
    ],
    value: "current",
  },
])

watchEffect(() => {
  if (props.stages?.length) {
    filters.value[0].options = props.stages
  }
  if (props.currentStageId) {
    filters.value[0].value = props.currentStageId
  }
})

const onFiltersChange = (filterValues) => {
  refresh()
}
const onSelectDepartment = (val) => {
  selectedDepartments.value = val
}

defineExpose({
  list,
  loadPage,
  refresh,
})
</script>

<template>
  <filterHeader
    title="任务进度"
    :filters="filters"
    :departmentData="departmentTree"
    @change="onFiltersChange"
    @selectDepartment="onSelectDepartment"
  />
  <view class="content">
    <dataTable v-if="list.length" :columns="columns" :source="list" />
    <empty v-if="list.length === 0 && loadMoreStatus === 'nomore'" />
  </view>
</template>

<style lang="scss" scoped>
.content {
  padding: 24rpx;
}
</style>
