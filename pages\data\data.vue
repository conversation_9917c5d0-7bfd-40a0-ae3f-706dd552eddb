<script setup>
import { ref, watch, computed, nextTick } from "vue"
import {
  onLoad,
  onShow,
  onPullDownRefresh,
  onReachBottom,
} from "@dcloudio/uni-app"
import request from "@/utils/request"
import { useUserStore } from "@/store/user"
import { useCommonStore } from "@/store/common"

import courseContent from "./components/courseContent.vue"
import paperContent from "./components/paperContent.vue"
import workTaskContent from "./components/workTaskContent.vue"
import recallTaskContent from "./components/recallTaskContent.vue"
import outNoticeTaskContent from "./components/outNoticeTaskContent.vue"
import { checkPermissionOr } from "@/utils/utils"
import { permissionConst } from "@/utils/permissionConst"

const commonStore = useCommonStore()
const userStore = useUserStore()

const currentStageId = computed(() => commonStore.currentStageId)

const courseContentRef = ref(null)
const paperContentRef = ref(null)
const workTaskContentRef = ref(null)
const recallTaskRef = ref(null)
const outNoticeTaskContentRef = ref(null)

const tabs = computed(() => [
  {
    name: "course",
    title: "学习",
    component: courseContentRef,
    show: 1,
  },
  {
    name: "paper",
    title: "考核",
    component: paperContentRef,
    show: 1,
  },
  {
    name: "workTask",
    title: "任务",
    component: workTaskContentRef,
    show: 1,
  },
  {
    name: "recallTask",
    title: "征召",
    component: recallTaskRef,
    show: checkPermissionOr([
      permissionConst.mini_statistics_recall_task_list.status,
    ]),
  },
  {
    name: "outNoticeTask",
    title: "回访",
    component: outNoticeTaskContentRef,
    show: checkPermissionOr([
      permissionConst.mini_statistics_out_notice_task_list.status,
    ]),
  },
])
const currentIndex = ref(0)
const currentTab = computed(() => tabs.value[currentIndex.value])

const changeTab = (index) => {
  currentIndex.value = index
  if (currentTab.value.component?.value?.list.length === 0) {
    currentTab.value.component.value.loadPage()
  }
}

const stages = ref([])
const loadStages = async () => {
  const res = await request({
    url: "/mini/stage",
    method: "GET",
  })
  let list = res.result?.data || []
  stages.value = list.map((item) => {
    return {
      label: item.name,
      value: item.id,
    }
  })
}

const departmentTree = ref([])
const myDepartmentCode = computed(() => userStore.userInfo.department_code)
const loadDepartmentTree = async () => {
  const res = await request({
    url: "/mini/department_tree",
  })
  departmentTree.value = res.result || []
}

onLoad((options) => {
  uni.hideTabBar()
  let tab = 0
  if (options.tab) {
    let index = tabs.value.findIndex((tab) => tab.name === options.tab)
    tab = index
  }
  loadStages()
  loadDepartmentTree()
  nextTick(() => {
    changeTab(tab)
  })
})
onPullDownRefresh(() => {
  currentTab.value?.component.value?.refresh()
})
onReachBottom(() => {
  currentTab.value?.component.value?.loadPage()
})

onShow(() => {})
</script>

<template>
  <view>
    <view class="header">
      <view class="title">
        <template v-for="(tab, index) in tabs" :key="index">
          <view
            v-if="tab.show"
            :class="currentIndex == index ? 'checked' : ''"
            @click="changeTab(index)"
          >
            <view class="">
              {{ tab.title }}
            </view>
            <view class="green" v-if="currentIndex == index"> </view>
          </view>
        </template>
      </view>
    </view>
    <view class="body">
      <courseContent
        ref="courseContentRef"
        v-show="currentTab.name == 'course'"
        :stages="stages"
        :departmentTree="departmentTree"
        :myDepartmentCode="myDepartmentCode"
        :currentStageId="currentStageId"
      />
      <paperContent
        ref="paperContentRef"
        v-show="currentTab.name == 'paper'"
        :stages="stages"
        :departmentTree="departmentTree"
        :myDepartmentCode="myDepartmentCode"
        :currentStageId="currentStageId"
      />
      <workTaskContent
        ref="workTaskContentRef"
        v-show="currentTab.name == 'workTask'"
        :stages="stages"
        :departmentTree="departmentTree"
        :myDepartmentCode="myDepartmentCode"
        :currentStageId="currentStageId"
      />
      <recallTaskContent
        ref="recallTaskRef"
        v-show="currentTab.name == 'recallTask'"
        :stages="stages"
        :departmentTree="departmentTree"
        :myDepartmentCode="myDepartmentCode"
        :currentStageId="currentStageId"
        v-if="
          checkPermissionOr([
            permissionConst.mini_statistics_recall_task_list.status,
          ])
        "
      />
      <outNoticeTaskContent
        ref="outNoticeTaskContentRef"
        v-show="currentTab.name == 'outNoticeTask'"
        :stages="stages"
        :departmentTree="departmentTree"
        :myDepartmentCode="myDepartmentCode"
        :currentStageId="currentStageId"
        v-if="
          checkPermissionOr([
            permissionConst.mini_statistics_out_notice_task_list.status,
          ])
        "
      />

      <view style="height: 180rpx"></view>
    </view>
    <tabNav index="2" />
  </view>
</template>

<style lang="scss" scoped>
.header {
  background-color: #fff;
}
.body {
  padding: 24rpx 0;
}
.title {
  font-size: 32rpx;
  color: #7e808a;

  text-align: center;
  font-style: normal;
  text-transform: none;
  display: flex;
  text-align: center;
  padding-top: 50rpx;
  justify-content: space-evenly;

  .checked {
    font-weight: bold;
    font-size: 32rpx;
    color: #577f49;
  }

  .green {
    width: 25rpx;
    height: 0rpx;
    background: #ffffff;
    border-bottom: 6rpx solid #577f49;
    border-radius: 6rpx;
    margin: 10rpx auto;
  }
}
</style>
