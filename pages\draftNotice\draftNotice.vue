<script setup>
import { ref, watch, computed } from "vue"
import {
  onLoad,
  onShow,
  onPullDownRefresh,
  onReachBottom,
} from "@dcloudio/uni-app"
import request from "@/utils/request"
import { usePagination } from "@/utils/hooks"
import dayjs from "dayjs"
import { useCommonStore } from "@/store/common"
import { navTo } from "@/utils/utils"

const commonStore = useCommonStore()

const categoryId = computed(() => commonStore.platformNoticeCategoryId)

const goDetail = (item) => {
  console.log(item)
  navTo(`/pages/article/article?articleId=${item.id}`)
}

const list = ref([])
const loadData = (options) => {
  return new Promise((resolve) => {
    request({
      url: "/mini/articles",
      method: "GET",
      data: Object.assign(
        {
          category_id: categoryId.value,
        },
        options
      ),
    }).then((res) => {
      list.value.push(...(res.result?.data || []))
      resolve(res)
    })
  })
}
const category = ref({})
const loadCategory = () => {
  request({
    url: `/mini/category/${categoryId.value}`,
    method: "GET",
  }).then((res) => {
    category.value = res.result
    uni.setNavigationBarTitle({
      title: category.value.name,
    })
  })
}
const { page, loadPage, refresh, loadMoreStatus } = usePagination({
  loadData,
  list,
})

onLoad((options) => {
  console.log(options)
  loadPage()
  loadCategory()
})
onPullDownRefresh(() => {
  refresh()
})
onReachBottom(() => {
  loadPage()
})
</script>

<template>
  <view class="page">
    <view class="body">
      <view
        class="box"
        v-for="item in list"
        :key="item.id"
        @click="goDetail(item)"
      >
        <view class="box-left">
          <view> {{ item.title }} </view>
          <view class="footer">
            <view class="time">
              <image src="/static/image/time.png" mode=""></image>
              <text>{{ dayjs(item.created_at).format("YYYY-MM-DD") }}</text>
            </view>
            <view class="top" v-if="item.top"> 置顶 </view>
          </view>
        </view>
        <view class="box-right">
          <image
            v-if="item.image_id"
            :src="item.image_id_attachments?.full_path"
            mode="aspectFill"
          ></image>
          <image
            v-else
            src="/static/image/notice-cover-default.png"
            mode="aspectFill"
          ></image>
        </view>
      </view>

      <empty
        v-if="list.length === 0 && loadMoreStatus === 'nomore'"
        theme="2"
      />
      <up-loadmore v-if="list.length > 0" :status="loadMoreStatus" />
    </view>
  </view>
</template>

<style lang="scss" scoped>
.page {
  background: #f7f8fa;
  padding-top: 20rpx;
}
.body {
  width: 702rpx;

  border-radius: 20rpx 20rpx 20rpx 20rpx;
  margin: 0rpx auto;
  overflow: hidden;
}
.box {
  width: 702rpx;
  min-height: 210rpx;
  background: #ffffff;
  padding: 24rpx;
  display: flex;
  gap: 30rpx;

  .box-left {
    flex: 1;

    .title {
      font-weight: 500;
      font-size: 32rpx;
      color: #111111;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }

    .footer {
      display: flex;
      align-items: center;
      margin-top: 34rpx;
      width: 98%;
      border-bottom: 2rpx solid #f6f6f6;
      padding-bottom: 22rpx;

      .time {
        display: flex;
        align-items: center;
        font-weight: 400;
        font-size: 26rpx;
        color: #bdc4ce;
        text-align: left;
        font-style: normal;
        text-transform: none;
        margin-right: 45rpx;
        image {
          width: 22rpx;
          height: 22rpx;
          margin-right: 10rpx;
        }
      }

      .top {
        width: 72rpx;
        height: 37rpx;
        line-height: 37rpx;
        font-weight: 400;
        font-size: 22rpx;
        background: #ffcc00;
        color: #ffffff;
        border-radius: 8rpx;
        text-align: center;
        margin-left: 28rpx;
      }

      .address {
        display: flex;

        font-weight: 400;
        font-size: 26rpx;
        color: #bdc4ce;
        text-align: left;
        font-style: normal;
        text-transform: none;

        image {
          width: 20rpx;
          height: 20rpx;
          margin-top: 10rpx;
          margin-right: 10rpx;
        }
      }
    }
  }

  .box-right {
    flex-shrink: 0;

    font-weight: 500;
    font-size: 32rpx;
    color: #111111;
    line-height: 44rpx;
    text-align: left;
    font-style: normal;
    text-transform: none;

    image {
      height: 146rpx;
      width: 146rpx;
      border-radius: 12rpx;
    }
  }
}
</style>
