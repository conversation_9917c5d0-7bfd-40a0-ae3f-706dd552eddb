<script setup>
import { ref, watch, computed } from "vue"
import { onLoad, onShow } from "@dcloudio/uni-app"
import request from "@/utils/request"
import dayjs from "dayjs"

const certificateList = ref([])

const activeCertificate = computed(() => {
  return certificateList.value[0] || null
})

const loadData = async () => {
  const res = await request({ url: "/mini/certificate" })
  certificateList.value = res.result.data
}

const posterData = computed(() => {
  return {
    css: {
      // 根节点若无尺寸，自动获取父级节点
      width: "620rpx",
      height: "400rpx",
      backgroundColor: "#ffffff",
      position: "relative",
    },
    views: [
      {
        name: "bg",
        type: "image",
        src: "https://beijing-zb.oss-cn-beijing.aliyuncs.com/certificate/20240818-123122.png",
        css: {
          width: "620rpx",
          height: "400rpx",
        },
      },
      {
        name: "no",
        type: "text",
        text: `证书编号：${activeCertificate.value?.no}`,
        css: {
          position: "absolute",
          top: "57rpx",
          right: "60rpx",
          fontSize: "10rpx",
          fontWeight: "500",
          color: "#717171",
        },
      },
      {
        name: "expiredDate",
        type: "text",
        text: dayjs(activeCertificate.value?.expired_at).format(
          "YYYY年MM月DD日"
        ),
        css: {
          position: "absolute",
          bottom: "90rpx",
          left: "78rpx",
          fontSize: "10rpx",
          fontWeight: "500",
          color: "#717171",
        },
      },
      {
        name: "name",
        type: "text",
        text: activeCertificate.value?.name,
        css: {
          position: "absolute",
          bottom: "90rpx",
          left: "0",
          width: "620rpx",
          textAlign: "center",
          fontSize: "24rpx",
          fontWeight: "500",
          color: "#717171",
        },
      },
      {
        name: "date",
        type: "text",
        text: dayjs(activeCertificate.value?.created_at).format(
          "YYYY年MM月DD日"
        ),
        css: {
          position: "absolute",
          bottom: "90rpx",
          right: "78rpx",
          fontSize: "10rpx",
          fontWeight: "500",
          color: "#717171",
        },
      },
    ],
  }
})
const imgUrl = ref("")
const onSuccess = (e) => {
  imgUrl.value = e
  console.log("onSuccess", "生成成功", imgUrl)
  // console.log("imgUrl", imgUrl.value)
}
const onFail = (e) => {
  console.log("onFail", "生成失败", e)
}
const saveImage = () => {
  uni.saveImageToPhotosAlbum({
    filePath: imgUrl.value,
    success: () => {
      uni.showToast({
        title: "保存成功",
        icon: "success",
      })
    },
    fail: (err) => {
      console.log("err", err)
      uni.showToast({
        title: "保存失败",
        icon: "none",
      })
    },
  })
}

onLoad((options) => {
  loadData()
})
</script>

<template>
  <view v-if="certificateList.length">
    <navHeader
      title="电子证书"
      bg="/static/image/bg2.png"
      :imgHeight="775"
      :showLeft="true"
      color="#fff"
    ></navHeader>

    <view class="page">
      <image class="badge" src="/static/image/badge.svg" />
      <view class="desc">您已获得征兵工作资格</view>
      <view class="certificate">
        <image class="certificate-img" :src="imgUrl" mode="aspectFill" />
      </view>
      <button class="btn" @click="saveImage">保存到手机相册</button>
    </view>
  </view>

  <view class="empty-page" v-else>
    <navHeader title="电子证书" showLeft color="#000000" />
    <empty theme="2">您尚未获得征兵工作资格电子证书</empty>
  </view>

  <l-painter
    custom-style="position: fixed; left: 200%"
    v-if="activeCertificate"
    path-type="url"
    isCanvasToTempFilePath
    :board="posterData"
    @success="onSuccess"
    @fail="onFail"
  />
</template>

<style lang="scss" scoped>
.empty-page {
  padding-top: 200rpx;
}

.page {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 270rpx;
  position: absolute;
  top: 0;
  width: 100%;
  left: 0;

  .badge {
    width: 64rpx;
    height: 64rpx;
  }

  .desc {
    font-size: 38rpx;
    color: #fff;
    margin-top: 47rpx;
  }

  .certificate {
    margin-top: 75rpx;
    width: 588rpx;
    height: 380rpx;
    background: #ffffff;
    border-radius: 16rpx;
    box-shadow: 0 0 20rpx 0 rgba(0, 0, 0, 0.1);
    overflow: hidden;

    .certificate-img {
      width: 100%;
      height: 100%;
    }
  }

  .btn {
    margin-top: 75rpx;
    width: 584rpx;
    height: 100rpx;
    line-height: 100rpx;
    background: #577f49;
    border-radius: 50rpx;
    font-size: 34rpx;
    color: #ffffff;
  }
}
</style>
