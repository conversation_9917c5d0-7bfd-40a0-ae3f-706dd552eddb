<script setup>
import { ref, computed } from "vue"
import { onLoad, onShow } from "@dcloudio/uni-app"
import request from "@/utils/request"
import { questionTypeMap } from "@/utils/constants"

const questionId = ref(0)
const questionDetail = ref({})

const indexToLetter = (index) => {
  return String.fromCharCode(65 + index)
}
const correctAnswerIndex = computed(() => {
  let correct = []
  for (let i = 0; i < questionDetail.value.answers.length; i++) {
    if (questionDetail.value.answers[i].is_correct) {
      correct.push(i)
    }
  }
  return correct
})

const deleteDialogVisible = ref(false)
const deleteClose = () => {
  deleteDialogVisible.value = false
}
const deleteOpen = () => {
  deleteDialogVisible.value = true
}
const remove = async () => {
  console.log("remove")
  const res = await request({
    url: `/mini/error_question/${questionId.value}`,
    method: "delete",
  })
  if (res?.code === 200) {
    uni.showToast({
      title: "移出成功",
    })
    setTimeout(() => {
      uni.navigateBack()
    }, 1500)
  }
}

const loadQuestionDetail = () => {
  request({
    url: "/mini/question/" + questionId.value,
    method: "GET",
  }).then((res) => {
    if (res.code === 200) {
      questionDetail.value = res.result
    }
  })
}

onLoad((options) => {
  console.log(options)
  questionId.value = options.questionId ? parseInt(options.questionId) : 0
  loadQuestionDetail()
})
</script>

<template>
  <navHeader
    title="错题详情"
    bg="/static/image/bg.png"
    color="#000"
  ></navHeader>
  <view class="error-detail">
    <view class="card" v-if="questionDetail.id">
      <view class="title">
        ({{ questionTypeMap[questionDetail.type].text }})
        {{ questionDetail.title }}
      </view>

      <view
        class="answerList"
        v-if="['single', 'judge'].includes(questionDetail.type)"
      >
        <view
          class="answer"
          :class="{
            correct: item.is_correct,
            incorrect: false,
            checked: item.is_correct,
          }"
          v-for="(item, index) in questionDetail.answers"
          :key="index"
        >
          {{ indexToLetter(index) }}. {{ item.title }}
        </view>
      </view>

      <view class="answerList" v-if="questionDetail.type == 'multiple'">
        <view class="uni-list">
          <checkbox-group>
            <label
              class="uni-list-cell uni-list-cell-pd"
              v-for="(item, index) in questionDetail.answers"
              :key="index"
            >
              <view
                class="answer"
                :class="{
                  correct: item.is_correct,
                  incorrect: false,
                  checked: item.is_correct,
                }"
                style="display: flex"
              >
                <checkbox :value="item.id" :checked="item.is_correct" disabled />
                <view>{{ indexToLetter(index) }}. {{ item.title }}</view>
              </view>
            </label>
          </checkbox-group>
        </view>
      </view>
      <view class="desc">
        <view class="answer">
          正确答案：
          <text style="color: #577f49">
            {{ correctAnswerIndex.map((i) => indexToLetter(i)).join(", ") }}
          </text>
        </view>
        <view class="info">
          <view class="label">要点提示：</view>
          <view class="content">{{ questionDetail.analysis }}</view>
        </view>
      </view>
    </view>

    <button @click="deleteOpen" class="remove">
      <image src="/static/image/delete.svg" mode=""></image>
      移出错题集
    </button>
  </view>

  <up-popup
    mode="center"
    :safeAreaInsetBottom="false"
    :show="deleteDialogVisible"
    :round="10"
    closeable
    @close="deleteClose"
    @open="deleteOpen"
  >
    <view class="dialog">
      <view class="dialog-title"> 确定要从错题集移除此题吗？ </view>

      <view class="dialog-btn-group">
        <button class="dialog-btn red" @click="remove">确定移除</button>
        <button class="dialog-btn" @click="deleteClose">不了</button>
      </view>
    </view>
  </up-popup>
</template>

<style lang="scss" scoped>
.error-detail {
  padding: 55rpx;
  padding-top: 220rpx;
}

.desc {
  margin-top: 74rpx;
  font-weight: bold;
  font-size: 32rpx;
  color: #333333;

  .info {
    margin-top: 26rpx;
    display: flex;
    align-items: flex-start;
    font-size: 32rpx;
    color: #333333;

    .label {
      flex-shrink: 0;
      font-weight: bold;
    }

    .content {
      flex: 1;
      font-weight: 500;
      margin-left: 20rpx;
    }
  }
}

.card {
  width: 640rpx;
  margin: 0 auto;
}

.title {
  width: 640rpx;
  margin: 0 auto;
  font-weight: bold;
  font-size: 32rpx;
  color: #333333;
  margin-bottom: 46rpx;
  text-align: left;
  font-style: normal;
  text-transform: none;
}

.answerList {
  .answer {
    width: 640rpx;
    padding: 20rpx;
    background: #ffffff;
    border-radius: 20rpx 20rpx 20rpx 20rpx;
    margin-bottom: 20rpx;

    checkbox {
      margin-right: 12rpx;
      transform: scale(0.75);
    }

    &.checked {
      background: #fafff8;
      border: 2rpx solid #577f49;

      &.correct {
        background: #fafff8;
        box-shadow: 6rpx 6rpx 0rpx #577f49;
        border-color: #577f49;
      }

      &.incorrect {
        background: #fff8f8;
        box-shadow: 6rpx 6rpx 0rpx #ea2b2b;
        border-color: #ea2b2b;
      }
    }
  }
}

.remove {
  width: 298rpx;
  height: 100rpx;
  background: #ffffff;
  box-shadow: 0rpx 3rpx 60rpx 1rpx rgba(0, 0, 0, 0.16);
  border-radius: 72rpx 72rpx 72rpx 72rpx;
  position: fixed;
  bottom: 180rpx;
  left: 50%;
  transform: translateX(-50%);

  font-size: 26rpx;
  color: #9ca3b5;

  display: flex;
  justify-content: center;
  align-items: center;

  image {
    width: 36rpx;
    height: 36rpx;
    margin-right: 8rpx;
    font-size: 26rpx;
    color: #9ca3b5;
  }
}
</style>
