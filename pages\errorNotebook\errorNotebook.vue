<script setup>
import { ref, computed } from "vue"
import { onLoad, onShow, onReachBottom } from "@dcloudio/uni-app"
import request from "@/utils/request"
import { questionTypeMap } from "@/utils/constants"
import { navTo } from "@/utils/utils"
import { usePagination } from "@/utils/hooks"

const goDetail = (item) => {
  console.log("goDetail")
  const { question } = item
  navTo(`/pages/errorNotebook/errorDetail?questionId=${question.id}`)
}

const startPractice = () => {
  console.log("startPractice")
  navTo("/pages/errorNotebook/practice")
}

const list = ref([])
const loadData = (options) => {
  return new Promise((resolve) => {
    request({
      url: "/mini/error_question",
      method: "GET",
      data: Object.assign({}, options),
    }).then((res) => {
      list.value.push(...(res.result?.data || []))
      resolve(res)
    })
  })
}

const { page, loadPage, refresh, loadMoreStatus } = usePagination({
  loadData,
  list,
})

onLoad((options) => {
  // loadPage()
})

onShow(() => {
  refresh()
})

onReachBottom(() => {
  loadPage()
})
</script>

<template>
  <navHeader title="错题集" bg="/static/image/bg.png" color="#000"></navHeader>

  <view class="page">
    <view class="list">
      <view
        class="list-item"
        @click="goDetail(item)"
        v-for="item in list"
        :key="item.id"
      >
        ({{ questionTypeMap[item.question.type].text }})
        {{ item.question.title }}
      </view>
    </view>

    <empty v-if="list.length === 0 && loadMoreStatus === 'nomore'" />
    <up-loadmore v-if="list.length" :status="loadMoreStatus" />

    <view class="footer">
      <button :disabled="list.length === 0" class="btn" @click="startPractice">
        开始练习
      </button>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.page {
  padding: 24rpx;
  padding-top: 216rpx;
}

.list {
  .list-item {
    padding: 36rpx 60rpx 36rpx 28rpx;
    background-color: #fff;
    margin-bottom: 20rpx;
    border-radius: 20rpx;

    font-weight: 500;
    font-size: 32rpx;
    color: #111111;
  }
}

.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  box-shadow: 0rpx 3rpx 60rpx 1rpx rgba(0, 0, 0, 0.16);
  border-radius: 22rpx 22rpx 0rpx 0rpx;
  background-color: #fff;
  padding: 40rpx 60rpx;
  padding-bottom: calc(40rpx + env(safe-area-inset-bottom) / 2);

  .btn {
    width: 630rpx;
    height: 100rpx;
    line-height: 100rpx;
    background: #577f49;
    color: #ffffff;
    font-weight: 500;
    font-size: 34rpx;
    box-shadow: 0rpx 20rpx 48rpx 1rpx rgba(87, 127, 73, 0.2);
    border-radius: 50rpx 50rpx 50rpx 50rpx;

    &[disabled] {
      opacity: 0.5;
    }
  }
}
</style>
