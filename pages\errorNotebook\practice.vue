<script setup>
import { ref, watch, computed } from "vue"
import { onLoad, onShow } from "@dcloudio/uni-app"
import request from "@/utils/request"
import { navTo } from "@/utils/utils"

// 当前题目索引
const currentIndex = ref(0)
// 当前题目回答结果 正确 correct、错误 incorrect
const currentResult = ref("correct")

const totalCount = ref(0)

// 当前题目状态 未答题 unanswered、已答题 answered、已提交 submitted
const currentStatus = ref("unanswered")
const currentSelections = ref([])
const selectAnswer = (selections) => {
  currentSelections.value = selections
  currentStatus.value = "answered"

  let isCorrect = true
  if (currentQuestion.value.error_question_user_answer.correct) {
    const correct = currentQuestion.value.error_question_user_answer.correct
      .split(",")
      .map((id) => parseInt(id))
    if (correct.length !== selections.length) {
      isCorrect = false
    } else {
      for (let i = 0; i < selections.length; i++) {
        if (correct.indexOf(selections[i]) === -1) {
          isCorrect = false
          break
        }
      }
    }
  } else {
    isCorrect = false
  }
  currentResult.value = isCorrect ? "correct" : "incorrect"
}

const submitDialogVisible = ref(false)
function openSubmitDialog() {
  submitDialogVisible.value = true
}
const popStyle = {
  borderRadius: "20rpx 20rpx 20rpx 20rpx",
  width: "630rpx",
  height: "410rpx",
  background: "#FFFFFF",
  border: "2rpx solid #FFFFFF",
  padding: "0 41rpx",
  boxSizing: "border-box",
  margin: "0 auto",
}
function closeSubmitDialog() {
  submitDialogVisible.value = false
}

const roundId = ref(0)
const currentQuestion = ref(null)

const prevQuestion = async () => {
  const res = await request({
    url: `/mini/error_question/${roundId.value}/pre_question`,
    method: "POST",
    data: {
      cur_error_question_user_answer_id:
        currentQuestion.value.error_question_user_answer.id,
    },
  })

  currentQuestion.value = res.result
  currentIndex.value--

  currentSelections.value = res.result.answer
}
const nextQuestion = async () => {
  if (currentIndex.value === totalCount.value) {
    submitDialogVisible.value = true
    return
  }

  let data = {}
  if (currentIndex.value > 0) {
    data.cur_error_question_user_answer_id =
      currentQuestion.value.error_question_user_answer.id
  }
  const res = await request({
    url: `/mini/error_question/${roundId.value}/next_question`,
    method: "POST",
    data,
  })
  currentQuestion.value = res.result
  currentIndex.value++

  currentStatus.value = "unanswered"
  currentSelections.value = []
}

const answerCurrentQuestion = async () => {
  return new Promise((resolve) => {
    request({
      url: `/mini/error_question/${roundId.value}/answer`,
      method: "POST",
      data: {
        error_question_user_answer_id:
          currentQuestion.value.error_question_user_answer.id,
        selections: currentSelections.value,
      },
    }).then((res) => {
      if (res.code === 200) {
        currentStatus.value = "submitted"
      }
      resolve()
    })
  })
}

const percentage = computed(() => {
  return (currentIndex.value / totalCount.value) * 100
})

const startExam = async () => {
  const res = await request({
    url: `/mini/error_question_start`,
    method: "POST",
  })
  const { id, correct_count, total_count } = res.result
  totalCount.value = total_count
  roundId.value = id
  currentIndex.value = 0
  nextQuestion()
}

const allDown = computed(() => {
  return (
    currentIndex.value == totalCount.value &&
    currentStatus.value === "submitted"
  )
})

const submitPaper = async () => {
  await request({
    url: `/mini/error_question/${roundId.value}/finish`,
    method: "POST",
  })

  uni.navigateBack()
}

const handleFinish = () => {
  submitPaper()
}

const removeCurrent = async () => {
  const res = await request({
    url: `/mini/error_question/${currentQuestion.value.question.id}`,
    method: "delete",
    data: {
      round_id: roundId.value,
    },
  })
  if (res?.code === 200) {
    deleteClose()
    uni.showToast({
      title: "已将本题移出错题本",
    })

    if (currentIndex.value < totalCount.value) {
      nextQuestion()
    } else {
      submitPaper()
    }
  }
}

const pageOptions = ref({})
onLoad((options) => {
  console.log("onLoad")
  pageOptions.value = options
  if (currentIndex.value === 0) {
    startExam()
  }
})

// 移除确认
const deleteDialogVisible = ref(false)
const deleteClose = () => {
  deleteDialogVisible.value = false
}
const deleteOpen = () => {
  deleteDialogVisible.value = true
}
</script>

<template>
  <view class="page">
    <navHeader
      bg="/static/image/bg.png"
      title="错题练习"
      :showLeft="true"
      color="#000"
    ></navHeader>

    <view class="header">
      <view class="progress">
        <view>
          <text class="count"> {{ currentIndex }}</text>
          /
          <text>{{ totalCount }}</text>
        </view>
        <view class="line">
          <up-line-progress
            :showText="false"
            :percentage="percentage"
            activeColor="#577F49"
          ></up-line-progress>
        </view>
      </view>
    </view>
    <view class="body">
      <question
        v-if="currentQuestion"
        :qaId="currentQuestion.error_question_user_answer.id"
        :status="currentStatus"
        feedbackType="instant"
        :type="currentQuestion.error_question_user_answer.question_type"
        :title="currentQuestion.error_question_user_answer.question_title"
        :options="currentQuestion.error_question_user_answer.options"
        :correctSerials="currentQuestion.error_question_user_answer.correct"
        :analysis="currentQuestion.error_question_user_answer.question_analysis"
        @select="selectAnswer"
      />
    </view>

    <examFooter
      :status="currentStatus"
      feedbackType="instant"
      :allDown="allDown"
      :result="currentResult"
      paperType="practice"
      @prev="prevQuestion"
      @next="nextQuestion"
      @answer="answerCurrentQuestion"
      @finish="handleFinish"
    ></examFooter>

    <view class="remove" @click="deleteOpen">
      <image src="/static/image/remove.svg" class="icon"></image>
      移出
    </view>
  </view>

  <up-popup
    :customStyle="popStyle"
    round="20"
    closeable
    :show="submitDialogVisible"
    mode="center"
    :safeAreaInsetBottom="false"
    @close="closeSubmitDialog"
    @open="openSubmitDialog"
  >
    <view class="popbox">
      <view class=""> </view>
      <view class="center"> 确定提交本次答卷么 </view>
      <view class="submit" @click="submitPaper"> 提交答卷 </view>
    </view>
  </up-popup>

  <up-popup
    mode="center"
    :safeAreaInsetBottom="false"
    :show="deleteDialogVisible"
    :round="10"
    closeable
    @close="deleteClose"
    @open="deleteOpen"
  >
    <view class="dialog">
      <view class="dialog-title"> 确定要从错题集移除此题吗？ </view>

      <view class="dialog-btn-group">
        <button class="dialog-btn red" @click="removeCurrent">确定移除</button>
        <button class="dialog-btn" @click="deleteClose">不了</button>
      </view>
    </view>
  </up-popup>
</template>

<style lang="scss" scoped>
.page {
  position: absolute;
  top: 0;
  left: 0;
  height: 100vh;
  z-index: 1;
  background: #f7f8fa;

  .header {
    margin-top: 200rpx;

    .progress {
      font-size: 22rpx;
      color: #bdc4ce;
      text-align: center;
      display: flex;
      justify-content: center;
      .count {
        font-family: Arial, Arial;
        font-weight: 400;
        font-size: 30rpx;
        color: #577f49;
        text-align: center;
      }
      .line {
        margin-left: 27rpx;
        margin-top: 6rpx;
        width: 534rpx;
      }
    }
  }

  .body {
    padding: 24rpx;
    width: 750rpx;
  }
}

.popbox {
  position: relative;

  display: flex;
  flex-direction: column;
  justify-content: space-around;
  height: 100%;

  image {
    width: 282rpx;
    height: 282rpx;
    margin: 0 auto;
    margin-top: -200rpx;
  }

  .center {
    font-weight: bold;
    font-size: 38rpx;
    color: #06121e;
    line-height: 40rpx;
    text-align: center;
  }

  .submit {
    width: 524rpx;
    height: 100rpx;
    line-height: 100rpx;
    background: #577f49;
    border-radius: 55rpx 55rpx 55rpx 55rpx;
    text-align: center;
    color: #fff;
  }
}

.remove {
  width: 137rpx;
  height: 60rpx;
  line-height: 60rpx;
  background: #ffffff;
  border-radius: 0rpx 37rpx 37rpx 0rpx;

  position: fixed;
  left: 0;
  bottom: 320rpx;
  z-index: 2;

  display: flex;
  align-items: center;

  font-size: 26rpx;
  color: #9ca3b5;

  padding-left: 12rpx;
  padding-right: 26rpx;

  .icon {
    width: 36rpx;
    height: 36rpx;
    margin-right: 8rpx;
  }
}
</style>
