<script setup>
import { ref, watch, computed } from "vue"
import { onLoad, onShow } from "@dcloudio/uni-app"
import request from "@/utils/request"

const roundId = ref(0)

const currentTabIndex = ref(0)

const questions = ref([])

const list = computed(() => {
  if (currentTabIndex.value === 0) {
    return questions.value
  } else {
    return questions.value.filter((item) => item.is_correct === 0)
  }
})

const loadData = () => {
  console.log("loadData")
  request({
    url: `/mini/paper/${roundId.value}/questions`,
    method: "get",
    data: {
      page_size: 999,
    },
  }).then((res) => {
    console.log(res)
    questions.value = res.result?.data || []
  })
}

onLoad((options) => {
  console.log("onLoad")
  roundId.value = options.roundId
  loadData()
})
</script>

<template>
  <view class="page">
    <navHeader
      bg="/static/image/bg.png"
      title="答案解析"
      :showLeft="true"
      color="#000"
    ></navHeader>

    <view class="header">
      <view class="type">
        <view
          class="tab-item"
          :class="{ checked: currentTabIndex === 0 }"
          @click="currentTabIndex = 0"
        >
          全部题目
        </view>
        <view
          class="tab-item"
          :class="{ checked: currentTabIndex === 1 }"
          @click="currentTabIndex = 1"
        >
          只看错题
        </view>
      </view>
    </view>
    <view class="body">
      <analysisAnswer
        v-for="(question, index) in list"
        :key="question.id"
        :number="index + 1"
        :questionDetail="question"
      />
    </view>
  </view>
</template>

<style lang="scss" scoped>
.page {
  position: absolute;
  top: 0;
  left: 0;
  height: 100vh;
  z-index: 1;
  background: #f7f8fa;

  .header {
    margin-top: 200rpx;

    .type {
      display: flex;
      justify-content: space-between;
      text-align: center;
      width: 610rpx;
      height: 80rpx;
      background: #ffffff;
      border-radius: 55rpx 55rpx 55rpx 55rpx;
      margin: 0 auto;
      font-weight: 500;
      font-size: 28rpx;
      color: #9ca3b5;
      line-height: 80rpx;
      padding: 6rpx;

      .tab-item {
        line-height: 68rpx;
        width: 299rpx;
        height: 68rpx;
        text-align: center;

        &.checked {
          background: #577f49;
          box-shadow: 0rpx 3rpx 6rpx 1rpx rgba(0, 0, 0, 0.02);
          border-radius: 55rpx 55rpx 55rpx 55rpx;
          color: #ffffff;
        }
      }
    }
  }

  .body {
    padding: 24rpx;
    // background: #F7F8FA;
    width: 750rpx;
  }
}
</style>
