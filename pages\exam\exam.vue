<script setup>
import { ref, watch, computed } from "vue"
import { onLoad, onUnload, onShow } from "@dcloudio/uni-app"
import request from "@/utils/request"
import { navTo } from "@/utils/utils"

const paperId = ref(0)

// 当前题目索引
const currentIndex = ref(0)
// 当前题目回答结果 正确 correct、错误 incorrect
const currentResult = ref("correct")

// 当前题目状态 未答题 unanswered、已答题 answered、已提交 submitted
const currentStatus = ref("unanswered")
const currentSelections = ref([])
const selectAnswer = (selections) => {
  currentSelections.value = selections
  currentStatus.value = "answered"

  let isCorrect = true
  if (currentQuestion.value.paper_user_question_answer.correct) {
    const correct = currentQuestion.value.paper_user_question_answer.correct
      .split(",")
      .map((id) => parseInt(id))
    if (correct.length !== selections.length) {
      isCorrect = false
    } else {
      for (let i = 0; i < selections.length; i++) {
        if (correct.indexOf(selections[i]) === -1) {
          isCorrect = false
          break
        }
      }
    }
  } else {
    isCorrect = false
  }
  currentResult.value = isCorrect ? "correct" : "incorrect"
}

const submitText = ref("确定提交本次答卷么？")
const submitCloseable = ref(true)

const countdown = ref(0)
const countdownText = computed(() => {
  const minute = Math.floor(countdown.value / 60)
  const second = countdown.value % 60
  return `${minute}:${second < 10 ? "0" + second : second}`
})
let timer = null
const startCountdown = () => {
  timer = setInterval(() => {
    countdown.value--
    if (countdown.value <= 0) {
      clearInterval(timer)

      submitText.value = "时间到，请提交答卷"
      submitCloseable.value = false

      handleFinish()
    }
  }, 1000)
}

const submitDialogVisible = ref(false)
function openSubmitDialog() {
  submitDialogVisible.value = true
}
const popStyle = {
  borderRadius: "20rpx 20rpx 20rpx 20rpx",
  width: "630rpx",
  height: "410rpx",
  background: "#FFFFFF",
  border: "2rpx solid #FFFFFF",
  padding: "0 41rpx",
  boxSizing: "border-box",
  margin: "0 auto",
}
function closeSubmitDialog() {
  submitDialogVisible.value = false
}

const roundId = ref(0)
const paperData = ref(null)
const currentQuestion = ref(null)

const prevQuestion = async () => {
  const res = await request({
    url: `/mini/paper/${roundId.value}/pre_question`,
    method: "POST",
    data: {
      cur_paper_user_question_answer_id:
        currentQuestion.value.paper_user_question_answer.id,
    },
  })

  currentQuestion.value = res.result
  currentIndex.value--

  if (res.result?.paper_user_question_answer?.selections) {
    let selections = res.result.paper_user_question_answer.selections
    console.log("selections==================", selections)

    currentSelections.value = selections
      .split(",")
      .map((number) => parseInt(number))
    currentStatus.value = "answered"
  }
}
const nextQuestion = async () => {
  if (paperData.value?.feedback_type === "final" && currentIndex.value > 0) {
    await answerCurrentQuestion()
  }

  let data = {}
  if (currentIndex.value > 0) {
    data.cur_paper_user_question_answer_id =
      currentQuestion.value.paper_user_question_answer.id
  }
  const res = await request({
    url: `/mini/paper/${roundId.value}/next_question`,
    method: "POST",
    data,
  })
  currentQuestion.value = res.result
  currentIndex.value++

  currentStatus.value = "unanswered"
  currentSelections.value = []
}

const answerCurrentQuestion = async () => {
  return new Promise((resolve) => {
    request({
      url: `/mini/paper/${roundId.value}/answer`,
      method: "POST",
      data: {
        paper_user_question_answer_id:
          currentQuestion.value.paper_user_question_answer.id,
        selections: currentSelections.value,
      },
    }).then((res) => {
      if (res.code === 200) {
        currentStatus.value = "submitted"
      }
      resolve()
    })
  })
}

const percentage = computed(() => {
  return (currentIndex.value / paperData.value?.question_total_count) * 100
})

const startExam = async () => {
  const data = {}
  if (pageOptions.value.subChapterId) {
    data.sub_chapter_id = pageOptions.value.subChapterId
  }
  if (pageOptions.value.faceUserIdKey) {
    data.user_id_key = pageOptions.value.faceUserIdKey
  }
  const res = await request({
    url: `/mini/paper/${paperId.value}/start`,
    method: "POST",
    data,
  })
  const { id, correct_count, total_count } = res.result
  roundId.value = id
  currentIndex.value = 0
  nextQuestion()

  if (paperData.value.duration) {
    startCountdown()
  }
}

const allDown = computed(() => {
  return (
    currentIndex.value == paperData.value?.question_total_count &&
    currentStatus.value ===
      (paperData.value?.feedback_type === "final" ? "answered" : "submitted")
  )
})

const submitPaper = async () => {
  if (paperData.value?.feedback_type === "final") {
    await answerCurrentQuestion()
  }

  const res = await request({
    url: `/mini/paper/${roundId.value}/finish`,
    method: "POST",
  })

  switch (paperData.value?.type) {
    case "practice":
      navTo(
        "/pages/result/exercise",
        {
          paperId: paperId.value,
          feedbackType: paperData.value?.feedback_type,
          correctCount: res.result?.correct_count || 0,
          totalCount: res.result?.total_count || 0,
          subChapterId: pageOptions.value.subChapterId || 0,
          roundId: roundId.value,
        },
        "redirectTo"
      )
      break
    case "assessment":
      navTo(
        "/pages/result/exam",
        {
          paperId: paperId.value,
          type: "assessment",
          correctCount: res.result?.correct_count || 0,
          totalCount: res.result?.total_count || 0,
          isPass: res.result?.is_pass || false,
          score: res.result?.score || 0,
        },
        "redirectTo"
      )
      break
    case "exam":
      navTo(
        "/pages/result/exam",
        {
          paperId: paperId.value,
          roundId: roundId.value,
          type: "exam",
          correctCount: res.result?.correct_count || 0,
          totalCount: res.result?.total_count || 0,
          isPass: res.result?.is_pass || false,
          isSimulate: !!res.result?.is_simulate,
          score: res.result?.score || 0,
        },
        "redirectTo"
      )
      break
  }
}

const loadPaperData = async () => {
  const res = await request({
    url: `/mini/paper/${paperId.value}`,
    method: "GET",
  })
  paperData.value = res.result
  countdown.value = res.result?.duration ? res.result.duration * 60 : 0

  if (currentIndex.value === 0) {
    startExam()
  }
}

const handleFinish = () => {
  if (paperData.value?.feedback_type === "final") {
    openSubmitDialog()
  } else {
    submitPaper()
  }
}

const pageOptions = ref({})
onLoad((options) => {
  console.log("/pages/exam/exam.vue onLoad", options)
  pageOptions.value = options
  paperId.value = options.paperId ? parseInt(options.paperId) : 0
  loadPaperData()
})

onUnload(() => {
  clearInterval(timer)
})
</script>

<template>
  <view class="page">
    <navHeader
      bg="/static/image/bg.png"
      :title="paperData?.title"
      :showLeft="true"
      color="#000"
    ></navHeader>

    <view class="header">
      <view class="progress">
        <view class="progress-num">
          <text class="count"> {{ currentIndex }}</text>
          /
          <text>{{ paperData?.question_total_count || 0 }}</text>
        </view>
        <view class="line">
          <up-line-progress
            :showText="false"
            :percentage="percentage"
            activeColor="#577F49"
          ></up-line-progress>
        </view>
        <view class="countdown" v-if="paperData?.duration">
          <text class="count">{{ countdownText }}</text>
        </view>
      </view>
    </view>
    <view class="body">
      <question
        v-if="currentQuestion"
        :qaId="currentQuestion.paper_user_question_answer.id"
        :status="currentStatus"
        :feedbackType="paperData?.feedback_type"
        :type="currentQuestion.paper_user_question_answer.question_type"
        :title="currentQuestion.paper_user_question_answer.question_title"
        :options="currentQuestion.paper_user_question_answer.options"
        :correctSerials="currentQuestion.paper_user_question_answer.correct"
        :analysis="currentQuestion.paper_user_question_answer.question_analysis"
        :selections="currentSelections"
        @select="selectAnswer"
      />
    </view>

    <examFooter
      :status="currentStatus"
      :feedbackType="paperData?.feedback_type"
      :currentNo="currentIndex"
      :allDown="allDown"
      :result="currentResult"
      :paperType="paperData?.type"
      @prev="prevQuestion"
      @next="nextQuestion"
      @answer="answerCurrentQuestion"
      @finish="handleFinish"
    ></examFooter>
  </view>

  <up-popup
    :customStyle="popStyle"
    round="20"
    :closeable="submitCloseable"
    :show="submitDialogVisible"
    mode="center"
    :safeAreaInsetBottom="false"
    @close="closeSubmitDialog"
    @open="openSubmitDialog"
  >
    <view class="popbox">
      <view class=""> </view>
      <view class="center"> {{ submitText }} </view>
      <view class="submit" @click="submitPaper"> 提交答卷 </view>
    </view>
  </up-popup>
</template>

<style lang="scss" scoped>
.page {
  position: absolute;
  top: 0;
  left: 0;
  height: 100vh;
  z-index: 1;
  background: #f7f8fa;

  .header {
    margin-top: 200rpx;

    .progress {
      font-size: 22rpx;
      color: #bdc4ce;
      text-align: center;
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 0 55rpx;

      .progress-num {
        margin-right: 27rpx;
        .count {
          font-family: Arial, Arial;
          font-weight: 400;
          font-size: 30rpx;
          color: #577f49;
          text-align: center;
        }
      }

      .line {
        margin-top: 6rpx;
        flex: 1;
      }

      .countdown {
        margin-left: 25rpx;
        width: 122rpx;
        height: 48rpx;
        line-height: 48rpx;
        text-align: center;
        background: #577f49;
        border-radius: 24rpx 24rpx 24rpx 24rpx;
        font-size: 30rpx;
        color: #ffffff;
      }
    }
  }

  .body {
    padding: 24rpx;
    width: 750rpx;
  }
}

.popbox {
  position: relative;

  display: flex;
  flex-direction: column;
  justify-content: space-around;
  height: 100%;

  image {
    width: 282rpx;
    height: 282rpx;
    margin: 0 auto;
    margin-top: -200rpx;
  }

  .center {
    font-weight: bold;
    font-size: 38rpx;
    color: #06121e;
    line-height: 40rpx;
    text-align: center;
  }

  .submit {
    width: 524rpx;
    height: 100rpx;
    line-height: 100rpx;
    background: #577f49;
    border-radius: 55rpx 55rpx 55rpx 55rpx;
    text-align: center;
    color: #fff;
  }
}
</style>
