<script setup>
import { ref } from "vue"
import { onLoad, onShow } from "@dcloudio/uni-app"
import request from "@/utils/request"
import dayjs from "dayjs"
import { navTo } from "@/utils/utils"
import { useUserStore } from "@/store/user"

const userStore = useUserStore()

const examList = ref([])

const loadEnabledExam = async () => {
  const res = await request({
    url: `/mini/exam_paper`,
    method: "GET",
  })
  if (res.code === 200) {
    examList.value = res.result
  }
}

const faced = ref(false)
const showFaceAuth = ref(false)
const closeFaceAuth = () => {
  showFaceAuth.value = false
}
const openFaceAuth = () => {
  showFaceAuth.value = true
}
const faceUserIdKey = ref("")
const face = async () => {
  wx.startFacialRecognitionVerify({
    userIdKey: faceUserIdKey.value,
    success: (res) => {
      if (res.errCode === 0) {
        request({
          url: "/mini/get_user_face_result",
          data: {
            verify_result: res.verifyResult,
          },
        }).then((res) => {
          console.log(res)
          if (res.code === 200) {
            faced.value = true
            closeFaceAuth()

            if (optItem.value) {
              goExam(optItem.value)
            }
          }
        })
      }
    },
    fail: (err) => {
      console.log(err)
    },
  })
}

const checkFaceAuth = () => {
  if (!faced.value && userStore.haveToFaceAuth) {
    request({
      url: "/mini/get_user_id_key?scene=exam",
    }).then((res) => {
      if (res.code === 200) {
        faceUserIdKey.value = res.result.user_id_key
        openFaceAuth()
      }
    })
    return false
  }
  return true
}

const optItem = ref(null)
const goExam = (item) => {
  optItem.value = item

  // 没学完
  if (!item.course_all_studied) {
    uni.showModal({
      title: "提示",
      content: "您还未完成课程学习，请先完成课程学习",
      showCancel: false,
      success: (res) => {
        console.log(res)
        if (res.confirm) {
          navTo(`/pages/course/course`, {}, "redirectTo")
        }
      },
    })
    return
  } else if(item.join_times >= item.times) {
    uni.showModal({
      title: "提示",
      content: `本次考试次数已用完，每人只能参加${item.times}次考试`,
      showCancel: false,
    })
    return
  } else if (!checkFaceAuth()) {
    return
  }

  let query = {
    type: "exam",
    paperId: item.id,
    isSimulate: !!item.is_simulate,
  }
  if (faceUserIdKey.value) {
    query.faceUserIdKey = faceUserIdKey.value
  }
  navTo(`/pages/exam/examTips`, query)
}

onShow(() => {
  loadEnabledExam()
})
</script>

<template>
  <view>
    <navHeader
      bg="/static/image/exam-bg.png"
      :imgHeight="374"
      :showLeft="true"
      color="#fff"
    ></navHeader>
    <view class="body">
      <view class="exam-list" v-if="examList.length > 0">
        <view class="exam-item" v-for="item in examList" :key="item.id">
          <view class="tag" :class="item.is_simulate ? 'simulation' : ''">
            {{ item.is_simulate ? "模拟考核" : "正式考核" }}
          </view>
          <view class="exam-info">
            <view class="title">{{ item.title }}</view>
            <view class="time"
              >考核时间：{{ dayjs(item.start_time).format("YYYY.MM.DD") }} -
              {{ dayjs(item.end_time).format("YYYY.MM.DD") }}
            </view>
          </view>
          <button class="btn" @click="goExam(item)">开始</button>
        </view>
      </view>
      <empty v-else> 当前无考核</empty>
    </view>
  </view>

  <!-- 人脸弹窗 -->
  <up-popup
    :show="showFaceAuth"
    mode="center"
    :safeAreaInsetBottom="false"
    :round="20"
    closeable
    @close="closeFaceAuth"
    @open="openFaceAuth"
  >
    <view class="face-pop">
      <view class="img">
        <image src="/static/image/face-verify.svg" mode=""></image>
      </view>
      <view class="title"> 请完成人脸识别认证</view>
      <view class="text">
        为避免影响使用本系统，请尽快 完成人脸识别认证。
      </view>
      <view class="">
        <button class="btn" @click="face">立即认证</button>
      </view>
    </view>
  </up-popup>
</template>

<style lang="scss" scoped>
.body {
  width: 750rpx;
  background: rgba(247, 248, 250, 0.65);
  border-radius: 44rpx 44rpx 0rpx 0rpx;
  border: 1rpx solid #fff;
  backdrop-filter: blur(10rpx);
  min-height: calc(100vh - 430rpx - 43rpx);
  margin-top: 331rpx;
  padding: 35rpx 24rpx;
}

.exam-list {
  display: flex;
  flex-direction: column;

  .exam-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: rgba(255, 255, 255, 0.94);
    box-shadow: 0rpx 6rpx 13rpx 1rpx rgba(0, 0, 0, 0.04);
    border-radius: 20rpx;
    border: 2rpx solid #ffffff;
    padding: 60rpx 43rpx;
    margin-bottom: 20rpx;
    position: relative;

    .tag {
      position: absolute;
      top: 0;
      left: 0;

      width: 140rpx;
      height: 47rpx;
      line-height: 47rpx;
      background: #f3ffef;
      color: #577f49;
      border-radius: 20rpx 0rpx 20rpx 0rpx;

      font-size: 24rpx;
      padding: 0 20rpx;

      &.simulation {
        background: #f5f7f9;
        color: #8c9198;
      }
    }

    .exam-info {
      flex: 1;
      padding-top: 30rpx;

      .title {
        font-weight: bold;
        font-size: 32rpx;
        color: #333;
      }

      .time {
        flex-shrink: 0;
        font-size: 24rpx;
        color: #999999;
        margin-top: 18rpx;
      }
    }

    .btn {
      width: 120rpx;
      height: 60rpx;
      line-height: 60rpx;
      background: #577f49;
      color: #fff;
      font-weight: 500;
      font-size: 26rpx;
      border-radius: 31rpx;
    }
  }
}
</style>
