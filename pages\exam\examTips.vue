<script setup>
import { ref, watch, computed } from "vue"
import { onLoad, onShow } from "@dcloudio/uni-app"
import request from "@/utils/request"
import dayjs from "dayjs"
import { navTo } from "@/utils/utils"

const paperId = ref(0)
const subChapterId = ref(0)
const isSimulate = ref(false)

const type = ref("exam")

const paperData = ref({})

const loadData = async () => {
  const res = await request({
    url: `/mini/paper/${paperId.value}`,
    method: "GET",
  })
  if (res.code === 200) {
    res.result.description = res.result.description.replace(/\n/g, "<br>")
    paperData.value = res.result
  }
}

const faceUserIdKey = ref("")
const goExam = async () => {
  const navParams = {
    paperId: paperId.value,
    type: type.value,
    isSimulate: isSimulate.value,
  }

  if (subChapterId.value) {
    navParams.subChapterId = subChapterId.value
  }
  if (faceUserIdKey.value) {
    navParams.faceUserIdKey = faceUserIdKey.value
  }
  navTo(`/pages/exam/exam`, navParams)
}

onLoad(async (options) => {
  console.log("/pages/exam/examTips.vue onLoad", options)
  paperId.value = options.paperId
  if (options.type) {
    type.value = options.type
  }
  if (options.subChapterId) {
    subChapterId.value = options.subChapterId
  }
  if (options.faceUserIdKey) {
    faceUserIdKey.value = options.faceUserIdKey
  }
  isSimulate.value = options.isSimulate === "true"
  loadData()
})
</script>

<template>
  <view>
    <navHeader
      bg="/static/image/exam-tips-bg.png"
      :imgHeight="true"
      :showLeft="true"
      color="#fff"
    ></navHeader>
    <view class="body">
      <view class="title-box">
        <view class="title">{{ paperData.title }}</view>
        <view class="time" v-if="paperData.start_time && paperData.end_time">
          考核时间：{{ dayjs(paperData.start_time).format("YYYY.MM.DD") }} -
          {{ dayjs(paperData.end_time).format("YYYY.MM.DD") }}
        </view>
      </view>

      <view class="box">
        <view class="title"> - 温馨提示 - </view>

        <view class="content">
          <rich-text :nodes="paperData.description"></rich-text>
        </view>

        <button class="btn" @click="goExam">开始考核</button>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.body {
  width: 750rpx;
  padding: 0 24rpx;
  padding-top: 210rpx;

  .title-box {
    text-align: center;
    margin-bottom: 50rpx;

    .title {
      font-size: 52rpx;
      color: #ffffff;
      font-weight: 500;
    }

    .time {
      font-size: 28rpx;
      color: #ffffff;
      margin-top: 28rpx;
    }
  }

  .box {
    background: #ffffff;
    box-shadow: 2rpx 11rpx 48rpx 1rpx rgba(0, 0, 0, 0.08);
    border-radius: 20rpx 20rpx 20rpx 20rpx;
    padding: 55rpx 36rpx 75rpx;

    .title {
      font-weight: 500;
      font-size: 28rpx;
      color: #9ca3b5;
      text-align: center;
      margin-bottom: 33rpx;
    }

    .content {
      line-height: 54rpx;
      font-size: 28rpx;
      color: #333333;
      font-weight: 500;
    }

    .btn {
      height: 100rpx;
      background: #577f49;
      color: #ffffff;
      box-shadow: 0rpx 20rpx 48rpx 1rpx rgba(87, 127, 73, 0.2);
      border-radius: 50rpx 50rpx 50rpx 50rpx;
      margin: 60rpx auto;
      line-height: 100rpx;
      text-align: center;
    }
  }
}
</style>
