<script setup>
import { ref, watch, computed } from "vue"
import {
  onLoad,
  onShow,
  onPullDownRefresh,
  onReachBottom,
} from "@dcloudio/uni-app"
import request from "@/utils/request"
import { navTo } from "@/utils/utils"
import { usePagination } from "@/utils/hooks"

const goDetail = (item) => {
  navTo(`/pages/policy/detail?articleId=${item.id}`)
}

const keyword = ref("")
const list = ref([])
const loadSections = (options) => {
  let data = {}
  if (keyword.value) {
    data.keyword = keyword.value
  }
  return new Promise((resolve) => {
    request({
      url: "/mini/collect",
      method: "GET",
      data: Object.assign(data, options),
    }).then((res) => {
      list.value.push(...(res.result?.data || []))
      resolve(res)
    })
  })
}

const { page, loadPage, loadMoreStatus, refresh } = usePagination({
  loadData: loadSections,
  list,
})

onLoad((options) => {
  console.log(options)
  // loadPage()
})
onPullDownRefresh(() => {
  refresh()
})
onReachBottom(() => {
  loadPage()
})
onShow(() => {
  refresh()
})
</script>

<template>
  <view class="body">
    <view class="search">
      <up-search
        :show-action="false"
        placeholder="关键词搜索"
        placeholderColor="#bdc4ce"
        searchIconColor="#bdc4ce"
        v-model="keyword"
        bgColor="#ffffff"
        :searchIconSize="28"
        :inputStyle="{ height: '80rpx' }"
        @search="refresh"
        @clear="refresh"
      ></up-search>
    </view>

    <view class="list">
      <view
        class=""
        @click="goDetail(item.collect)"
        v-for="(item, index) in list"
        :key="index"
      >
        <policyItem :item="item.collect" />
      </view>

      <empty v-if="list.length === 0 && loadMoreStatus === 'nomore'" />
      <up-loadmore v-if="list.length > 0" :status="loadMoreStatus" />
    </view>
  </view>
</template>

<style lang="scss" scoped>
.body {
  padding: 35rpx 24rpx;
  .search {
    margin-bottom: 24rpx;
  }

  .list {
  }
}
</style>
