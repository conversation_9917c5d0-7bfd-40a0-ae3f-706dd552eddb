<script setup>
import { ref, watch, computed } from "vue"
import { onLoad, onShow, onShareAppMessage } from "@dcloudio/uni-app"
import { useCommonStore } from "@/store/common"
import { useUserStore } from "@/store/user"
import { fixedMenuTemplateMap } from "@/utils/constants"
import request from "@/utils/request"
import dayjs from "dayjs"
import { navTo } from "@/utils/utils"

const commonStore = useCommonStore()
const userStore = useUserStore()

const isLogin = computed(() => userStore.isLogin)
const userInfo = computed(() => userStore.userInfo)

const timeRange = computed(() => {
  const date = new Date()
  const hour = date.getHours()
  if (hour < 6) {
    return "凌晨好"
  } else if (hour < 9) {
    return "早上好"
  } else if (hour < 12) {
    return "上午好"
  } else if (hour < 14) {
    return "中午好"
  } else if (hour < 17) {
    return "下午好"
  } else if (hour < 19) {
    return "傍晚好"
  } else if (hour < 22) {
    return "晚上好"
  } else {
    return "夜里好"
  }
})

const dateText = computed(() => {
  const date = new Date()
  return `${date.getMonth() + 1}月${date.getDate()}日 周${
    ["日", "一", "二", "三", "四", "五", "六"][date.getDay()]
  }`
})

const remindCount = computed(() => userStore.remindCount)

const menus = ref([])
const notice = ref([])
const platform_notice = ref([])

const goNav = (item) => {
  console.log(item)
  switch (item.template) {
    case "article-category":
      const notice_category_id = commonStore.config.notice_category_id
      const platform_notice_category_id =
        commonStore.config.platform_notice_category_id
      const policy_category_id = commonStore.config.policy_category_id
      if (item.url == notice_category_id) {
        navTo(`/pages/notify/notify`)
      } else if (item.url == platform_notice_category_id) {
        navTo(`/pages/draftNotice/draftNotice`)
      } else if (item.url == policy_category_id) {
        navTo(`/pages/policy/policy`)
      } else {
        navTo(`/pages/article/category`, { categoryId: item.url })
      }
      break
    case "fixed":
      navTo(fixedMenuTemplateMap[item.url].url)
      break
    case "page":
      navTo(`/pages/article/page`, { pageId: item.url })
      break
    case "custom":
      navTo(item.url)
      break
    default:
      break
  }
}

const goLogin = () => {
  if (isLogin.value) {
    return
  }
  navTo("/pages/login/login")
}

const showFaceAuth = ref(false)
const closeFaceAuth = () => {
  showFaceAuth.value = false
}
const openFaceAuth = () => {
  showFaceAuth.value = true
}
const face = async () => {
  const res = await request({
    url: "/mini/get_user_id_key?scene=account_auth",
  })
  wx.startFacialRecognitionVerify({
    userIdKey: res.result.user_id_key,
    success: (res) => {
      if (res.errCode === 0) {
        request({
          url: "/mini/get_user_face_result",
          data: {
            verify_result: res.verifyResult,
          },
        }).then((res) => {
          console.log(res)
          if (res.code === 200) {
            userStore.getUserInfo()
            closeFaceAuth()
          }
        })
      }
    },
    fail: (err) => {
      console.log(err)
    },
  })
}

const showCodeActive = ref(false)
const closeCodeActive = () => {
  showCodeActive.value = false
}
const openCodeActive = () => {
  showCodeActive.value = true
}
const code = ref("")
const activeCode = async () => {
  if (!code.value) {
    uni.showToast({
      title: "请输入注册码",
      icon: "none",
    })
    return
  }
  const res = await request({
    url: "/mini/account_activate",
    method: "POST",
    data: {
      code: code.value,
    },
  })
  if (res.code === 200) {
    uni.showToast({
      title: "激活成功",
      icon: "success",
    })
    setTimeout(() => {
      uni.reLaunch({
        url: "/pages/index/index",
      })
    }, 1000)
  }
}

const loadData = async () => {
  const res = await request({
    url: "/mini/index",
  })
  console.log(res)
  if (res.code === 200) {
    menus.value = res.result.menus
    platform_notice.value = res.result.platform_notice?.list
  }
  if (isLogin.value) {
    const noticeRes = await request({
      url: "/mini/inform?scope=can_handle",
      data: {
        page: 1,
        page_size: 5,
      },
    })
    if (noticeRes.code === 200) {
      notice.value = noticeRes.result?.data || []
    }
  }
}

const goNotifyDetail = (item) => {
  navTo(`/pages/noticeDetail/noticeDetail?id=${item.id}`)
}

onLoad((options) => {
  console.log(options)
  uni.hideTabBar()
})

onShow(() => {
  loadData()
  if (!isLogin.value) {
    return
  }
  userStore.getUserInfo().then((res) => {
    if (res.code === 200) {
      if (!userInfo.value?.activated) {
        openCodeActive()
      } else if (!userInfo.value?.faced && userStore.haveToFaceAuth) {
        openFaceAuth()
      }
    } else {
      if (res.code === 10002) {
        openCodeActive()
      } else if (res.code === 10001) {
        openFaceAuth()
      }
    }
  })
})

onShareAppMessage(() => {
  return {
    title: "北京征兵办公云平台",
    imageUrl: commonStore.forwardDefaultImage,
  }
})
</script>

<template>
  <navHeader
    title="首页"
    bg="/static/image/register.png"
    :showLeft="false"
  ></navHeader>
  <view class="page">
    <view class="header">
      <view class="user-info" @click="goLogin">
        <up-avatar
          default-url="/static/image/avatar.png"
          :src="userInfo.avatar_id_attachments?.full_path"
          class="avatar"
        ></up-avatar>
        <view class="info">
          <view class="">
            <view class="name"> {{ timeRange }}，{{ userInfo.name }} </view>
            <text> {{ dateText }} </text>
          </view>
        </view>
      </view>

      <view class="status-info">
        <view class="count" @click="navTo('/pages/aibot/aibot')">
          <image src="/static/image/msg.svg" mode="heightFix"></image>
        </view>
        <view class="count" @click="navTo('/pages/message/message')">
          <image src="/static/image/bell.svg" mode="heightFix"></image>
          <view class="badge" v-if="remindCount > 0">
            {{ remindCount }}
          </view>
        </view>
      </view>
    </view>
    <view class="body">
      <view class="notify">
        <image src="/static/image/notice-left.png" mode="heightFix"></image>

        <swiper
          autoplay
          circular
          vertical
          interval="3000"
          duration="500"
          style="width: 100%; height: 80rpx"
        >
          <swiper-item
            v-for="item in platform_notice"
            :key="item.id"
            @click="navTo('/pages/draftNotice/draftNotice')"
          >
            <view class="content">
              <view class="date">
                {{ dayjs(item.created_at).format("MM/DD") }}
              </view>
              <text>{{ item.title }}</text>
            </view>
          </swiper-item>
        </swiper>
      </view>

      <view class="nav">
        <view
          class="nav-item"
          v-for="(item, index) in menus"
          :key="index"
          @click="goNav(item)"
        >
          <image :src="item.icon_id_attachments.full_path" mode=""></image>
          <view class="title"> {{ item.name }} </view>
        </view>

        <navigator url="/packageTasks/pages/index/index">
          <view class="nav-item">
            <image src="/static/image/renwu.png" mode="heightFix"></image>
            <view class="title"> 任务 </view>
          </view>
        </navigator>
      </view>

      <view class="section">
        <view class="title">
          <image
            src="/static/image/announce-title.png"
            mode="heightFix"
          ></image>
          <view class="more" @click.stop="navTo('/pages/notify/notify')">
            更多
            <!-- <up-icon name="arrow-right" color="#9CA3B5" size="12" class="icon"></up-icon> -->
            <image src="/static/image/right.png" mode=""></image>
          </view>
        </view>

        <view class="notifyList">
          <notifyItem
            v-for="(item, index) in notice"
            :key="index"
            :item="item"
            @openDetail="goNotifyDetail(item)"
          />
        </view>
      </view>

      <view style="height: 180rpx"></view>
    </view>
  </view>

  <tabNav index="0" />

  <!-- 人脸弹窗 -->
  <up-popup
    :show="showFaceAuth"
    mode="center"
    :safeAreaInsetBottom="false"
    :round="20"
    closeable
    @close="closeFaceAuth"
    @open="openFaceAuth"
  >
    <view class="face-pop">
      <view class="img">
        <image src="/static/image/face-verify.svg" mode=""></image>
      </view>
      <view class="title"> 请完成人脸识别认证 </view>
      <view class="text">
        为避免影响使用本系统，请尽快 完成人脸识别认证。
      </view>
      <view class="">
        <button class="btn" @click="face">立即认证</button>
      </view>
    </view>
  </up-popup>
  <!-- 注册码激活弹窗 -->
  <up-popup
    :show="showCodeActive"
    mode="center"
    :safeAreaInsetBottom="false"
    :round="20"
    closeable
    @close="closeCodeActive"
    @open="openCodeActive"
  >
    <view class="code-active-pop">
      <view class="img">
        <image src="/static/image/warn.svg" mode=""></image>
      </view>
      <view class="title"> 您的注册码已过期 </view>
      <input
        class="input"
        v-model="code"
        placeholder="输入新的注册码"
        placeholder-class="input-placeholder"
      />
      <view class="">
        <button class="btn" @click="activeCode">提交</button>
      </view>
    </view>
  </up-popup>
</template>

<style>
page {
  background: #ffffff;
}
</style>

<style lang="scss" scoped>
.page {
  padding-top: 222rpx;
}

.code-active-pop {
  width: 630rpx;
  // height: 553rpx;
  background: #ffffff;
  border-radius: 20rpx 20rpx 20rpx 20rpx;
  border: 2rpx solid #ffffff;
  padding: 56rpx 53rpx 49rpx;

  .img {
    width: 64rpx;
    height: 64rpx;
    margin: 7rpx auto 30rpx;

    image {
      width: 64rpx;
      height: 64rpx;
    }
  }

  .title {
    font-weight: bold;
    font-size: 38rpx;
    color: #262626;
    text-align: center;
    margin-bottom: 48rpx;
  }

  .input {
    margin: 0 auto;
    padding: 0 40rpx;
    text-align: left;
    width: 524rpx;
    height: 100rpx;
    background: #f7f8fa;
    border-radius: 20rpx 20rpx 20rpx 20rpx;
    border: 1rpx solid #e1e1e1;
    color: #333333;
    line-height: 100rpx;
  }

  .btn {
    margin-top: 56rpx;
    width: 524rpx;
    height: 100rpx;
    background: #577f49;
    border-radius: 55rpx 55rpx 55rpx 55rpx;
    color: #fff;
    line-height: 100rpx;
    text-align: center;
    font-size: 32rpx;
  }
}

.header {
  margin-bottom: 39rpx;
  padding: 0 28rpx;
  display: flex;
  justify-content: space-between;

  .user-info {
    display: flex;
    align-items: center;

    .avatar {
      width: 70rpx;
      height: 70rpx;
    }

    .info {
      margin-left: 22rpx;
      text-align: left;
      font-style: normal;
      text-transform: none;

      .name {
        font-weight: 800;
        font-size: 34rpx;
        color: #21232c;
      }

      text {
        font-size: 26rpx;
        color: #9ca3b5;

        text-align: left;
        font-style: normal;
        text-transform: none;
      }
    }
  }

  .status-info {
    display: flex;
    align-items: center;
    gap: 30rpx;
    padding-right: 37rpx;

    .count {
      width: 64rpx;
      height: 64rpx;
      background: rgba(255, 255, 255, 0.8);
      border-radius: 32rpx 32rpx 32rpx 32rpx;
      border: 1rpx solid rgba(151, 151, 151, 0.2);
      text-align: center;
      position: relative;
      display: flex;
      justify-content: center;
      align-items: center;

      image {
        height: 30rpx;
      }

      .badge {
        position: absolute;
        top: -10rpx;
        right: -10rpx;
        min-width: 26rpx;
        padding: 0 6rpx;
        height: 26rpx;
        border-radius: 13rpx;
        line-height: 26rpx;
        font-size: 20rpx;
        text-align: center;
        background: #ff3b3b;
        color: #ffffff;
        border: 2rpx solid #ffffff;
      }
    }
  }
}

.body {
  width: 750rpx;
  background: linear-gradient(180deg, #f7f8fa 0%, rgba(247, 248, 250, 0) 100%);
  backdrop-filter: blur(30rpx);
  border-radius: 30rpx 30rpx 0 0;
  padding: 31rpx 24rpx;

  .title {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;

    image {
      height: 60rpx;
    }

    .more {
      display: flex;
      justify-content: space-around;
      align-items: center;
      width: 120rpx;
      height: 54rpx;
      background: #ffffff;
      box-shadow: 2rpx 4rpx 16rpx 1rpx rgba(0, 0, 0, 0.04);
      border-radius: 50rpx 50rpx 50rpx 50rpx;
      border: 1rpx solid #f7f8fa;
      text-align: center;
      line-height: 50rpx;

      font-weight: 400;
      font-size: 24rpx;
      color: #9ca3b5;
      padding-left: 24rpx;
      padding-right: 12rpx;

      image {
        width: 8rpx;
        height: 16rpx;
      }
    }
  }

  .notify {
    padding: 0 20rpx;
    height: 80rpx;
    line-height: 80rpx;
    width: 702rpx;
    background: #ffffff;
    box-shadow: 2rpx 11rpx 48rpx 1rpx rgba(0, 0, 0, 0.02);
    border-radius: 20rpx 20rpx 20rpx 20rpx;

    font-weight: 500;
    font-size: 28rpx;
    color: #333333;

    display: flex;
    align-items: center;

    image {
      flex-shrink: 0;
      height: 36rpx;
    }

    swiper {
      flex: 1;
      height: 80rpx;

      .content {
        display: flex;
        align-items: center;
        overflow: hidden;
        text-overflow: ellipsis;

        .date {
          font-weight: 400;
          font-size: 28rpx;
          color: #9ca3b5;
          margin: 0 20rpx;
          text-align: left;
          font-style: normal;
          text-transform: none;
        }

        text {
          flex: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
  }

  .nav {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 40rpx 0;
    margin-top: 50rpx;
    margin-bottom: 70rpx;

    .nav-item {
      image {
        display: block;
        width: 92rpx;
        height: 92rpx;
        margin: 0 auto;
      }

      .title {
        margin-top: 16rpx;

        font-weight: bold;
        font-size: 28rpx;
        color: #333333;
        text-align: center;
        display: flex;
        justify-content: center;
      }
    }
  }

  .notifyList {
    margin-top: 20rpx;
  }
}
</style>
