<script setup>
import { ref, watch, computed } from "vue"
import { onLoad, onShow, onShareAppMessage } from "@dcloudio/uni-app"
import { useCommonStore } from "@/store/common.js"
import { useUserStore } from "@/store/user.js"
import request from "@/utils/request.js"
import bgImg from "@/static/image/login.png"
import { inputStyle } from "@/utils/constants.js"
import { navTo } from "@/utils/utils.js"

inputStyle.padding = "0 20rpx"

const store = useCommonStore()
const userStore = useUserStore()

const navHeight = ref(0)

const params = ref({
  phone: "",
  password: "",
  captcha_code: "",
})
onLoad((options) => {
  navHeight.value = store.navHeight
})

const formPage = ref("")
const backBeforePage = () => {
  if (formPage.value) {
    navTo(formPage.value, {}, "redirectTo")
    return
  }
  const pages = getCurrentPages()
  if (pages.length > 1) {
    uni.navigateBack()
  } else {
    uni.reLaunch({
      url: "/pages/index/index",
    })
  }
}

const captcha = ref({})
const getCaptcha = () => {
  params.value.captcha_code = ""
  request({
    url: "/mini/get_captcha",
    method: "post",
  }).then((res) => {
    if (res.code === 200) {
      captcha.value = res.result
    }
  })
}

const login = () => {
  userStore
    .login({
      ...params.value,
      captcha_key: captcha.value.key,
    })
    .then((res) => {
      if (res.code === 200) {
        uni.showToast({
          title: "登录成功",
          icon: "success",
        })

        setTimeout(() => {
          backBeforePage()
        }, 1000)
      }
    })
    .catch(() => {
      getCaptcha()
    })
}

onLoad((options) => {
  formPage.value = options.formPage || ""
  getCaptcha()
})

onShareAppMessage(() => {
  return {
    title: "北京征兵办公云平台",
    imageUrl: commonStore.forwardDefaultImage,
  }
})
</script>

<template>
  <view class="page">
    <navHeader
      :img-height="531"
      title="北京征兵办公云平台"
      color="#fff"
      :bg="bgImg"
      :showLeft="false"
    ></navHeader>
    <view class="body">
      <image src="/static/image/avatar.png" mode="" class="avatar"></image>
      <view class="title">您好，请登录</view>
      <up-form class="form">
        <up-form-item>
          <up-input
            v-model="params.phone"
            :customStyle="inputStyle"
            placeholder="手机号码"
            placeholderClass="placeholder"
          >
            <template #prefix>
              <image
                src="/static/image/login-account.svg"
                class="prefix-icon"
              ></image>
            </template>
          </up-input>
        </up-form-item>

        <up-form-item>
          <up-input
            v-model="params.password"
            :customStyle="inputStyle"
            placeholder="密码"
            placeholderClass="placeholder"
            type="password"
          >
            <template #prefix>
              <image
                src="/static/image/login-password.svg"
                class="prefix-icon"
              ></image>
            </template>
          </up-input>
        </up-form-item>

        <up-form-item>
          <up-input
            :customStyle="inputStyle"
            v-model="params.captcha_code"
            disabledColor="#ffffff"
            placeholder="图形验证码"
            placeholderClass="placeholder"
          >
            <template #prefix>
              <image
                src="/static/image/login-captcha.svg"
                class="prefix-icon"
              ></image>
            </template>
          </up-input>
          <image :src="captcha.img" @click="getCaptcha" class="captcha"></image>
        </up-form-item>

        <view
          class="forget_password"
          @click="navTo('/pages/resetPassword/resetPassword')"
        >
          忘记密码?
        </view>
      </up-form>
      <button class="login_btn" @click="login">登录</button>
      <view class="bottom_text">
        还没有账号?
        <text @click="navTo('/pages/register/register')">快速注册</text>
      </view>
    </view>
  </view>

  <privacyPopup />
</template>

<style lang="scss">
.u-form {
  padding: 0 36rpx;
}

.u-form-item__body__right__content {
  position: relative;
}
</style>

<style lang="scss" scoped>
.page {
  background: #f7f8fa;
  .body {
    position: absolute;
    top: 425rpx;
    width: 702rpx;
    background: #ffffff;
    box-shadow: 2rpx 11rpx 48rpx 1rpx rgba(0, 0, 0, 0.08);
    border-radius: 20rpx 20rpx 20rpx 20rpx;
    border: 1rpx solid #ffffff;
    z-index: 1;
    left: 24rpx;
    box-sizing: border-box;
    padding-bottom: 74rpx;

    .login_btn {
      width: 630rpx;
      height: 100rpx;
      background: #577f49;
      box-shadow: 0rpx 20rpx 48rpx 1rpx rgba(87, 127, 73, 0.2);
      border-radius: 50rpx 50rpx 50rpx 50rpx;
      color: #fff;
      line-height: 100rpx;
      margin: 45rpx auto;
    }
    .bottom_text {
      margin-top: 30rpx;
      text-align: center;
      color: #b7b9be;
      text {
        color: #577f49;
      }
    }
    .avatar {
      width: 140rpx;
      height: 140rpx;
      transform: translate(351rpx - 70rpx, -70rpx);
    }
    .title {
      font-weight: 800;
      font-size: 48rpx;
      color: #21232c;
      text-align: center;
      font-style: normal;
      text-transform: none;
      transform: translateY(-20rpx);
    }

    .forget_password {
      font-weight: 400;
      font-size: 32rpx;
      color: #b7b9be;
      padding-right: 36rpx;
      text-align: right;
      font-style: normal;
      text-transform: none;
    }
  }
}
.login-image {
  width: 750rpx;
  height: 531rpx;
  position: absolute;
  z-index: -1;
  top: 0;
}
.u-navbar__content__title {
  color: #fff !important;
}

.captcha {
  width: 233rpx;
  height: 62rpx;
  background: #577f49;
  border-radius: 15rpx 15rpx 15rpx 15rpx;
  position: absolute;
  right: 34rpx;
  top: 50%;
  transform: translateY(-50%);
  z-index: 2;
}

.prefix-icon {
  width: 44rpx;
  height: 44rpx;
  margin-right: 16rpx;
  vertical-align: middle;
}
</style>
