<script setup>
import { ref, onMounted, toRef } from "vue"
import ReadMore from "@/pages/matching/components/readMore.vue"

const props = defineProps({
  options: {
    type: Array,
    default: () => [],
  },
  modelValue: {
    type: Object,
    default: {},
  },
})

const emit = defineEmits(["update:modelValue", "change"])

const modelValue = toRef(props, "modelValue")

const confirm = () => {
  emit("update:modelValue", modelValue.value)
  emit("change", modelValue.value)
}

const onSelect = (key, value) => {
  if (!modelValue.value[key]) {
    modelValue.value[key] = []
  }
  if (modelValue.value[key].includes(value)) {
    modelValue.value[key] = modelValue.value[key].filter(
      (item) => item !== value
    )
  } else {
    modelValue.value[key].push(value)
  }
}
</script>

<template>
  <view class="filter">
    <view class="filter-title">筛选</view>
    <view class="filter-content">
      <view class="filter-item" v-for="(item, index) in options" :key="index">
        <view class="filter-item-title">
          {{ item.title }}
          <up-icon
            v-if="!item.expand"
            @click="item.expand = !item.expand"
            name="arrow-down"
          ></up-icon>
          <up-icon
            v-if="item.expand"
            @click="item.expand = !item.expand"
            name="arrow-up"
          ></up-icon>
        </view>
        <read-more :expand="item.expand">
          <view class="filter-item-content">
            <view
              class="filter-item-content-item"
              :class="{ active: props.modelValue[item.name]?.includes(option) }"
              v-for="(option, index1) in item.options"
              :key="index1"
              @click="onSelect(item.name, option)"
            >
              {{ option }}
            </view>
          </view>
        </read-more>
      </view>
    </view>
    <view class="filter-footer">
      <button class="reset" @click="emit('update:modelValue', {})">重置</button>
      <button class="confirm" @click="confirm">确定</button>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.filter {
  display: flex;
  flex-direction: column;
  max-height: 80vh;

  .filter-title {
    text-align: center;
    font-size: 34rpx;
    color: #303445;
    font-weight: bold;
    padding: 38rpx 0;
  }

  .filter-content {
    padding: 0 38rpx;
    flex: 1;
    overflow-y: auto;

    .filter-item {
      margin-bottom: 48rpx;

      .filter-item-title {
        font-weight: 500;
        font-size: 28rpx;
        color: #21232c;
        margin-bottom: 12rpx;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      .filter-item-content {
        display: flex;
        flex-wrap: wrap;
        gap: 20rpx;

        .filter-item-content-item {
          min-width: 189rpx;
          height: 72rpx;
          line-height: 72rpx;
          text-align: center;
          padding: 0 32rpx;
          border-radius: 55rpx 55rpx 55rpx 55rpx;
          background: #f7f8fa;
          color: #434a54;

          &.active {
            border: 2rpx solid #4c9f6b;
            background: #f3ffef;
            color: #577f49;
          }
        }
      }
    }
  }

  .filter-footer {
    width: 100%;
    display: flex;
    justify-content: space-between;
    padding: 40rpx 38rpx;
    gap: 34rpx;

    button {
      flex: 1;
      height: 100rpx;
      line-height: 100rpx;
      font-size: 34rpx;
      color: #fff;
      border-radius: 50rpx 50rpx 50rpx 50rpx;
    }
    .reset {
      background: rgba(87, 127, 73, 0.1);
      color: #577f49;
    }
    .confirm {
      background: #577f49;
      color: #ffffff;
    }
  }
}
</style>
