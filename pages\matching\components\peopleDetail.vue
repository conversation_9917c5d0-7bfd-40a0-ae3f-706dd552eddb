<script setup>
import { ref, watch, computed } from "vue"
import { idCardDesensitization } from "@/utils/utils"

const props = defineProps({
  peopleDetail: {
    type: Object,
    default: () => ({}),
  },
})

const emit = defineEmits(["call"])

const fieldList = [
  { label: "身份证号", key: "id_card" },
  { label: "民族", key: "nation" },
  { label: "政治面貌", key: "political" },
  { label: "所在区", key: "district_name" },
  { label: "户籍地", key: "from" },
  { label: "联系方式", key: "phone" },
  // { label: "文化程度", key: "study_situation" },
  { label: "所学专业", key: "major" },
  { label: "学业情况", key: "study_situation" },
  {
    label: "学习类型",
    key: "study_form",
    render: (value) => {
      if (value === 1) return "全日制"
      if (value === 2) return "非全日制"
      return "未知"
    },
  },
  {
    label: "是否理工类",
    key: "is_engineering",
    render: (value) => {
      if (value === 1) return "是"
      if (value === 2) return "否"
      return "未知"
    },
  },
  {
    label: "高级以上职业技能等级证书(职业资格证书)名称及编号",
    key: "certificate",
  },
]

const call = () => {
  emit("call", props.peopleDetail.phone)
}
</script>

<template>
  <view class="people-detail">
    <view class="name-call">
      <view class="name">
        {{ peopleDetail.name }}
        <image
          v-if="peopleDetail.sex"
          class="gender"
          :src="
            peopleDetail.sex == '男'
              ? '/static/image/boy.png'
              : '/static/image/girl.png'
          "
        ></image>
      </view>
      <image
        src="/static/image/call.svg"
        mode=""
        class="call"
        @click="call"
      ></image>
    </view>
    <view class="info">
      <view class="item" v-for="field in fieldList" :key="field.key">
        <view class="label">
          {{ field.label }}
        </view>
        <view class="value">
          {{
            field.render
              ? field.render(peopleDetail[field.key])
              : peopleDetail[field.key]
          }}
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.people-detail {
  display: flex;
  flex-direction: column;
  max-height: 80vh;
  padding: 45rpx 45rpx 0;

  .name-call {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .name {
      display: flex;
      align-items: center;
      font-size: 52rpx;
      font-weight: bold;
      color: #21232c;

      .gender {
        width: 49rpx;
        height: 49rpx;
        margin-left: 22rpx;
      }
    }

    .call {
      width: 56rpx;
      height: 56rpx;
      border: 1rpx solid #c3cbd6;
      padding: 8rpx;
      border-radius: 50%;
    }
  }

  .progress-wrapper {
    margin-top: 30rpx;
  }

  .info {
    margin-top: 84rpx;
    flex: 1;
    overflow-y: auto;

    .item {
      padding: 20rpx 0;
      border-bottom: 2rpx solid #f6f6f6;

      .label {
        font-size: 28rpx;
        color: #9ca3b5;
        margin-bottom: 7rpx;
      }

      .value {
        font-size: 32rpx;
        color: #111111;
      }
    }
  }
}
</style>
