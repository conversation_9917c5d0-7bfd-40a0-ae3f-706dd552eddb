<script setup>
const props = defineProps({
  // 折叠
  expand: {
    type: Boolean,
    default: false,
  },
  // 最大高度
  maxHeight: {
    type: Number,
    default: 125,
  },
})
</script>

<template>
  <view>
    <view
      :class="[!expand ? 'fold' : 'unfold']"
      :style="{ maxHeight: expand ? 'none' : `${maxHeight}px` }"
    >
      <slot></slot>
    </view>
  </view>
</template>

<style scoped lang="scss">
.fold {
  overflow-y: hidden;
}

.unfold {
  height: auto;
}
</style>
