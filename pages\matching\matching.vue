<script setup>
import { ref, watch, computed } from "vue"
import {
  onLoad,
  onShow,
  onPullDownRefresh,
  onReachBottom,
} from "@dcloudio/uni-app"
import request from "@/utils/request"
import dayjs from "dayjs"
import { usePagination } from "@/utils/hooks"
import peopleDetail from "./components/peopleDetail.vue"
import matchingFilter from "./components/matchingFilter.vue"

const isSelectAll = ref(false)
const toggleSelectAll = () => {
  if (!isSelectAll.value) {
    checkedList.value = list.value.map((item) => item.id)
    isSelectAll.value = true
  } else {
    checkedList.value = []
    isSelectAll.value = false
  }
}

const checkedList = ref([])
const toggleSelectItem = (id) => {
  if (checkedList.value.includes(id)) {
    checkedList.value.splice(checkedList.value.indexOf(id), 1)
    if (isSelectAll.value) {
      isSelectAll.value = false
    }
  } else {
    checkedList.value.push(id)

    if (checkedList.value.length === list.value.length) {
      isSelectAll.value = true
    }
  }
}

const isShowItemCheckbox = computed(() => {
  return checkedList.value.length || filterActive.value || isSelectAll.value
})

const keyword = ref("")
const list = ref([])
const loadData = (options) => {
  let _filterValues = { ...filterValues.value }
  for (const key in _filterValues) {
    if (_filterValues[key].length === 0) {
      delete _filterValues[key]
    } else {
      _filterValues[key] = _filterValues[key].join(",")
    }
  }

  return new Promise((resolve) => {
    request({
      url: "/mini/match/teen_soldier",
      method: "GET",
      data: Object.assign(
        {
          keyword: keyword.value,
          ..._filterValues,
        },
        options
      ),
    }).then((res) => {
      list.value.push(...(res.result?.data || []))
      resolve(res)
    })
  })
}

const callPhone = (phone, relationId) => {
  uni.makePhoneCall({
    phoneNumber: phone,
  })
  request({
    url: `/mini/call`,
    method: "POST",
    data: {
      call_type: "teen_soldier",
      relation_id: relationId,
    },
  })
}

const { page, loadPage, loadMoreStatus, refresh } = usePagination({
  loadData,
  list,
})

const optPeople = ref(null)
const detailVisible = ref(false)
const openDetail = async (item) => {
  const res = await request({
    url: `/mini/match/teen_soldier/${item.id}`,
  })
  optPeople.value = res.result
  detailVisible.value = true
}
const closeDetail = () => {
  detailVisible.value = false
}

const filterVisible = ref(false)
const openFilter = () => {
  filterVisible.value = true
}
const closeFilter = () => {
  filterVisible.value = false
}
const filterOptions = ref({})
const filterValues = ref({})
const filterActive = computed(() => {
  return Object.keys(filterValues.value).length > 0
})
const loadFilterOptions = () => {
  request({
    url: "/mini/match/teen_soldier_search_option",
    method: "GET",
    data: {
      attr: "education",
    },
  }).then((res) => {
    console.log(res)
    filterOptions.value = res.result
  })
}
const onFilterChange = (values) => {
  console.log(values)
  refresh()
  closeFilter()
}

const exportData = () => {
  let data = {
    type: "export_teen_soldier_excel",
    name: "导出应征青年",
    params: {},
  }

  if (!isSelectAll.value && checkedList.value.length) {
    data.params = {
      ids: checkedList.value,
    }
  }

  request({
    url: "/mini/task",
    method: "POST",
    data,
  }).then((res) => {
    if (res.code === 200) {
      openExportTip()

      isSelectAll.value = false
      checkedList.value = []
    }
  })
}

const exportTipVisible = ref(false)
const popStyle = {
  borderRadius: "20rpx 20rpx 20rpx 20rpx",
  width: "630rpx",
  height: "410rpx",
  background: "#FFFFFF",
  border: "2rpx solid #FFFFFF",
  padding: "0 41rpx",
  boxSizing: "border-box",
  margin: "0 auto",
}
const openExportTip = () => {
  exportTipVisible.value = true
}
const closeExportTip = () => {
  exportTipVisible.value = false
}

onLoad((options) => {
  console.log(options)
  loadPage()
  loadFilterOptions()
})
onPullDownRefresh(() => {
  refresh()
})
onReachBottom(() => {
  loadPage()
})
</script>

<template>
  <view>
    <navHeader
      bg="/static/image/matching-bg.png"
      :imgHeight="374"
      :showLeft="true"
      color="#fff"
    ></navHeader>
    <view class="body">
      <view class="filter-bar">
        <view class="search">
          <up-search
            :show-action="false"
            placeholder="关键词搜索"
            placeholderColor="#bdc4ce"
            searchIconColor="#bdc4ce"
            v-model="keyword"
            bgColor="#ffffff"
            :searchIconSize="28"
            :inputStyle="{ height: '80rpx' }"
            @search="refresh"
            @clear="refresh"
          ></up-search>
        </view>
        <view
          class="filter-btn"
          :class="{ active: filterActive }"
          @click="openFilter"
        >
          <text>筛选</text>
          <image
            :src="
              filterActive
                ? '/static/image/filter-active.png'
                : '/static/image/filter.png'
            "
            mode=""
            class="filter-icon"
          ></image>
        </view>
      </view>

      <view class="list">
        <view
          class="box"
          v-for="item in list"
          :key="item.id"
          @click="openDetail(item)"
        >
          <view
            class="checkbox"
            v-show="isShowItemCheckbox"
            @click.stop="toggleSelectItem(item.id)"
          >
            <image
              v-if="checkedList.includes(item.id) || isSelectAll"
              src="/static/image/checkbox-checked.svg"
              mode=""
            ></image>
            <image
              v-else
              src="/static/image/checkbox-unchecked.svg"
              mode=""
            ></image>
          </view>

          <view class="box-left">
            <view class="title">
              {{ item.name }}
              <image
                v-if="item.sex"
                :src="
                  item.sex === '女'
                    ? '/static/image/girl.png'
                    : '/static/image/boy.png'
                "
                mode=""
                style="width: 30rpx; height: 30rpx"
              ></image>
            </view>
            <view class="date">
              <text v-if="item.age">{{ item.age }}岁 · </text>
              <text>{{ item.id_card }}</text>
            </view>
          </view>
          <view class="box-right">
            <image
              src="/static/image/call.svg"
              mode=""
              style="width: 56rpx; height: 56rpx"
              @click.stop="callPhone(item.phone, item.id)"
            ></image>
          </view>
        </view>

        <up-loadmore v-if="list.length" :status="loadMoreStatus" />
      </view>
    </view>

    <view class="footer">
      <view class="check-all" @click="toggleSelectAll">
        <image
          :src="
            isSelectAll
              ? '/static/image/checkbox-checked.svg'
              : '/static/image/checkbox-unchecked.svg'
          "
          mode=""
          style="width: 40rpx; height: 40rpx"
        ></image>
        <text>全选</text>
      </view>

      <button class="btn" @click="exportData">导出选中的名单</button>
    </view>
  </view>

  <up-popup
    :show="detailVisible"
    @close="closeDetail"
    :round="10"
    mode="bottom"
  >
    <peopleDetail
      v-if="optPeople"
      :peopleDetail="optPeople"
      @call="(phone) => callPhone(phone, optPeople.id)"
    />
  </up-popup>

  <up-popup
    closeable
    :show="filterVisible"
    @close="closeFilter"
    :round="10"
    mode="bottom"
  >
    <matchingFilter
      :options="filterOptions"
      v-model="filterValues"
      @change="onFilterChange"
    />
  </up-popup>

  <up-popup
    :customStyle="popStyle"
    round="20"
    closeable
    :show="exportTipVisible"
    mode="center"
    :safeAreaInsetBottom="false"
    @close="closeExportTip"
    @open="openExportTip"
  >
    <view class="popbox">
      <view class=""> </view>
      <view class="center"> 已导出，请前往PC端下载 </view>
      <view class="submit" @click="closeExportTip"> 我知道了 </view>
    </view>
  </up-popup>
</template>

<style lang="scss" scoped>
.body {
  width: 750rpx;
  background: rgba(247, 248, 250, 0.65);
  border-radius: 44rpx 44rpx 0rpx 0rpx;
  border: 1rpx solid #fff;
  backdrop-filter: blur(10rpx);
  min-height: calc(100vh - 430rpx - 43rpx);
  margin-top: 331rpx;
  padding: 35rpx 24rpx;

  .filter-bar {
    margin-bottom: 35rpx;
    display: flex;
    gap: 20rpx;

    .search {
      flex: 1;
    }

    .filter-btn {
      width: 160rpx;
      height: 80rpx;
      padding: 0 28rpx;
      background: #ffffff;
      box-shadow: 0rpx 6rpx 13rpx 1rpx rgba(0, 0, 0, 0.04);
      border-radius: 40rpx 40rpx 40rpx 40rpx;
      display: flex;
      justify-content: center;
      align-items: center;

      text {
        font-size: 26rpx;
        color: #21232c;
      }

      .filter-icon {
        width: 28rpx;
        height: 28rpx;
        margin-left: 10rpx;
      }
    }
  }

  .list {
    padding-bottom: 260rpx;
  }

  .box {
    width: 702rpx;
    background: rgba(255, 255, 255, 0.94);
    box-shadow: 0rpx 6rpx 13rpx 1rpx rgba(0, 0, 0, 0.04);
    border-radius: 20rpx 20rpx 20rpx 20rpx;
    border: 2rpx solid #ffffff;
    margin-bottom: 20rpx;
    display: flex;
    align-items: center;
    padding: 46rpx 49rpx;

    .checkbox {
      width: 40rpx;
      height: 40rpx;
      margin-right: 54rpx;
      display: flex;
      justify-content: center;
      align-items: center;

      image {
        width: 40rpx;
        height: 40rpx;
      }
    }

    .box-left {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      line-height: 40rpx;
      .title {
        font-weight: 500;
        font-size: 32rpx;
        color: #333333;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
      .date {
        font-weight: 400;
        font-size: 24rpx;
        color: #bdc4ce;
        text-align: left;
        font-style: normal;
        text-transform: none;
        margin-top: 10rpx;
      }
    }
    .box-right {
      flex-shrink: 0;
      margin-left: 40rpx;
      font-weight: 400;
      font-size: 28rpx;
      color: #9ca3b5;
      display: flex;
      flex-direction: column;
      justify-content: center;
    }
  }
}

.footer {
  position: fixed;
  bottom: 0;
  width: 750rpx;
  background: #ffffff;
  box-shadow: 0rpx -6rpx 13rpx 1rpx rgba(0, 0, 0, 0.04);
  border-radius: 44rpx 44rpx 0rpx 0rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 59rpx 54rpx;
  padding-bottom: calc(59rpx + env(safe-area-inset-bottom) / 2);

  .check-all {
    display: flex;
    align-items: center;

    image {
      width: 40rpx;
      height: 40rpx;
      margin-right: 33rpx;
    }

    text {
      font-weight: bold;
      font-size: 32rpx;
      color: #000000;
    }
  }

  .btn {
    width: 407rpx;
    height: 100rpx;
    line-height: 100rpx;
    background: #577f49;
    border-radius: 50rpx 50rpx 50rpx 50rpx;
    font-weight: 500;
    font-size: 34rpx;
    color: #ffffff;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}

.popbox {
  position: relative;

  display: flex;
  flex-direction: column;
  justify-content: space-around;
  height: 100%;

  image {
    width: 282rpx;
    height: 282rpx;
    margin: 0 auto;
    margin-top: -200rpx;
  }

  .center {
    font-weight: bold;
    font-size: 38rpx;
    color: #06121e;
    line-height: 40rpx;
    text-align: center;
  }

  .submit {
    width: 524rpx;
    height: 100rpx;
    line-height: 100rpx;
    background: #577f49;
    border-radius: 55rpx 55rpx 55rpx 55rpx;
    text-align: center;
    color: #fff;
  }
}
</style>
