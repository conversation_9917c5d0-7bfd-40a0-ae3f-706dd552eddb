<script setup>
import { ref, watch, computed } from "vue"
import { onLoad, onShow } from "@dcloudio/uni-app"
import request from "@/utils/request"
import dayjs from "dayjs"

const messageId = ref(0)
const title = ref("")
const content = ref("")
const datetime = ref("")

const renderString = (html) => {
  content.value = html.replace(
    /<p>/g,
    `<p style="margin-bottom: 16px;">&emsp;&emsp;`
  )

  content.value = html.replace(
    /<img /g,
    '<img style="max-width:100%;height:auto;" '
  )
}

const loadData = () => {
  request({
    url: `/mini/notification/${messageId.value}`,
    method: "get",
  }).then((res) => {
    if (res.code === 200) {
      console.log("res", res)
      title.value = res.result.message_title
      renderString(res.result?.message_content || "")
      datetime.value = dayjs(res.result.created_at).format("YYYY-MM-DD HH:mm")

      if (!res.result.is_read) {
        request({
          url: `/mini/notification_read/${messageId.value}`,
          method: "post",
        })
      }
    }
  })
}

onLoad((options) => {
  console.log("onLoad", options)
  messageId.value = options.messageId
  loadData()
})
</script>

<template>
  <view class="article">
    <view class="title">
      {{ title }}
    </view>
    <view class="datetime">
      <image src="/static/image/time.png" mode=""></image>
      <text>
        {{ datetime }}
      </text>
    </view>
    <view class="content">
      <mp-html
        :content="content"
        :tag-style="{
          p: 'margin-bottom: 42rpx; font-size: 34rpx; color: #111111;',
        }"
      ></mp-html>
    </view>
  </view>
</template>

<style>
page {
  background-color: #ffffff;
}
</style>

<style lang="scss" scoped>
.article {
  padding: 50rpx;
  padding-bottom: 150rpx;

  .title {
    font-weight: bold;
    font-size: 42rpx;
    color: #111111;
    text-align: left;
    font-style: normal;
    margin-bottom: 24rpx;
  }
  .datetime {
    display: flex;
    align-items: center;
    margin-bottom: 64rpx;
    image {
      width: 26rpx;
      height: 26rpx;
    }
    text {
      font-size: 26rpx;
      color: #bdc4ce;
      margin-left: 12rpx;
    }
  }
  .content {
    font-size: 34rpx;
    color: #111111;
    margin-bottom: 35rpx;
  }
}
</style>
