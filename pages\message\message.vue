<script setup>
import { ref, watch, computed } from "vue"
import { onLoad, onShow } from "@dcloudio/uni-app"
import request from "@/utils/request"
import { usePagination } from "@/utils/hooks"
import dayjs from "dayjs"

const list = ref([])
const loadData = (options) => {
  return new Promise((resolve) => {
    request({
      url: "/mini/notification",
      method: "GET",
      data: Object.assign({}, options),
    }).then((res) => {
      list.value.push(...(res.result?.data || []))
      resolve(res)
    })
  })
}

const { page, loadPage, refresh, loadMoreStatus } = usePagination({
  loadData,
  list,
})

const goDetail = (item) => {
  if (!item.is_read) {
    item.is_read = 1
  }
  uni.navigateTo({
    url: `/pages/message/detail?messageId=${item.id}`,
  })
}

onLoad((options) => {
  console.log(options)
  loadPage()
})
</script>

<template>
  <view class="page">
    <view class="list">
      <view
        class="list-item"
        v-for="item in list"
        :key="item.id"
        @click="goDetail(item)"
      >
        <view class="red" v-if="!item.is_read"> </view>

        <view class="title"> {{ item.message_title }} </view>

        <view class="date">
          {{ dayjs(item.created_at).format("YYYY-MM-DD HH:mm") }}
        </view>
      </view>

      <up-loadmore v-if="list.length" :status="loadMoreStatus" />

      <empty
        v-if="list.length === 0 && loadMoreStatus === 'nomore'"
        theme="2"
      />
    </view>
  </view>
</template>

<style lang="scss" scoped>
.page {
  padding: 24rpx;
  .list {
    .list-item {
      position: relative;
      width: 702rpx;
      height: 204rpx;
      background: #ffffff;
      border-radius: 20rpx 20rpx 20rpx 20rpx;
      padding: 24rpx 41rpx;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      margin-bottom: 20rpx;

      .red {
        position: absolute;
        top: 19rpx;
        left: 19rpx;
        width: 10rpx;
        height: 10rpx;
        background: #ff0000;
        border-radius: 50%;
      }
      .title {
        font-weight: 500;
        font-size: 30rpx;
        color: #333333;
        text-align: left;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .date {
        font-weight: 400;
        font-size: 26rpx;
        color: #bdc4ce;
      }
    }
  }
}
</style>
