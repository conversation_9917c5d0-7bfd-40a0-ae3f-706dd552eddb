<script setup>
import { ref, watch, computed } from "vue"
import { onLoad, onShow } from "@dcloudio/uni-app"
import request from "@/utils/request"
import { inputStyle } from "@/utils/constants.js"

const accountFormRef = ref(null)

const accountData = ref({
  old_password: "",
  password: "",
  password_confirmation: "",
})

const accountRules = {
  old_password: {
    type: "string",
    required: true,
    message: "请填写原始密码",
  },
  password: {
    type: "string",
    required: true,
    message: "请填写密码",
  },
  password_confirmation: {
    type: "string",
    required: true,
    message: "请再次输入密码",
  },
}

const submit = () => {
  accountFormRef.value.validate().then((valid) => {
    if (valid) {
      request({
        url: "/mini/update_password",
        method: "post",
        data: accountData.value,
      }).then((res) => {
        if (res.code === 200) {
          navTo(
            "/pages/result/accountResult",
            {
              title: "密码修改成功",
            },
            "redirectTo"
          )
        }
      })
    }
  })
}

onLoad((options) => {
  console.log(options)
})
</script>

<template>
  <view>
    <navHeader title="修改密码" :showLeft="true"></navHeader>
    <view class="body">
      <up-form
        labelPosition="top"
        labelWidth="auto"
        :model="accountData"
        :rules="accountRules"
        ref="accountFormRef"
      >
        <up-form-item label="原始密码" prop="old_password">
          <up-input
            :customStyle="inputStyle"
            v-model="accountData.old_password"
            disabledColor="#ffffff"
            placeholder="输入原始密码"
            border="none"
            type="password"
          >
          </up-input>
        </up-form-item>
        <up-form-item label="新密码" prop="password">
          <up-input
            :customStyle="inputStyle"
            v-model="accountData.password"
            disabledColor="#ffffff"
            placeholder="输入新密码"
            border="none"
            type="password"
          >
          </up-input>
        </up-form-item>
        <up-form-item label="新密码" prop="password_confirmation">
          <up-input
            :customStyle="inputStyle"
            v-model="accountData.password_confirmation"
            disabledColor="#ffffff"
            placeholder="再次输入新密码"
            border="none"
            type="password"
          >
          </up-input>
        </up-form-item>
      </up-form>
    </view>
    <view class="footer">
      <button class="btn" @click="submit">保存</button>
    </view>
  </view>
</template>

<style>
page {
  background-color: #ffffff;
  padding-top: 240rpx;
}
</style>

<style lang="scss" scoped>
.body {
  padding: 0 60rpx;
}

.footer {
  width: 750rpx;
  padding: 40rpx 60rpx;
  padding-bottom: calc(40rpx + env(safe-area-inset-bottom) / 2);
  background: #ffffff;
  box-shadow: 0rpx 3rpx 60rpx 1rpx rgba(0, 0, 0, 0.16);
  border-radius: 22rpx 22rpx 0rpx 0rpx;
  position: absolute;
  bottom: 0rpx;
  .btn {
    width: 630rpx;
    height: 100rpx;
    background: #577f49;
    box-shadow: 0rpx 20rpx 48rpx 1rpx rgba(87, 127, 73, 0.2);
    border-radius: 50rpx 50rpx 50rpx 50rpx;
    color: #fff;
    line-height: 100rpx;
  }
}
</style>
