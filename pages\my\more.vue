<script setup>
import { ref, watch, computed } from "vue"
import { onLoad, onShow } from "@dcloudio/uni-app"
import request from "@/utils/request"
import { useUserStore } from "@/store/user"
import {navTo} from "@/utils/utils";
import {useCommonStore} from "@/store/common";

const userStore = useUserStore()
const commonStore = useCommonStore()

const userAgreementId = computed(() => commonStore.userAgreementId)
const goPrivacy = () => {
  wx.openPrivacyContract()
}

const goUserAgreement = ()=>{
  navTo(`/pages/article/page`, { pageId: userAgreementId.value })
}

const logoutDialogVisible = ref(false)
const logoutOpen = () => {
  logoutDialogVisible.value = true
}
const logoutClose = () => {
  logoutDialogVisible.value = false
}
const confirmLogout = () => {
  userStore.logout()
  logoutClose()
  uni.reLaunch({
    url: "/pages/login/login",
  })
}

onLoad((options) => {
  console.log(options)
})
</script>

<template>
  <view>
    <view class="list">
      <button class="item" @click="goUserAgreement">
        用户协议
        <image src="/static/image/right.png" mode="" class="img"></image>
      </button>
      <button class="item" @click="goPrivacy">
        隐私政策
        <image src="/static/image/right.png" mode="" class="img"></image>
      </button>
      <button class="item" open-type="feedback">
        意见反馈
        <image src="/static/image/right.png" mode="" class="img"></image>
      </button>
    </view>

    <view class="btn" @click="logoutOpen"> 退出登录 </view>
  </view>

  <up-popup
    mode="center"
    :safeAreaInsetBottom="false"
    :show="logoutDialogVisible"
    :round="10"
    closeable
    @close="logoutClose"
    @open="logoutOpen"
  >
    <view class="dialog">
      <view class="dialog-title"> 确定要退出登录吗？ </view>
      <view class="dialog-btn-group">
        <button class="dialog-btn red" @click="confirmLogout">确定</button>
        <button class="dialog-btn" @click="logoutClose">不了</button>
      </view>
    </view>
  </up-popup>
</template>

<style lang="scss" scoped>
.btn {
  width: 702rpx;
  height: 104rpx;
  line-height: 104rpx;
  background: #ffffff;
  border-radius: 20rpx 20rpx 20rpx 20rpx;
  font-weight: 500;
  font-size: 30rpx;
  color: #fb5854;
  text-align: center;
  margin: 0 auto;
}
.list {
  width: 702rpx;
  padding: 20rpx 50rpx;
  background: #ffffff;
  box-shadow: 8rpx 14rpx 48rpx 1rpx rgba(0, 0, 0, 0.02);
  border-radius: 20rpx 20rpx 20rpx 20rpx;
  display: flex;
  flex-direction: column;
  margin: 24rpx auto;
  margin-top: 24rpx;
  .item {
    display: flex;
    justify-content: space-between;
    font-weight: 500;
    font-size: 30rpx;
    background-color: unset;
    color: #111111;
    position: relative;
    margin: unset;
    width: 100%;
    padding: 16rpx 0;

    &::after {
      border: none;
    }

    .img {
      width: 8rpx;
      height: 16rpx;
      position: absolute;
      top: 50%;
      right: 0;
      transform: translateY(-50%);

      image {
        width: 100%;
        height: 100%;
      }
    }
  }
}
</style>
