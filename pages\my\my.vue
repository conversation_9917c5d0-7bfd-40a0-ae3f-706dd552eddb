<script setup>
import { ref, watch, computed } from "vue"
import { onLoad, onShow } from "@dcloudio/uni-app"
import request from "@/utils/request"
import { useUserStore } from "@/store/user"
import { navTo } from "@/utils/utils"

const userStore = useUserStore()
const isLogin = computed(() => userStore.isLogin)
const userInfo = computed(() => userStore.userInfo)

const remindCount = computed(() => userStore.remindCount)

const navList = computed(() => [
  // {
  //   title: "我的积分",
  //   icon: "/static/image/my-nav-1.png",
  //   url: "/pages/score/score",
  // },
  // {
  //   title: "我的举报",
  //   icon: "/static/image/my-nav-2.png",
  //   url: "/pages/report/list",
  // },
  {
    title: "收藏夹",
    icon: "/static/image/my-nav-3.png",
    url: "/pages/favorites/favorites",
  },
  {
    title: "系统消息",
    icon: "/static/image/my-nav-4.png",
    url: "/pages/message/message",
    redDot: remindCount.value > 0,
  },
])

const menuList = computed(() => [
  {
    title: "电子证书",
    url: "/pages/electronicCertificate/electronicCertificate",
  },
  {
    title: "个人资料",
    url: "/pages/my/personalData",
  },
  {
    title: "修改密码",
    url: "/pages/my/changePassword",
  },
  {
    title: "更多",
    url: "/pages/my/more",
  },
])

const onUserInfoClick = () => {
  if (!isLogin.value) {
    navTo("/pages/login/login")
  }
}

const hasCert = ref(false)
const loadCert = async () => {
  const res = await request({ url: "/mini/certificate" })
  hasCert.value = res.result.data.length > 0
}

onLoad((options) => {
  uni.hideTabBar()
})
onShow(() => {
  if (isLogin.value) {
    loadCert()
    userStore.getUserInfo()
  }
})
</script>

<template>
  <view class="page">
    <navHeader
      title="我的"
      bg="/static/image/bg.png"
      color="#000"
      :showLeft="false"
    ></navHeader>
    <view class="header" @click="onUserInfoClick">
      <up-avatar
        default-url="/static/image/avatar.png"
        :src="userInfo.avatar_id_attachments?.full_path"
        class="avatar"
        size="60"
      ></up-avatar>
      <view class="info">
        <view class="name"> {{ userInfo.name }} </view>
        <view
          class="cert"
          :class="{ active: hasCert }"
          @click="navTo('/pages/electronicCertificate/electronicCertificate')"
        >
          <template v-if="hasCert">
            <image src="/static/image/cert.svg" mode=""></image>
            已获得征兵工作资格证书
          </template>
          <template v-else> 尚未获得征兵工作资格证书 </template>
        </view>
      </view>
    </view>
    <view class="body">
      <view class="box">
        <view
          class="item"
          v-for="(item, index) in navList"
          :key="index"
          @click="navTo(item.url)"
        >
          <view class="item-icon">
            <image :src="item.icon" mode=""></image>
            <view class="red-dot" v-if="item.redDot"></view>
          </view>
          <view class="item-title">
            {{ item.title }}
          </view>
        </view>
      </view>

      <view class="list">
        <view
          class="item"
          @click="navTo(item.url)"
          v-for="(item, index) in menuList"
          :key="index"
        >
          {{ item.title }}
          <image src="/static/image/right.png" mode="" class="img"></image>
        </view>
      </view>
    </view>
    <view style="height: 200rpx"></view>
    <tabNav index="3" />
  </view>
</template>

<style lang="scss" scoped>
.page {
  position: absolute;
  top: 0;
  left: 0;
  height: 100vh;
  z-index: 1;
  padding: 24rpx;
  background-color: #f7f8fa;
}

.header {
  display: flex;

  .avatar {
    flex-shrink: 0;
    width: 108rpx;
    height: 108rpx;
  }

  .info {
    margin-left: 27rpx;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 4rpx 0;

    .name {
      font-weight: bold;
      font-size: 38rpx;
      line-height: 54rpx;
      color: #21232c;
    }

    .cert {
      height: 45rpx;
      line-height: 45rpx;
      padding: 0 17rpx;
      background: #bdc4ce;
      border-radius: 23rpx 23rpx 23rpx 23rpx;
      border: 1rpx solid #ffffff;
      font-weight: 500;
      font-size: 22rpx;
      color: #ffffff;
      display: flex;
      align-items: center;

      image {
        width: 20rpx;
        height: 20rpx;
        margin-right: 8rpx;
      }

      &.active {
        color: #577f49;
        background: #f3ffef;
      }
    }
  }
}

.body {
  margin-top: 56rpx;

  .list {
    width: 702rpx;
    padding: 24rpx 50rpx;
    background: #ffffff;
    box-shadow: 8rpx 14rpx 48rpx 1rpx rgba(0, 0, 0, 0.02);
    border-radius: 20rpx 20rpx 20rpx 20rpx;
    display: flex;
    flex-direction: column;
    margin-top: 24rpx;

    .item {
      display: flex;
      justify-content: space-between;
      padding: 38rpx 0;

      .img {
        width: 8rpx;
        height: 16rpx;
        margin-top: 14rpx;
        image {
          width: 100%;
          height: 100%;
        }
      }
    }
  }

  .box {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 24rpx;
    width: 702rpx;
    height: 217rpx;
    background: #ffffff;
    box-shadow: 8rpx 14rpx 48rpx 1rpx rgba(0, 0, 0, 0.02);
    border-radius: 20rpx 20rpx 20rpx 20rpx;

    .item {
      display: flex;
      flex-direction: column;
      justify-content: center;
      gap: 16rpx;
      // width: 25%;
      text-align: center;

      .item-icon {
        width: 102rpx;
        height: 102rpx;
        background: #f7f8fa;
        margin: 0 auto;
        border-radius: 42rpx;
        overflow: hidden;
        position: relative;

        image {
          width: 100%;
          height: 100%;
        }

        .red-dot {
          width: 16rpx;
          height: 16rpx;
          background: #ff0000;
          border: 2px solid #ffffff;
          border-radius: 50%;
          position: absolute;
          top: 24rpx;
          right: 30rpx;
        }
      }

      .item-title {
        font-weight: 500;
        font-size: 28rpx;
        color: #303445;
      }
    }
  }
}

.title {
  margin-top: 40rpx;

  font-weight: 800;
  font-size: 48rpx;
  color: #21232c;
  text-align: center;
  font-style: normal;
  text-transform: none;
  margin-bottom: 70rpx;
}

.header {
  margin-top: 158rpx;
}

.footer {
  width: 750rpx;
  height: 238rpx;
  background: #ffffff;
  border-radius: 22rpx 22rpx 0rpx 0rpx;

  .btn {
    width: 630rpx;
    height: 100rpx;
    background: #577f49;
    box-shadow: 0rpx 20rpx 48rpx 1rpx rgba(87, 127, 73, 0.2);
    border-radius: 50rpx 50rpx 50rpx 50rpx;
    color: #fff;
    line-height: 100rpx;
    margin-top: 45rpx;
  }
}
</style>
