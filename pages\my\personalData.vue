<script setup>
import { ref, watch, computed } from "vue"
import { onLoad, onShow } from "@dcloudio/uni-app"
import request, { fetchUpload } from "@/utils/request"
import { inputStyle } from "@/utils/constants.js"

const originalData = ref({})
const formData = ref({
  name: "",
  id_card: "",
  phone: "",
  sms_code: "",
  department_code: "",
  job: "",
  business_type_id: "",
})

const rules = {
  name: [{ required: true, message: "请输入姓名", trigger: "blur" }],
  id_card: [{ required: true, message: "请输入身份证号码", trigger: "blur" }],
  phone: [{ required: true, message: "请输入手机号", trigger: "blur" }],
  sms_code: [
    {
      validator: (rule, value) => {
        if (codeVisible.value && !value) {
          return "请输入手机验证码"
        }
        return true
      },
    },
  ],
  department_code: [{ required: true, message: "请选择单位", trigger: "blur" }],
  job: [{ required: true, message: "请输入职务", trigger: "blur" }],
  // business_type_id: [
  //   {
  //     type: "number",
  //     required: true,
  //     message: "请选择业务类别",
  //     trigger: "blur",
  //   },
  // ],
}

const uFormRef = ref(null)

const submitDialogVisible = ref(false)
const submitOpen = () => {
  submitDialogVisible.value = true
}
const submitClose = () => {
  submitDialogVisible.value = false
}

const checkSubmit = () => {
  console.log("submit", formData.value)

  uFormRef.value.validate().then((valid) => {
    if (valid) {
      submitOpen()
    }
  })
}

const confirmSubmit = () => {
  request({
    url: "/mini/info",
    method: "post",
    data: formData.value,
  }).then((res) => {
    console.log(res)
    if (res.code === 200) {
      submitClose()
      uni.$u.toast("保存成功")
      setTimeout(() => {
        uni.navigateBack()
      }, 1500)
    }
  })
}

const codeVisible = ref(false)
watch(
  () => formData.value.phone,
  (val) => {
    if (val && val.length === 11 && val !== originalData.value.phone) {
      codeVisible.value = true
    } else {
      codeVisible.value = false
    }
  }
)

const smsCountdown = ref(0)
const sendSmsCode = () => {
  if (!formData.value.phone) {
    uni.$u.toast("请输入手机号")
    return
  }
  request({
    url: "/mini/send_code",
    method: "post",
    data: {
      phone: formData.value.phone,
      scene: "update_info",
    },
  }).then((res) => {
    if (res.code === 200) {
      uni.$u.toast("发送成功")
      smsCountdown.value = 60
      const timer = setInterval(() => {
        smsCountdown.value--
        if (smsCountdown.value <= 0) {
          clearInterval(timer)
        }
      }, 1000)
    } else {
      getCaptcha()
    }
  })
}

const avatar = ref("")
const uploadAvatar = (e) => {
  fetchUpload(e.detail.avatarUrl).then((res) => {
    console.log(res)
    avatar.value = res.result.file.full_path
    formData.value.avatar_id = res.result.file.id
  })
}

const departmentName = ref("")
const departmentData = ref([])
const treeRef = ref(null)
const departmentPickerVisible = ref(false)
const closeDepartmentPicker = () => {
  departmentPickerVisible.value = false
}
const openDepartmentPicker = () => {
  departmentPickerVisible.value = true
}
const onDepartmentChange = (val, name) => {
  console.log(val, name)
  formData.value.department_code = val[0]
  departmentName.value = name
  closeDepartmentPicker()
}

const loadData = () => {
  console.log("loadData")
  request({
    url: "/mini/info",
    method: "get",
  }).then((res) => {
    console.log(res)
    originalData.value = { ...res.result }

    formData.value = res.result
    if (res.result.avatar_id) {
      avatar.value = res.result.avatar_id_attachments.full_path
    } else {
      avatar.value = "/static/image/avatar.png"
    }
    departmentName.value = res.result?.department_name || ""
  })
}

const loadDepartmentData = () => {
  request({
    url: "/mini/department_tree?p_code=c1101",
    method: "get",
  }).then((res) => {
    departmentData.value = res.result
  })
}

onLoad((options) => {
  loadDepartmentData()
  loadData()
})
</script>

<template>
  <view class="page">
    <up-form
      labelPosition="top"
      labelWidth="auto"
      :model="formData"
      :rules="rules"
      ref="uFormRef"
    >
      <button
        class="upload-avatar"
        open-type="chooseAvatar"
        @chooseavatar="uploadAvatar"
      >
        <image :src="avatar" mode="aspectFill" class="avatar" />
        <view class="btn">
          <image class="icon" src="/static/image/camera.svg" />
        </view>
      </button>

      <up-form-item label="姓名" prop="name">
        <up-input
          :customStyle="inputStyle"
          v-model="formData.name"
          border="none"
          placeholder="请输入姓名"
        ></up-input>
      </up-form-item>
      <up-form-item label="身份证号码" prop="id_card">
        <up-input
          :customStyle="inputStyle"
          v-model="formData.id_card"
          disabledColor="#ffffff"
          placeholder="请输入身份证号码"
          border="none"
        >
        </up-input>
      </up-form-item>

      <up-form-item label="手机号" prop="phone">
        <up-input
          :customStyle="inputStyle"
          v-model="formData.phone"
          disabledColor="#ffffff"
          placeholder="请输入手机号"
          border="none"
        ></up-input>
      </up-form-item>

      <up-form-item label="手机验证码" prop="sms_code" v-if="codeVisible">
        <up-input
          :customStyle="inputStyle"
          v-model="formData.sms_code"
          disabledColor="#ffffff"
          placeholder="请输入手机验证码"
          border="none"
        ></up-input>
        <button
          class="sms-btn"
          @click="sendSmsCode"
          :disabled="smsCountdown > 0"
        >
          {{ smsCountdown > 0 ? smsCountdown + "s" : "获取验证码" }}
        </button>
      </up-form-item>
      <up-form-item
        label="单位"
        prop="department_code"
        @click="openDepartmentPicker"
      >
        <up-input
          :modelValue="departmentName"
          readonly
          :customStyle="inputStyle"
          disabledColor="#ffffff"
          placeholder="请选择单位"
          border="none"
        ></up-input>
        <template #right style="margin-right: 100rpx">
          <up-icon name="arrow-right"></up-icon>
        </template>
      </up-form-item>
      <up-form-item label="职务" prop="job">
        <up-input
          :customStyle="inputStyle"
          v-model="formData.job"
          disabledColor="#ffffff"
          placeholder="请输入职务"
          border="none"
        ></up-input>
      </up-form-item>
    </up-form>

    <view class="footer">
      <button class="btn" @click="checkSubmit">保存</button>
    </view>
  </view>

  <!-- <up-action-sheet
    :show="showBusinessType"
    :actions="businessTypes"
    title="请选择业务类别"
    @close="showBusinessType = false"
    @select="changeBusinessType"
  >
  </up-action-sheet> -->

  <up-popup
    closeable
    :show="departmentPickerVisible"
    @close="closeDepartmentPicker"
    :round="10"
    @open="openDepartmentPicker"
    mode="bottom"
  >
    <view class="department-picker">
      <view class="picker-title">筛选</view>
      <view class="picker-content">
        <treePicker
          v-if="departmentData.length"
          ref="treeRef"
          valueKey="code"
          :data="departmentData"
          themeColor="#577F49"
          :multiple="false"
          @change="onDepartmentChange"
        />
      </view>
    </view>
  </up-popup>

  <up-popup
    mode="center"
    :safeAreaInsetBottom="false"
    :show="submitDialogVisible"
    :round="10"
    closeable
    @close="submitClose"
    @open="submitOpen"
  >
    <view class="dialog">
      <view class="dialog-title"> 确定要保存吗？ </view>
      <view class="dialog-btn-group">
        <button class="dialog-btn" @click="submitClose">不了</button>
        <button class="dialog-btn red" @click="confirmSubmit">确定</button>
      </view>
    </view>
  </up-popup>
</template>

<style lang="scss">
.u-form-item__body__right__content {
  position: relative;
}
.item__body__right__content__icon {
  transform: translateX(-70rpx);
  position: absolute;
  right: 0rpx;
}
</style>

<style lang="scss">
page {
  background-color: #fff;
}
</style>

<style lang="scss" scoped>
.page {
  background-color: #fff;
  padding: 0 60rpx;
  padding-bottom: 250rpx;
}

.upload-avatar {
  background: unset;
  margin: 48px auto;
  width: 240rpx;
  height: 240rpx;
  position: relative;

  .avatar {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: #f5f5f5;
  }

  .btn {
    position: absolute;
    bottom: 0;
    right: 0;

    width: 64rpx;
    height: 64rpx;
    background: linear-gradient(
      135deg,
      rgba(255, 255, 255, 0.9) 0%,
      rgba(255, 255, 255, 0.7) 100%
    );
    box-shadow: 0rpx 32rpx 48rpx 0rpx rgba(160, 163, 189, 0.16);
    border-radius: 108rpx 108rpx 108rpx 108rpx;
    border: 2rpx solid rgba(252, 252, 252, 0.5);

    display: flex;
    justify-content: center;
    align-items: center;

    .icon {
      width: 33rpx;
      height: 30rpx;
    }
  }
}

.footer {
  width: 750rpx;
  height: 226rpx;
  background: #ffffff;
  box-shadow: 0rpx 3rpx 60rpx 1rpx rgba(0, 0, 0, 0.16);
  border-radius: 22rpx 22rpx 0rpx 0rpx;
  position: fixed;
  bottom: 0rpx;
  left: 0rpx;
  z-index: 9;

  .btn {
    width: 630rpx;
    height: 100rpx;
    background: #577f49;
    box-shadow: 0rpx 20rpx 48rpx 1rpx rgba(87, 127, 73, 0.2);
    border-radius: 50rpx 50rpx 50rpx 50rpx;
    line-height: 100rpx;
    color: #fff;
    margin: 40rpx auto;
    text-align: center;
  }
}

.sms-btn {
  font-size: 32rpx;
  color: #577f49;
  border: none;
  background: none;
  position: absolute;
  right: 34rpx;
  top: 50%;
  transform: translateY(-50%);
  z-index: 2;
}

.department-picker {
  padding: 24rpx;
  padding-bottom: 0;
  height: 75vh;
  display: flex;
  flex-direction: column;

  .picker-title {
    font-size: 32rpx;
    color: #111111;
    font-weight: bold;
    margin-bottom: 24rpx;
    text-align: center;
  }

  .picker-content {
    margin-top: 24rpx;
    flex: 1;
    overflow: hidden;
  }

  .btn-wrapper {
    display: flex;
    justify-content: center;
    padding: 0 36rpx;
    margin-top: 24rpx;

    .btn {
      width: 100%;
      height: 100rpx;
      line-height: 100rpx;
      font-size: 34rpx;
      color: #ffffff;
      background: #577f49;
      box-shadow: 0rpx 20rpx 48rpx 1rpx rgba(87, 127, 73, 0.2);
      border-radius: 55rpx 55rpx 55rpx 55rpx;
    }
  }
}
</style>
