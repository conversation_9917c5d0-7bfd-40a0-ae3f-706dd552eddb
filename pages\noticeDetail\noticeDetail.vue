<script setup>
import { ref, watch, computed } from "vue"
import { onLoad, onShow } from "@dcloudio/uni-app"
import request from "@/utils/request"
import { navTo } from "@/utils/utils"
import dayjs from "dayjs"

const id = ref(null)
const scope = ref("")

const activeTabIndex = ref(0)

const goEdit = () => {
  navTo(`/pages/office/postNotice?id=${id.value}`)
}

const deleteDialogVisible = ref(false)
function deleteOpen() {
  deleteDialogVisible.value = true
}
function deleteClose() {
  deleteDialogVisible.value = false
}

const confirmDel = () => {
  request({
    url: `/mini/inform/${id.value}`,
    method: "delete",
  }).then(() => {
    uni.showToast({
      title: "删除成功",
      icon: "success",
    })
    uni.navigateBack()
  })
}

const detailData = ref({})
const loadData = () => {
  request({
    url: `/mini/inform/${id.value}`,
  }).then((res) => {
    detailData.value = res.result
  })
}

onLoad((options) => {
  console.log(options)
  if (options.scope) {
    scope.value = options.scope
  }
  if (options.id) {
    id.value = parseInt(options.id)
  }
})

onShow(() => {
  if (id.value) {
    loadData()
  }
})
</script>

<template>
  <view class="article">
    <view class="title">
      {{ detailData.title }}
    </view>
    <view class="info">
      <view class="info-item">
        <image src="/static/image/time.png" mode=""></image>
        <text>
          {{ dayjs(detailData.created_at).format("YYYY-MM-DD HH:mm") }}
        </text>
      </view>
      <view class="info-item">
        <image src="/static/image/user.png" mode=""></image>
        <text>
          {{ detailData.district_name }}{{ detailData.department_name }}
        </text>
      </view>
    </view>

    <view class="content">
      <view class="tabs">
        <view
          class="tab-item"
          :class="{ active: activeTabIndex === 0 }"
          @click="activeTabIndex = 0"
          >通知详情</view
        >
        <view
          class="tab-item"
          :class="{ active: activeTabIndex === 1 }"
          @click="activeTabIndex = 1"
          >接收单位</view
        >
      </view>

      <view class="detail-wrapper" v-if="activeTabIndex === 0">
        <mp-html
          :content="detailData.content"
          :tag-style="{
            p: 'margin-bottom: 42rpx; font-size: 34rpx; color: #111111;',
          }"
        ></mp-html>

        <view
          class="files"
          v-if="
            detailData.file_ids_attachments &&
            detailData.file_ids_attachments.length > 0
          "
        >
          <fileList :fileList="detailData.file_ids_attachments" />
        </view>
      </view>
      <view class="departments-wrapper" v-if="activeTabIndex === 1">
        <view class="departments">
          <view class="department" v-for="item in detailData.receive_departments">
            {{ item.tree_name }}
          </view>
        </view>
      </view>
    </view>
  </view>

  <view style="height: 220rpx"></view>

  <view class="footer" v-if="scope === 'create'">
    <button class="delete" @click="deleteOpen">删除</button>
    <button class="edit" @click="goEdit">修改</button>
  </view>

  <up-popup
    mode="center"
    :safeAreaInsetBottom="false"
    :show="deleteDialogVisible"
    :round="10"
    closeable
    @close="deleteClose"
    @open="deleteOpen"
  >
    <view class="dialog">
      <view class="dialog-title"> 确定要删除此通知吗？ </view>

      <view class="dialog-btn-group">
        <button class="dialog-btn red" @click="confirmDel">确定删除</button>
        <button class="dialog-btn" @click="deleteClose">不了</button>
      </view>
    </view>
  </up-popup>
</template>

<style>
page {
  background-color: #ffffff;
}
</style>

<style lang="scss" scoped>
.article {
  padding: 50rpx;
  padding-bottom: 150rpx;

  .title {
    font-weight: bold;
    font-size: 42rpx;
    color: #111111;
    text-align: left;
    font-style: normal;
    margin-bottom: 24rpx;
  }

  .info {
    display: flex;
    gap: 70rpx;
    margin-bottom: 64rpx;

    .info-item {
      display: flex;
      align-items: center;

      image {
        width: 26rpx;
        height: 26rpx;
      }
      text {
        font-size: 26rpx;
        color: #bdc4ce;
        margin-left: 12rpx;
      }
    }
  }

  .content {
    font-size: 34rpx;
    color: #111111;
    margin-bottom: 35rpx;
  }
  .files {
    margin-top: 20rpx;
  }
}

.tabs {
  display: flex;
  gap: 55rpx;
  margin-bottom: 30rpx;

  .tab-item {
    font-weight: 500;
    font-size: 30rpx;
    color: #8c9198;
    padding-bottom: 18rpx;
    position: relative;

    &.active {
      font-weight: bold;
      color: #303445;

      &::after {
        content: "";
        position: absolute;
        bottom: 0rpx;
        left: 50%;
        transform: translateX(-50%);
        width: 24rpx;
        height: 6rpx;
        background: #577f49;
        border-radius: 3rpx;
      }
    }
  }
}

.departments-wrapper {
  //max-height: 500rpx;
  //overflow-y: auto;

  .department {
    margin-bottom: 8rpx;
    font-weight: 400;
    font-size: 30rpx;
    color: #000000;
  }
}

.footer {
  display: flex;
  justify-content: space-between;
  width: 750rpx;
  min-height: 226rpx;
  background: #ffffff;
  box-shadow: 0rpx 3rpx 60rpx 1rpx rgba(0, 0, 0, 0.16);
  border-radius: 22rpx 22rpx 0rpx 0rpx;
  padding: 40rpx 60rpx;
  padding-bottom: calc(env(safe-area-inset-bottom) / 2 + 40rpx);
  position: fixed;
  z-index: 1;
  bottom: 0rpx;
  left: 0rpx;

  button {
    width: 285rpx;
    height: 100rpx;
    line-height: 100rpx;
    background: #ffffff;
    color: #21232c;
    font-size: 34rpx;
    border-radius: 50rpx 50rpx 50rpx 50rpx;
    border: 1rpx solid #e1e1e1;

    &.delete {
      color: #ea2b2b;
    }
  }
}
</style>
