<script setup>
import { ref, watch, computed } from "vue"
import {
  onLoad,
  onShow,
  onPullDownRefresh,
  onReachBottom,
} from "@dcloudio/uni-app"
import request from "@/utils/request"
import { usePagination } from "@/utils/hooks"

const list = ref([])
const loadData = (options) => {
  return new Promise((resolve) => {
    request({
      url: "/mini/inform?scope=can_handle",
      method: "GET",
      data: Object.assign({}, options),
    }).then((res) => {
      list.value.push(...(res.result?.data || []))
      resolve(res)
    })
  })
}

const { page, loadPage, refresh, loadMoreStatus } = usePagination({
  loadData,
  list,
})

const goDetail = (item) => {
  uni.navigateTo({
    url: `/pages/noticeDetail/noticeDetail?id=${item.id}`,
  })
}

onLoad((options) => {
  console.log(options)
  loadPage()
})
onPullDownRefresh(() => {
  refresh()
})
onReachBottom(() => {
  loadPage()
})
</script>

<template>
  <view class="page">
    <navHeader
      title="通知公告"
      bg="/static/image/bg.png"
      :showLeft="true"
    ></navHeader>
    <view class="body">
      <notifyItem
        v-for="(item, index) in list"
        :key="index"
        :item="item"
        @openDetail="goDetail(item)"
      />
      <empty
        v-if="list.length === 0 && loadMoreStatus === 'nomore'"
        theme="2"
      />
      <up-loadmore v-if="list.length" :status="loadMoreStatus" />
    </view>
  </view>
</template>

<style lang="scss" scoped>
.body {
  padding-top: 200rpx;
  padding-bottom: 50rpx;
}
</style>
