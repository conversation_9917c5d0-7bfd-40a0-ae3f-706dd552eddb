<script setup>
import { ref, watch, computed } from "vue"
import dayjs from "dayjs"
import { navTo } from "@/utils/utils"
import { usePagination } from "@/utils/hooks"
import request from "@/utils/request"
import { surveyStatusMap } from "@/utils/constants"

const props = defineProps({
  queryParams: {
    type: Object,
    default: () => ({}),
  },
})

const goDetail = (item) => {
  if (
    item.status === surveyStatusMap.finished.status ||
    item.is_answer === true
  ) {
    uni.showToast({
      title:
        item.status === surveyStatusMap.finished.status ? "已过期" : "已填写",
      icon: "none",
    })
    return
  }

  navTo(`/pages/office/questionnaireDetail?surveyId=${item.id}`)
}

const list = ref([])
const total = ref(0)
const loadData = async (options) => {
  return new Promise((resolve) => {
    request({
      url: "/mini/survey",
      method: "GET",
      data: Object.assign(
        {
          scope: "can_handle",
          ...props.queryParams,
        },
        options
      ),
    }).then((res) => {
      list.value.push(...(res.result?.data || []))
      total.value = res.result?.total || 0
      resolve(res)
    })
  })
}
const { page, loadPage, loadMoreStatus, refresh } = usePagination({
  loadData,
  list,
})

defineExpose({
  total,
  list,
  loadPage,
  refresh,
})
</script>

<template>
  <view class="list">
    <view
      class="item"
      v-for="(item, index) in list"
      :key="index"
      @click="goDetail(item)"
    >
      <view class="left">
        <view class="content">
          {{ item.name }}
        </view>
        <view class="date">
          <image src="/static/image/time.png" mode="" class="icon"></image>
          {{ dayjs(item.start_at).format("YYYY.MM.DD") }} -
          {{ dayjs(item.end_at).format("YYYY.MM.DD") }}
        </view>
      </view>
      <view class="right">
        <view
          class="btn disabled"
          v-if="item.status === surveyStatusMap.finished.status"
        >
          已过期
        </view>
        <view class="btn" v-else-if="item.is_answer === false"> 填写 </view>
        <view class="btn disabled" v-else-if="item.is_answer === true">
          已填写
        </view>
      </view>
    </view>

    <empty v-if="!list.length && loadMoreStatus === 'nomore'" />
    <up-loadmore v-if="list.length" :status="loadMoreStatus" />
  </view>
</template>

<style lang="scss" scoped>
.list {
  .item {
    background: #ffffff;
    box-shadow: 0rpx 6rpx 13rpx 1rpx rgba(0, 0, 0, 0.04);
    border-radius: 20rpx 20rpx 20rpx 20rpx;
    padding: 30rpx;
    display: flex;
    justify-content: space-between;
    gap: 45rpx;
    margin-bottom: 20rpx;

    .left {
      flex: 1;
      overflow: hidden;

      .content {
        height: 89rpx;
        line-height: 44rpx;
        margin-bottom: 30rpx;

        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        word-break: break-all;

        text-overflow: ellipsis;
        overflow: hidden;
      }

      .date {
        font-weight: 400;
        font-size: 24rpx;
        color: #bdc4ce;
        display: flex;
        align-items: center;

        .icon {
          width: 20rpx;
          height: 20rpx;
          margin-right: 12rpx;
        }
      }
    }

    .right {
      flex-shrink: 0;
      display: flex;
      flex-direction: column;
      justify-content: center;

      .btn {
        width: 121rpx;
        height: 61rpx;
        background: #577f49;
        border-radius: 31rpx 31rpx 31rpx 31rpx;
        font-weight: 500;
        font-size: 26rpx;
        color: #ffffff;
        line-height: 61rpx;
        text-align: center;

        &.disabled {
          background: #f7f8fa;
          color: #9ca3b5;
        }
      }
    }
    .right2 {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      .text {
        width: 86rpx;
        height: 37rpx;
        background: #f39c12;
        border-radius: 15rpx 15rpx 15rpx 0rpx;
        opacity: 0.14;
        font-weight: 500;
        font-size: 22rpx;
        color: #f39c12;
      }
    }
  }
}
</style>
