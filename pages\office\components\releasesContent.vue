<script setup>
import { ref, computed } from "vue"
import dayjs from "dayjs"
import request from "@/utils/request"
import { navTo } from "@/utils/utils"

const currentTabIndex = ref(0)
const currentTab = computed(() => tabs.value[currentTabIndex.value])
const tabs = ref([
  {
    title: "任务",
    type: "task",
    pagination: {
      list: [],
      page: 1,
      size: 10,
      loadMoreStatus: "loadmore",
      loadMore: function () {
        console.log("loadmore", this)
        if (this.loadMoreStatus !== "loadmore") return
        this.loadMoreStatus = "loading"
        request({
          url: "/mini/work_task",
          method: "get",
          data: {
            page: this.page,
            size: this.size,
            scope: "create",
            status: currentFilter.value,
          },
        }).then((res) => {
          if (res.result?.next_page_url) {
            this.loadMoreStatus = "loadmore"
            this.page++
          } else {
            this.loadMoreStatus = "nomore"
          }
          this.list.push(...(res.result?.data || []))
        })
      },
      refresh: function () {
        this.loadMoreStatus = "loadmore"
        this.page = 1
        this.list = []
        this.loadMore()
      },
    },
  },
  {
    title: "通知",
    type: "notice",
    pagination: {
      list: [],
      page: 1,
      size: 10,
      loadMoreStatus: "loadmore",
      loadMore: function () {
        console.log("loadmore", this)
        if (this.loadMoreStatus !== "loadmore") return
        this.loadMoreStatus = "loading"
        request({
          url: "/mini/inform",
          method: "get",
          data: {
            page: this.page,
            size: this.size,
            scope: "create",
            status: currentFilter.value,
          },
        }).then((res) => {
          if (res.result?.next_page_url) {
            this.loadMoreStatus = "loadmore"
            this.page++
          } else {
            this.loadMoreStatus = "nomore"
          }
          this.list.push(...(res.result?.data || []))
        })
      },
      refresh: function () {
        this.loadMoreStatus = "loadmore"
        this.page = 1
        this.list = []
        this.loadMore()
      },
    },
  },
])
const changeTab = (index) => {
  currentTabIndex.value = index
  if (tabs.value[index].pagination.list.length === 0) {
    loadMore()
  }
}

const refresh = () => {
  currentTab.value.pagination.refresh()
}

const filterRef = ref()
const filterOptions = computed(() => [
  {
    value: "",
    label: "全部状态",
  },
  {
    value: "published",
    label: "已发布",
  },
  {
    value: "draft",
    label: "未发布",
  },
])
const currentFilter = ref("")
const onFilterChange = (e) => {
  currentTab.value.pagination.refresh()
}

const optType = ref("task")
const optItem = ref(null)
const deleteDialogVisible = ref(false)
function deleteOpen() {
  closeActionsPopup()
  deleteDialogVisible.value = true
}
function deleteClose() {
  deleteDialogVisible.value = false
}

const goEdit = () => {
  if (optType.value === "task") {
    navTo(`/pages/office/postTask?id=${optItem.value.id}`)
  } else {
    navTo(`/pages/office/postNotice?id=${optItem.value.id}`)
  }
}
async function confirmDel() {
  if (optType.value === "task") {
    await request({
      url: `/mini/work_task/${optItem.value.id}`,
      method: "delete",
    })
  } else {
    await request({
      url: `/mini/inform/${optItem.value.id}`,
      method: "delete",
    })
  }
  deleteClose()
  currentTab.value.pagination.list.splice(
    currentTab.value.pagination.list.indexOf(optItem.value),
    1
  )
  uni.showToast({
    title: "删除成功",
    icon: "none",
  })
}

const actionsPopupVisible = ref(false)
function openActionsPopup(type, item) {
  optType.value = type
  optItem.value = item
  actionsPopupVisible.value = true
}
function closeActionsPopup() {
  optItem.value = null
  actionsPopupVisible.value = false
}

const loadMore = () => {
  currentTab.value.pagination.loadMore()
}

const goNoticeDetail = (item) => {
  if (item.status === "draft") {
    navTo(`/pages/office/postNotice?id=${item.id}`)
  } else {
    navTo(`/pages/noticeDetail/noticeDetail?id=${item.id}&scope=create`)
  }
}

const goTaskDetail = (item) => {
  if (item.status === "draft") {
    navTo(`/pages/office/postTask?id=${item.id}`)
  } else {
    navTo(`/pages/office/taskDetail?taskId=${item.id}&scope=create`)
  }
}

defineExpose({
  pagination: () => currentTab.value.pagination,
  loadMore,
  refresh,
})
</script>

<template>
  <view class="releases-content">
    <view class="filter">
      <view class="type">
        <view
          v-for="(item, index) in tabs"
          :key="index"
          @click="changeTab(index)"
          :class="currentTabIndex == index ? 'checked' : 'text'"
        >
          {{ item.title }}
        </view>
      </view>

      <u-dropdown
        ref="filterRef"
        active-color="#577f49"
        inactive-color="#bdc4ce"
      >
        <u-dropdown-item
          v-model="currentFilter"
          :title="
            filterOptions.find((item) => item.value == currentFilter)?.label ||
            '全部状态'
          "
          :options="filterOptions"
          @change="onFilterChange"
        ></u-dropdown-item>
      </u-dropdown>
    </view>
    <view class="list" v-if="currentTabIndex == 0">
      <view
        class="task-item"
        v-for="(item, index1) in tabs[0].pagination.list"
        :key="index1"
        @click="goTaskDetail(item)"
      >
        <view class="left">
          <view class="content">
            {{ item.title }}
          </view>
          <view class="date">
            <image src="/static/image/time.png" mode="" class="icon"></image>
            {{
              item.status == "published"
                ? dayjs(item.submitted_at).format("YYYY-MM-DD")
                : "未发布"
            }}
          </view>
        </view>
        <view class="right2">
          <template>
            <view class="finish-status" v-if="item.status == 'published'">
              <text class="done">{{ item.report_department_count }}</text
              >/{{ item.departments_count }}
            </view>
            <button class="pub-btn" v-if="item.status == 'draft'">发布</button>
          </template>

          <view class="more" @click.stop="openActionsPopup('task', item)">
            <image src="/static/image/more.png" />
          </view>
        </view>
      </view>

      <up-loadmore
        v-if="tabs[0].pagination.list.length"
        :status="tabs[0].pagination.loadMoreStatus"
      />
    </view>
    <view class="list" v-if="currentTabIndex == 1">
      <view
        class="notify-item"
        v-for="(item, index2) in tabs[1].pagination.list"
        :key="index2"
        @click="goNoticeDetail(item)"
      >
        <view class="top">
          <view class="title"> {{ item.title }} </view>
          <button class="pub-btn" v-if="item.status == 'draft'">发布</button>
        </view>
        <view class="bottom">
          <view class="time">
            <image src="/static/image/time.png" mode=""></image>
            <text>
              {{
                item.status == "published"
                  ? dayjs(item.created_at).format("YYYY-MM-DD")
                  : "未发布"
              }}
            </text>
          </view>

          <view class="more" @click.stop="openActionsPopup('notice', item)">
            <image src="/static/image/more.png" />
          </view>
        </view>
      </view>

      <up-loadmore
        v-if="tabs[1].pagination.list.length"
        :status="tabs[1].pagination.loadMoreStatus"
      />
    </view>
  </view>

  <up-popup
    mode="bottom"
    :show="actionsPopupVisible"
    @close="closeActionsPopup"
  >
    <view>
      <view class="popbox">
        <view class="box bottom" @click="goEdit"> 修改 </view>
        <view class="box" @click="deleteOpen"> 删除 </view>
      </view>
      <view class="popbox" style="margin-top: 24rpx">
        <view class="box" @click="closeActionsPopup"> 取消 </view>
      </view>
    </view>
  </up-popup>

  <up-popup
    mode="center"
    :safeAreaInsetBottom="false"
    :show="deleteDialogVisible"
    :round="10"
    closeable
    @close="deleteClose"
    @open="deleteOpen"
  >
    <view class="dialog">
      <view class="dialog-title">
        确定要删除此
        {{ optType == "task" ? "任务" : "通知" }}
        吗？
      </view>

      <view class="dialog-btn-group">
        <button class="dialog-btn red" @click="confirmDel">确定删除</button>
        <button class="dialog-btn" @click="deleteClose">不了</button>
      </view>
    </view>
  </up-popup>
</template>

<script>
export default {
  options: {
    styleIsolation: "shared",
  },
}
</script>

<style lang="scss">
:deep(.u-dropdown__menu) {
  justify-content: flex-end !important;
  height: 60rpx !important;
  z-index: 0 !important;

  .u-dropdown__menu__item {
    .u-dropdown__menu__item__text {
      color: #577f49 !important;
    }
    flex: unset;
    width: 211rpx;
    height: 60rpx;
    line-height: 60rpx;
    padding: 0 23rpx;
    background: #ffffff;
    border-radius: 37rpx 37rpx 37rpx 37rpx;
    color: #21232c;
    font-size: 26rpx;
  }
}
</style>

<style lang="scss" scoped>
.filter {
  display: flex;
  justify-content: space-between;
  height: 60rpx;
  position: relative;

  .type {
    width: 276rpx;
    height: 60rpx;
    background: #e8ebef;
    border-radius: 55rpx 55rpx 55rpx 55rpx;
    font-weight: 400;
    font-size: 28rpx;
    color: #9ca3b5;
    padding: 6rpx;
    display: flex;
    justify-content: space-between;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
  }
  .text {
    width: 127rpx;
    height: 48rpx;
    text-align: center;
    line-height: 48rpx;
  }
  .checked {
    width: 127rpx;
    height: 48rpx;
    background: #ffffff;
    box-shadow: 0rpx 3rpx 6rpx 1rpx rgba(0, 0, 0, 0.02);
    border-radius: 55rpx 55rpx 55rpx 55rpx;
    line-height: 48rpx;
    text-align: center;
    color: #577f49;
  }
}

.list {
  margin-top: 30rpx;

  .task-item {
    background: #ffffff;
    box-shadow: 0rpx 6rpx 13rpx 1rpx rgba(0, 0, 0, 0.04);
    border-radius: 20rpx 20rpx 20rpx 20rpx;
    padding: 30rpx;
    display: flex;
    justify-content: space-between;
    gap: 45rpx;
    margin-bottom: 20rpx;

    .left {
      flex: 1;
      overflow: hidden;

      .content {
        height: 89rpx;
        line-height: 44rpx;
        margin-bottom: 30rpx;

        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        word-break: break-all;

        text-overflow: ellipsis;
        overflow: hidden;
      }

      .date {
        font-weight: 400;
        font-size: 26rpx;
        color: #bdc4ce;
        display: flex;
        align-items: center;

        .icon {
          width: 20rpx;
          height: 20rpx;
          margin-right: 12rpx;
        }
      }
    }

    .right {
      flex-shrink: 0;
      display: flex;
      flex-direction: column;
      justify-content: center;

      .btn {
        width: 121rpx;
        height: 61rpx;
        background: #577f49;
        border-radius: 31rpx 31rpx 31rpx 31rpx;
        font-weight: 500;
        font-size: 26rpx;
        color: #ffffff;
        line-height: 61rpx;
        text-align: center;
      }
    }

    .right2 {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      align-items: flex-end;

      .pub-btn {
        width: 121rpx;
        height: 61rpx;
        line-height: 61rpx;
        background: #577f49;
        border-radius: 31rpx;
        font-weight: 500;
        font-size: 26rpx;
        color: #ffffff;
      }

      .finish-status {
        font-weight: 500;
        font-size: 22rpx;
        color: #111111;

        .done {
          font-weight: 500;
          font-size: 32rpx;
          color: #f39c12;
        }
      }

      .more {
        width: 42rpx;
        height: 42rpx;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-right: 10rpx;

        image {
          width: 30rpx;
          height: 6rpx;
        }
      }
    }
  }

  .notify-item {
    background: #ffffff;
    box-shadow: 0rpx 6rpx 13rpx 1rpx rgba(0, 0, 0, 0.04);
    border-radius: 20rpx 20rpx 20rpx 20rpx;
    padding: 26rpx;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    margin-bottom: 20rpx;

    .top {
      display: flex;
      justify-content: space-between;
      gap: 34rpx;

      .title {
        font-weight: 500;
        font-size: 32rpx;
        color: #111111;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        text-overflow: ellipsis;
        overflow: hidden;
      }

      .pub-btn {
        width: 121rpx;
        height: 61rpx;
        line-height: 61rpx;
        background: #577f49;
        border-radius: 31rpx;
        font-weight: 500;
        font-size: 26rpx;
        color: #ffffff;
      }
    }

    .bottom {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 26rpx;

      .time {
        font-weight: 400;
        font-size: 26rpx;
        color: #bdc4ce;
        display: flex;
        align-items: center;

        image {
          width: 20rpx;
          height: 20rpx;
          margin-right: 12rpx;
        }
      }

      .more {
        width: 42rpx;
        height: 42rpx;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-right: 10rpx;

        image {
          width: 30rpx;
          height: 6rpx;
        }
      }
    }
  }
}

.popbox {
  width: 702rpx;
  margin: 0 auto;
  background: #ffffff !important;
  box-shadow: 0rpx 3rpx 26rpx 1rpx rgba(12, 39, 38, 0.04);
  border-radius: 20rpx 20rpx 20rpx 20rpx;
  text-align: center;

  .box {
    width: 688rpx;
    height: 110rpx;
    line-height: 110rpx;
  }
  .bottom {
    border-bottom: 1rpx solid #e6e6e6;
  }
}
</style>
