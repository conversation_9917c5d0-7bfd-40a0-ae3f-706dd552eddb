<script setup>
import { ref, watch, computed, nextTick } from "vue"
import {
  onLoad,
  onShow,
  onReachBottom,
  onPullDownRefresh,
} from "@dcloudio/uni-app"
import request from "@/utils/request"
import { checkPermissionAnd, checkPermissionOr, navTo } from "@/utils/utils"

import taskContent from "./components/taskContent.vue"
import releasesContent from "./components/releasesContent.vue"
import questionnaireContent from "./components/questionnaireContent.vue"
import { permissionConst } from "@/utils/permissionConst"

const taskContentRef = ref(null)
const questionnaireContentRef = ref(null)
const releasesContentRef = ref(null)

const currentTabIndex = ref(0)
const tabs = computed(() => [
  {
    title: "我的任务",
    type: "task",
    component: taskContentRef,
    show: 1,
  },
  {
    title: "调查问卷",
    type: "questionnaire",
    component: questionnaireContentRef,
    show: 1,
  },
  {
    title: "我的发布",
    type: "release",
    component: releasesContentRef,
    show: checkPermissionOr([
      permissionConst.mini_work_task_store.status,
      permissionConst.mini_inform_store.status,
    ]),
  },
])
const currentTab = computed(() => tabs.value[currentTabIndex.value])
const changeTab = (index) => {
  currentTabIndex.value = index
  if (currentTabIndex.value === 2) {
    if (!releasesContentRef.value.pagination().list.length) {
      releasesContentRef.value?.loadMore()
    }
  } else if (!currentTab.value?.component.value?.list.length) {
    loadMore()
  }
}

const loadMore = () => {
  currentTab.value?.component.value?.loadPage()
}

onLoad((options) => {
  const { tab } = options
  if (tab) {
    currentTabIndex.value = tabs.value.findIndex((item) => item.type === tab)
  }
  nextTick(() => {
    changeTab(currentTabIndex.value)
  })
})

onPullDownRefresh(() => {
  currentTab.value?.component.value?.refresh()
})
</script>

<template>
  <view>
    <navHeader
      title="日常办公"
      bg="/static/image/bg.png"
      :showLeft="true"
    ></navHeader>
    <view
      class="header"
      :style="{
        marginTop: checkPermissionOr([
          permissionConst.mini_work_task_store.status,
          permissionConst.mini_inform_store.status,
        ])
          ? '200rpx'
          : '80rpx',
      }"
    >
    </view>
    <view class="body">
      <view
        class="nav"
        v-if="
          checkPermissionOr([
            permissionConst.mini_work_task_store.status,
            permissionConst.mini_inform_store.status,
          ])
        "
      >
        <image
          v-if="
            checkPermissionOr([permissionConst.mini_work_task_store.status])
          "
          src="/static/image/task.png"
          mode=""
          @click="navTo('/pages/office/postTask')"
        ></image>
        <image
          v-if="checkPermissionOr([permissionConst.mini_inform_store.status])"
          src="/static/image/gonggao.png"
          mode=""
          @click="navTo('/pages/office/postNotice')"
        ></image>
      </view>
      <view class="title">
        <template v-for="(item, index) in tabs" :key="index">
          <view
            v-if="item.show"
            :class="currentTabIndex == index ? 'checked' : ''"
            @click="changeTab(index)"
          >
            <view class=""> {{ item.title }} </view>
            <view class="green" v-if="currentTabIndex == index"> </view>
          </view>
        </template>
      </view>

      <view class="page-content">
        <taskContent ref="taskContentRef" v-show="currentTabIndex == 0" />
        <questionnaireContent
          ref="questionnaireContentRef"
          v-show="currentTabIndex == 1"
        />
        <releasesContent
          ref="releasesContentRef"
          v-show="currentTabIndex == 2"
        />
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.checked {
  font-weight: bold;
  color: #39455b;
}

.body {
  .nav {
    padding: 0 12rpx;
    display: flex;
    justify-content: space-between;

    image {
      width: 329rpx;
      height: 120rpx;
    }
  }

  padding: 24rpx;

  width: 100%;

  .title {
    font-weight: 400;
    font-size: 30rpx;
    color: #39455b;

    text-align: center;
    font-style: normal;
    text-transform: none;
    display: flex;
    text-align: center;
    padding: 24rpx;
    margin-top: 40rpx;
    justify-content: space-evenly;

    .green {
      width: 25rpx;
      height: 0rpx;
      background: #ffffff;
      border-bottom: 6rpx solid #577f49;
      border-radius: 6rpx;
      margin: 10rpx auto;
    }
  }
}

.header {
  margin-top: 200rpx;
}

.footer {
  width: 750rpx;
  height: 238rpx;
  background: #ffffff;

  border-radius: 22rpx 22rpx 0rpx 0rpx;

  .btn {
    width: 630rpx;
    height: 100rpx;
    background: #577f49;
    box-shadow: 0rpx 20rpx 48rpx 1rpx rgba(87, 127, 73, 0.2);
    border-radius: 50rpx 50rpx 50rpx 50rpx;
    color: #fff;
    line-height: 100rpx;
    margin-top: 45rpx;
  }
}
</style>
