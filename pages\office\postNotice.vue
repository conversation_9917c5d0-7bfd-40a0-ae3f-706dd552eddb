<script setup>
import { ref, watch, computed } from "vue"
import { onLoad, onShow } from "@dcloudio/uni-app"
import request from "@/utils/request"
import { inputStyle } from "@/utils/constants.js"

const treeData = ref([])

const fileList = ref([])

const formDataRules = {}
const selectedDepartmentCodeList = ref([])
const formData = ref({})

const departmentPickerVisible = ref(false)
function openDepartmentPicker() {
  departmentPickerVisible.value = true
}
function closeDepartmentPicker() {
  departmentPickerVisible.value = false
}

const loadDepartmentTree = () => {
  request({
    url: "/mini/department_tree",
    method: "get",
  }).then((res) => {
    treeData.value = res.result
  })
}
const onDepartmentChange = (e) => {
  selectedDepartmentCodeList.value = e
  closeDepartmentPicker()
}
const selectedText = computed(() => {
  // 树形
  const selectedNodes = []
  const findSelected = (nodes) => {
    nodes.forEach((node) => {
      if (selectedDepartmentCodeList.value.includes(node.code)) {
        selectedNodes.push(node)
      }
      if (node.children) {
        findSelected(node.children)
      }
    })
  }
  findSelected(treeData.value)
  return selectedNodes.map((node) => node.name).join(",")
})

const post = (status) => {
  const data = {
    title: formData.value.title,
    content: formData.value.content,
    receive_department_code: selectedDepartmentCodeList.value,
    file_ids: fileList.value.map((item) => item.id),
    status,
  }

  request({
    url:
      pageType.value === "edit"
        ? `/mini/inform/${editId.value}`
        : "/mini/inform",
    method: pageType.value === "edit" ? "put" : "post",
    data,
  }).then((res) => {
    console.log(res)
    if (res.code === 200) {
      uni.showToast({
        title: status === "draft" ? "保存成功" : "发布成功",
        icon: "none",
        duration: 2000,
      })
      setTimeout(() => {
        uni.navigateBack()
      }, 2000)
    }
  })
}

const pageType = ref("add")
const editId = ref(null)
const loadDefaultData = () => {
  request({
    url: `/mini/inform/${editId.value}`,
  }).then((res) => {
    formData.value = res.result || {}
    fileList.value = res.result?.file_ids_attachments || []

    selectedDepartmentCodeList.value = res.result?.departments.map(
      (item) => item.code
    )
  })
}

onLoad((options) => {
  if (options.id) {
    pageType.value = "edit"
    editId.value = options.id
    loadDefaultData()
  }
  loadDepartmentTree()
})
</script>

<template>
  <view class="page">
    <view class="body">
      <up-form
        labelPosition="top"
        labelWidth="auto"
        :model="formData"
        :rules="formDataRules"
        ref="accountFormRef"
      >
        <up-form-item label="通知标题" prop="title">
          <up-input
            :customStyle="inputStyle"
            v-model="formData.title"
            border="none"
            placeholder="输入通知标题"
          ></up-input>
        </up-form-item>

        <up-form-item label="接收单位">
          <view @click="openDepartmentPicker">
            <up-input
              :customStyle="inputStyle"
              :modelValue="selectedText"
              border="none"
              readonly
              placeholder="请选择接收单位"
            ></up-input>
          </view>
        </up-form-item>
        <up-form-item label="通知详情" prop="content">
          <up-textarea
            class="textarea"
            v-model="formData.content"
            placeholder="请输入通知详情"
            :maxlength="-1"
          ></up-textarea>
        </up-form-item>
        <up-form-item label="材料上传">
          <uploadFiles v-model:fileList="fileList" />
        </up-form-item>
      </up-form>

      <view class="btn-group">
        <button
          class="btn draft"
          @click="post('draft')"
          v-if="!editId || formData.status === 'draft'"
        >
          保存草稿
        </button>
        <button class="btn submit" @click="post('published')">发布通知</button>
      </view>
    </view>
    <up-popup
      closeable
      :show="departmentPickerVisible"
      @close="closeDepartmentPicker"
      :round="10"
      @open="openDepartmentPicker"
      mode="bottom"
    >
      <departmentPicker
        :nodes="treeData"
        :selectedData="selectedDepartmentCodeList"
        @change="onDepartmentChange"
      />
    </up-popup>
  </view>
</template>

<style lang="scss" scoped>
.textarea {
  padding: 0 23rpx;
  width: 618rpx;
  height: 350rpx;
  background: #f7f8fa;
  border-radius: 20rpx 20rpx 20rpx 20rpx;
  border: 1rpx solid #ebebeb;
}
.page {
  padding: 24rpx;
  .body {
    width: 702rpx;
    height: 1646rpx;
    background: #ffffff;
    border-radius: 20rpx 20rpx 20rpx 20rpx;
    padding: 30rpx;
  }
}

.btn-group {
  display: flex;
  gap: 30rpx;
  margin-top: 50rpx;

  .btn {
    flex: 1;
    height: 100rpx;
    line-height: 100rpx;
    border: 1rpx solid #577f49;
    border-radius: 50rpx;

    &.draft {
      background: #ffffff;
      color: #577f49;
    }

    &.submit {
      background: #577f49;
      color: #ffffff;
    }
  }
}
</style>
