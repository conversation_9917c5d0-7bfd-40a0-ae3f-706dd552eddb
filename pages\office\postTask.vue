<script setup>
import { ref, watch, computed } from "vue"
import { onLoad, onShow } from "@dcloudio/uni-app"
import request from "@/utils/request"
import { inputStyle } from "@/utils/constants.js"

const treeData = ref([])

const fileList = ref([])
const selectedDepartmentCodeList = ref([])
const formDataRules = {}
const formData = ref({})

const departmentPickerVisible = ref(false)
function openDepartmentPicker() {
  departmentPickerVisible.value = true
}
function closeDepartmentPicker() {
  departmentPickerVisible.value = false
}

const loadDepartmentTree = () => {
  request({
    url: "/mini/department_tree",
    method: "get",
  }).then((res) => {
    treeData.value = res.result
  })
}
const onDepartmentChange = (e) => {
  selectedDepartmentCodeList.value = e
  closeDepartmentPicker()
}
const selectedText = computed(() => {
  // 树形
  const selectedNodes = []
  const findSelected = (nodes) => {
    nodes.forEach((node) => {
      if (selectedDepartmentCodeList.value.includes(node.code)) {
        selectedNodes.push(node)
      }
      if (node.children) {
        findSelected(node.children)
      }
    })
  }
  findSelected(treeData.value)
  return selectedNodes.map((node) => node.name).join(",")
})

const selectedTaskType = ref({})
const taskTypePickerVisible = ref(false)
const showTypePicker = () => {
  taskTypePickerVisible.value = true
}
const closeTypePicker = () => {
  taskTypePickerVisible.value = false
}
const changeWorkTaskType = (e) => {
  console.log(e)
  selectedTaskType.value = e
  formData.value.type_id = e.id
  closeTypePicker()
}
const workTaskTypeList = ref([])
const loadWorkTaskTypeList = () => {
  request({
    url: "/mini/work_task_type",
    method: "get",
    data: {
      page_size: 999,
    },
  }).then((res) => {
    workTaskTypeList.value = res.result?.data || []
  })
}

const onUploadChange = (fileList) => {
  console.log("onUploadChange", fileList)
}

const post = (status) => {
  const data = {
    type_id: formData.value.type_id,
    title: formData.value.title,
    content: formData.value.content,
    receive_department_code: selectedDepartmentCodeList.value,
    file_ids: fileList.value.map((item) => item.id),
    status,
  }
  request({
    url:
      pageType.value === "edit"
        ? `/mini/work_task/${editId.value}`
        : "/mini/work_task",
    method: pageType.value === "edit" ? "put" : "post",
    data,
  }).then((res) => {
    console.log(res)
    if (res.code === 200) {
      uni.showToast({
        title: status === "draft" ? "保存成功" : "发布成功",
        icon: "none",
        duration: 2000,
      })
      setTimeout(() => {
        uni.navigateBack()
      }, 2000)
    }
  })
}

const pageType = ref("add")
const editId = ref(null)
const loadDefaultData = () => {
  request({
    url: `/mini/work_task/${editId.value}`,
  }).then((res) => {
    formData.value = res.result || {}
    fileList.value = res.result?.file_ids_attachments || []

    selectedTaskType.value = workTaskTypeList.value.find(
      (item) => item.id === res.result.type_id
    )
    selectedDepartmentCodeList.value = res.result.departments.map(
      (item) => item.code
    )
  })
}

onLoad((options) => {
  loadDepartmentTree()
  loadWorkTaskTypeList()
  if (options.id) {
    pageType.value = "edit"
    editId.value = options.id
    loadDefaultData()
  }
})
</script>

<template>
  <view class="page">
    <view class="body">
      <up-form
        labelPosition="top"
        labelWidth="auto"
        :model="formData"
        :rules="formDataRules"
        ref="accountFormRef"
      >
        <up-form-item label="任务标题" prop="title">
          <up-input
            :customStyle="inputStyle"
            v-model="formData.title"
            border="none"
            placeholder="请输入任务标题"
          ></up-input>
        </up-form-item>
        <up-form-item label="任务类型" prop="type_id" @click="showTypePicker">
          <up-input
            :customStyle="inputStyle"
            :modelValue="selectedTaskType?.name"
            disabledColor="#ffffff"
            placeholder="请选择任务类型"
            readonly
            border="none"
          >
          </up-input>
          <template #right style="margin-right: 100rpx">
            <up-icon name="arrow-right"></up-icon>
          </template>
        </up-form-item>
        <up-form-item label="接收单位" @click="openDepartmentPicker">
          <up-input
            :customStyle="inputStyle"
            :modelValue="selectedText"
            border="none"
            readonly
            placeholder="请选择接收单位"
          ></up-input>
          <template #right style="margin-right: 100rpx">
            <up-icon name="arrow-right"></up-icon>
          </template>
        </up-form-item>
        <up-form-item label="任务详情" prop="content">
          <up-textarea
            class="textarea"
            v-model="formData.content"
            placeholder="请输入任务详情"
            :maxlength="-1"
          ></up-textarea>
        </up-form-item>
        <up-form-item label="材料上传">
          <uploadFiles v-model:fileList="fileList" @change="onUploadChange" />
        </up-form-item>
      </up-form>

      <view class="btn-group">
        <button
          class="btn draft"
          @click="post('draft')"
          v-if="!editId || formData.status === 'draft'"
        >
          保存草稿
        </button>
        <button class="btn submit" @click="post('published')">发布任务</button>
      </view>
    </view>
    <up-popup
      closeable
      :show="departmentPickerVisible"
      @close="closeDepartmentPicker"
      :round="10"
      @open="openDepartmentPicker"
      mode="bottom"
    >
      <departmentPicker
        :nodes="treeData"
        :selectedData="selectedDepartmentCodeList"
        @change="onDepartmentChange"
      />
    </up-popup>
  </view>

  <up-action-sheet
    :show="taskTypePickerVisible"
    :actions="workTaskTypeList"
    title="请选择任务类型"
    @close="closeTypePicker"
    @select="changeWorkTaskType"
  >
  </up-action-sheet>
</template>

<style lang="scss" scoped>
.page {
  padding: 24rpx;
  .body {
    width: 702rpx;
    background: #ffffff;
    border-radius: 20rpx 20rpx 20rpx 20rpx;
    padding: 30rpx;
  }
}

:deep(.item__body__right__content__icon) {
  transform: translateX(-70rpx);
  position: absolute;
  right: 0rpx;
}

.btn-group {
  display: flex;
  gap: 30rpx;
  margin-top: 50rpx;

  .btn {
    flex: 1;
    height: 100rpx;
    line-height: 100rpx;
    border: 1rpx solid #577f49;
    border-radius: 50rpx;

    &.draft {
      background: #ffffff;
      color: #577f49;
    }

    &.submit {
      background: #577f49;
      color: #ffffff;
    }
  }
}
</style>
