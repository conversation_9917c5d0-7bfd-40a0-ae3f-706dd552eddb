<script setup>
import { ref, watch, computed } from "vue"
import { onLoad, onShow } from "@dcloudio/uni-app"
import request from "@/utils/request"

const treeData = ref([])

const fileList = ref([])
const formData = ref({})

const taskId = ref(null)

const submit = () => {
  const data = {
    work_task_id: taskId.value,
    content: formData.value.content,
    file_ids: fileList.value.map((item) => item.id),
  }

  request({
    url:
      pageType.value === "edit"
        ? `/mini/work_task_report/${editId.value}`
        : "/mini/work_task_report",
    method: pageType.value === "edit" ? "put" : "post",
    data,
  }).then((res) => {
    console.log(res)
    if (res.code === 200) {
      uni.showToast({
        title: "提交成功",
        icon: "none",
        duration: 2000,
      })
      setTimeout(() => {
        uni.navigateBack()
      }, 2000)
    }
  })
}

const pageType = ref("add")
const editId = ref(null)
const loadDefaultData = () => {
  request({
    url: `/mini/work_task_report/${editId.value}`,
  }).then((res) => {
    formData.value = res.result || {}
    fileList.value = res.result?.file_ids_attachments || []
  })
}

const loadDepartmentTree = () => {
  request({
    url: "/mini/department_tree?p_code=c1101",
    method: "get",
  }).then((res) => {
    treeData.value = res.result
  })
}

onLoad((options) => {
  if (options.id) {
    pageType.value = "edit"
    editId.value = options.id
    loadDefaultData()
  }
  if (options.taskId) {
    taskId.value = options.taskId
  }
  loadDepartmentTree()
})
</script>

<template>
  <view class="page">
    <view class="body">
      <view class="box">
        <view class="field-item">
          <view class="title">
            <text style="color: #fb5854">*</text> 上报详情
          </view>
          <up-textarea
            class="text-area"
            v-model="formData.content"
            placeholder="请输入上报详情"
            :maxlength="-1"
          ></up-textarea>
        </view>

        <view class="field-item">
          <view class="title"> 材料上传 </view>
          <uploadFiles v-model:fileList="fileList" />
        </view>

        <view class="btn" @click="submit"> 提交 </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.page {
  height: 100vh;
  width: 750rpx;
  background-color: #f7f8fa;

  .body {
    position: relative;
    padding: 24rpx;

    .box {
      min-height: 1075rpx;
      background: #ffffff;
      border-radius: 20rpx 20rpx 20rpx 20rpx;
      padding: 30rpx;

      .field-item {
        margin-bottom: 40rpx;
      }

      .title {
        font-weight: bold;
        font-size: 32rpx;
        color: #303445;
        margin-bottom: 11rpx;
      }
      .text-area {
        width: 618rpx;
        height: 350rpx;
        background: #f7f8fa;
        border-radius: 20rpx 20rpx 20rpx 20rpx;
        border: 1rpx solid #ebebeb;
        margin: 0 auto;
        margin-bottom: 40rpx;
        padding: 22rpx 38rpx;
        box-sizing: border-box;
      }

      .btn {
        width: 630rpx;
        height: 100rpx;
        background: #577f49;
        box-shadow: 0rpx 20rpx 48rpx 1rpx rgba(87, 127, 73, 0.2);
        border-radius: 50rpx 50rpx 50rpx 50rpx;
        margin: 0 auto;
        color: #fff;
        text-align: center;
        line-height: 100rpx;
        margin-top: 100rpx;
      }
    }
  }
}

.nav {
  position: absolute;
  top: 0;
  left: 0;
  width: 750rpx;
  height: 478rpx;
}
</style>
