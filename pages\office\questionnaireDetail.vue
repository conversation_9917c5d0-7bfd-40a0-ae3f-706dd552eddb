<script setup>
import { ref, watch, computed } from "vue"
import { onLoad, onShow } from "@dcloudio/uni-app"
import request from "@/utils/request"
import dayjs from "dayjs"
import { navTo } from "@/utils/utils"
import { inputStyle } from "@/utils/constants.js"

const role = ref("worker")

const surveyId = ref(0)

const forms = ref([])
const name = ref("")
const description = ref(
  "参加测试的人员请务必诚实、独立地回答问题，只有如此，才能得到有效的结果。"
)
const startAt = ref("")
const endAt = ref("")

const loadData = () => {
  request({
    url: `/mini/survey/${surveyId.value}`,
    method: "get",
  }).then((res) => {
    if (res.code === 200) {
      name.value = res.result.name
      if (res.result.description) {
        description.value = res.result.description
      }
      startAt.value = dayjs(res.result.start_at).format("YYYY.MM.DD")
      endAt.value = dayjs(res.result.end_at).format("YYYY.MM.DD")
      forms.value = res.result.forms
    } else {
      navTo(
        `/pages/result/questionnaire`,
        {
          role: role.value,
          status: "empty",
        },
        "redirectTo"
      )
    }
  })
}

const onRadioChange = (formItem, e) => {
  formItem.value = e.detail.value
}

const onCheckBoxChange = (formItem, e) => {
  formItem.value = e.detail.value
}

const submit = () => {
  for (let i = 0; i < forms.value.length; i++) {
    if (
      forms.value[i].required &&
      forms.value[i].type !== 2 &&
      !forms.value[i].value
    ) {
      uni.showToast({
        title: "请填写完整",
        icon: "none",
        duration: 2000,
      })
      return
    }
  }

  request({
    url: `/mini/survey/${surveyId.value}/submit`,
    method: "post",
    data: {
      forms: {
        ...forms.value,
      },
    },
  }).then((res) => {
    if (res.code === 200) {
      navTo(
        `/pages/result/questionnaire`,
        {
          role: role.value,
          status: "success",
        },
        "redirectTo"
      )
    }
  })
}

onLoad((options) => {
  console.log("onLoad", options)

  if (options.scene) {
    const scene = decodeURIComponent(options.scene)
    const params = scene.split("&")
    const query = {}
    params.forEach((item) => {
      const arr = item.split("=")
      query[arr[0]] = arr[1]
    })
    options = query
  }

  surveyId.value = options.surveyId
  if (options.role) {
    role.value = options.role
  }
  loadData()
})
</script>

<template>
  <navHeader
    title="调查问卷"
    bg="/static/image/questionnaireDetail.png"
    :imgHeight="557"
    :showLeft="role === 'worker'"
    color="#fff"
  ></navHeader>
  <view class="page">
    <view class="title-box">
      <view class="title">
        {{ name }}
      </view>
      <view class="duration">
        <image src="/static/image/clock.svg" mode="" class="icon"></image>
        <text>{{ startAt }} - {{ endAt }}</text>
      </view>
    </view>

    <view class="question-box">
      <view class="tips-box">
        <view class="tips-title"> - 温馨提示 - </view>
        <view class="tips-content">
          {{ description }}
        </view>
      </view>

      <view
        class="question-item"
        :class="{ required: formItem.required }"
        v-for="(formItem, index) in forms"
        :key="index"
      >
        <view class="question-title">
          <text class="no">{{ index + 1 }}.</text>
          {{ formItem.label }}
        </view>
        <view class="question-content">
          <template v-if="formItem.type === 0">
            <up-input
              v-model="formItem.value"
              :placeholder="formItem.placeholder"
              :customStyle="inputStyle"
              placeholderClass="placeholder"
            />
          </template>
          <template v-else-if="formItem.type === 1">
            <up-textarea
              v-model="formItem.value"
              :placeholder="formItem.placeholder"
              :maxlength="-1"
            />
          </template>
          <template v-else-if="formItem.type === 2">
            <view>{{ formItem.placeholder }}</view>
          </template>
          <template v-else-if="formItem.type === 5">
            <radio-group @change="onRadioChange(formItem, $event)">
              <label
                v-for="(option, index) in formItem.options"
                :key="index"
                class="radio"
              >
                <image
                  :src="
                    option === formItem.value
                      ? '/static/image/radio-checked.svg'
                      : '/static/image/radio-unchecked.svg'
                  "
                  mode=""
                  class="radio-icon"
                ></image>
                <radio :value="option" v-show="false"></radio>
                <text>{{ option }}</text>
              </label>
            </radio-group>
          </template>
          <template v-else-if="formItem.type === 6">
            <checkbox-group
              @change="onCheckBoxChange(formItem, $event)"
              class="checkbox-group"
            >
              <!-- <checkbox
                v-for="(option, index) in formItem.options"
                :key="index"
                :value="option"
              >
                {{ option }}
              </checkbox> -->

              <label
                v-for="(option, index) in formItem.options"
                :key="index"
                class="checkbox"
              >
                <image
                  :src="
                    formItem.value?.includes(option)
                      ? '/static/image/checkbox-checked.svg'
                      : '/static/image/checkbox-unchecked.svg'
                  "
                  mode=""
                  class="checkbox-icon"
                ></image>
                <checkbox :value="option" v-show="false"></checkbox>
                <text>{{ option }}</text>
              </label>
            </checkbox-group>
          </template>
        </view>
      </view>

      <!-- <template v-if="role === 'recruit'">
        <view class="question-item">
          <view class="question-title">
            <text class="no"> {{ forms.length + 1 }}. </text>
            所在区
          </view>
          <view class="question-content">
            <up-input
              v-model="district"
              placeholder="请输入所在区"
              :customStyle="inputStyle"
              placeholderClass="placeholder"
            />
          </view>
        </view>
        <view class="question-item">
          <view class="question-title">
            <text class="no"> {{ forms.length + 2 }}. </text>
            学校名称
          </view>
          <view class="question-content">
            <up-input
              v-model="school"
              placeholder="请输入学校名称"
              :customStyle="inputStyle"
              placeholderClass="placeholder"
            />
          </view>
        </view>
      </template> -->
    </view>

    <view class="btn-wrapper">
      <button class="submit" @click="submit">提交问卷</button>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.page {
  padding-top: 220rpx;
  padding-bottom: 96rpx;

  .title-box {
    padding: 0 60rpx;
    margin-bottom: 42rpx;

    .title {
      font-weight: bold;
      font-size: 42rpx;
      color: #ffffff;
      text-align: center;
      margin-bottom: 24rpx;
    }
    .duration {
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 26rpx;
      .icon {
        width: 20rpx;
        height: 20rpx;
        margin-right: 20rpx;
      }
      text {
        color: rgba(255, 255, 255, 0.6);
      }
    }
  }

  .tips-box {
    padding: 36rpx 40rpx;
    background: #ffffff;
    border-radius: 24rpx 24rpx 24rpx 24rpx;
    border: 2rpx solid #ffffff;
    margin-bottom: 24rpx;

    .tips-title {
      font-size: 28rpx;
      color: #8c9198;
      // letter-spacing: 50px;
      text-align: center;
      margin-bottom: 16rpx;
    }

    .tips-content {
      color: #3d444d;
      font-size: 28rpx;
    }
  }

  .question-box {
    padding: 0 24rpx;
    .question-item {
      margin-bottom: 24rpx;
      background: #ffffff;
      box-shadow: 0rpx 3rpx 20rpx 1rpx rgba(0, 0, 0, 0.03);
      border-radius: 20rpx 20rpx 20rpx 20rpx;

      .question-title {
        font-size: 32rpx;
        color: #21232c;
        font-weight: bold;
        border-bottom: 1rpx solid #e5e5e5;
        padding: 28rpx;
        display: flex;

        .no {
          flex-shrink: 0;
        }
      }
      .question-content {
        color: #21232c;
        font-size: 32rpx;
        padding: 48rpx 66rpx;

        radio-group,
        checkbox-group {
          display: flex;
          flex-direction: column;
          gap: 40rpx;
        }

        .radio {
          display: flex;
          align-items: center;
          gap: 20rpx;
          font-size: 32rpx;
          color: #21232c;
          .radio-icon {
            width: 36rpx;
            height: 36rpx;
          }
        }

        .checkbox {
          display: flex;
          align-items: center;
          gap: 20rpx;
          font-size: 32rpx;
          color: #21232c;
          .checkbox-icon {
            width: 40rpx;
            height: 40rpx;
          }
        }
      }
    }

    .required {
      .question-title {
        &::before {
          content: "*";
          color: #ff4d4f;
          position: relative;
          left: -6rpx;
        }
      }
    }
  }
  .btn-wrapper {
    display: flex;
    justify-content: center;
    margin-top: 50rpx;
    .submit {
      width: 702rpx;
      height: 100rpx;
      line-height: 100rpx;
      font-size: 34rpx;
      color: #ffffff;
      background: #577f49;
      box-shadow: 0rpx 20rpx 48rpx 1rpx rgba(87, 127, 73, 0.2);
      border-radius: 50rpx 50rpx 50rpx 50rpx;
    }
  }
}
</style>
