<script setup>
import { ref, watch, computed } from "vue"
import { onLoad, onShow, onReachBottom } from "@dcloudio/uni-app"
import dayjs from "dayjs"
import request from "@/utils/request"
import { navTo } from "@/utils/utils"
import { useUserStore } from "@/store/user"

const userStore = useUserStore()
const userInfo = computed(() => userStore.userInfo)

const taskId = ref(null)
const scope = ref("can_handle")

const activeTabIndex = ref(0)

const goEdit = () => {
  navTo(`/pages/office/postTask?id=${taskId.value}`)
}

const deleteDialogVisible = ref(false)
function deleteOpen() {
  deleteDialogVisible.value = true
}
function deleteClose() {
  deleteDialogVisible.value = false
}

const confirmDel = () => {
  request({
    url: `/mini/work_task/${taskId.value}`,
    method: "delete",
  }).then(() => {
    uni.showToast({
      title: "删除成功",
      icon: "success",
    })
    uni.navigateBack()
  })
}

const detailData = ref({})
const loadData = () => {
  request({
    url: `/mini/work_task/${taskId.value}`,
  }).then((res) => {
    detailData.value = res.result
  })
}

const reportList = ref([])
const reportPagination = ref({
  page: 0,
  page_size: 15,
  total: 15,
})
const loadReportList = () => {
  if (reportList.value.length >= reportPagination.value.total) {
    return
  } else {
    reportPagination.value.page++
  }

  let data = {
    work_task_id: taskId.value,
    page: reportPagination.value.page,
    page_size: reportPagination.value.page_size,
  }

  if (scope.value === "can_handle") {
    data.scope = "my_department"
  } else if (scope.value === "create") {
    data.scope = "can_see"
  }

  request({
    url: `/mini/work_task_report`,
    data,
  }).then((res) => {
    reportPagination.value.total = res.result?.total || 0
    reportList.value.push(...(res.result?.data || []))
  })
}

const optReportItem = ref(null)
const onClickReport = (item) => {
  optReportItem.value = item
  openActionsPopup()
}
const actionsPopupVisible = ref(false)
function openActionsPopup() {
  actionsPopupVisible.value = true
}
function closeActionsPopup() {
  actionsPopupVisible.value = false
}

const goReportEdit = () => {
  navTo(
    `/pages/office/postWork?id=${optReportItem.value.id}&taskId=${taskId.value}`
  )
  closeActionsPopup()
}
const deleteReportDialogVisible = ref(false)
const deleteReportOpen = () => {
  closeActionsPopup()
  deleteReportDialogVisible.value = true
}
const deleteReportClose = () => {
  deleteReportDialogVisible.value = false
}
const confirmDelReport = () => {
  request({
    url: `/mini/work_task_report/${optReportItem.value.id}`,
    method: "delete",
  }).then(() => {
    uni.showToast({
      title: "删除成功",
      icon: "success",
    })
    reportList.value.splice(
      reportList.value.findIndex((item) => item.id === optReportItem.value.id),
      1
    )
    deleteReportClose()
  })
}

onLoad((options) => {
  console.log(options)
  if (options.scope) {
    scope.value = options.scope
  }
  if (options.taskId) {
    taskId.value = parseInt(options.taskId)
  }
})

onShow(() => {
  if (taskId.value) {
    loadData()

    reportList.value = []
    reportPagination.value = {
      page: 0,
      page_size: 15,
      total: 15,
    }
    loadReportList()
  }
})

onReachBottom(() => {
  loadReportList()
})
</script>

<template>
  <view class="page">
    <view class="section">
      <view class="title">
        {{ detailData.title }}
      </view>

      <view class="info">
        <view class="info-item">
          <image src="/static/image/time.png" mode="" class="size"></image>
          发布时间：{{
            dayjs(detailData.submitted_at).format("YYYY-MM-DD HH:mm")
          }}
        </view>
        <view class="info-item" v-if="detailData.type">
          <image src="/static/image/task-type.svg" class="size" mode=""></image>
          任务类型：{{ detailData.type?.name }}
        </view>
        <view class="info-item">
          <image src="/static/image/task-user.svg" class="size" mode=""></image>
          发布者：{{
            detailData.user_name === "city" ? "北京市" : detailData.user_name
          }}
        </view>
      </view>
    </view>

    <view class="section">
      <view class="tabs">
        <view
          class="tab-item"
          :class="{ active: activeTabIndex === 0 }"
          @click="activeTabIndex = 0"
          >任务详情</view
        >
        <view
          class="tab-item"
          :class="{ active: activeTabIndex === 1 }"
          @click="activeTabIndex = 1"
          >接收单位</view
        >
      </view>
      <view class="content">
        <view class="detail-wrapper" v-if="activeTabIndex === 0">
          <mp-html :content="detailData.content"></mp-html>
          <view style="margin-top: 20rpx">
            <fileList :fileList="detailData.file_ids_attachments" />
          </view>
        </view>
        <view class="departments-wrapper" v-if="activeTabIndex === 1">
          <view class="departments">
            <view
              class="department"
              v-for="item in detailData.receive_departments"
            >
              {{ item.tree_name }}
            </view>
          </view>
        </view>
      </view>
    </view>

    <view class="section">
      <view class="title">
        <view> 上报详情 </view>

        <view class="finish-status" v-if="scope === 'create'">
          <text class="done">{{ detailData.report_department_count }}</text
          >/{{ detailData.departments_count }}
        </view>
      </view>
      <view class="content">
        <view v-for="item in reportList" :key="item.id" class="report-item">
          <view class="report-title" v-if="scope === 'create'">
            {{ item.district_name }} {{ item.department_name }}
          </view>
          <view class="report-time">
            <text>
              {{ dayjs(item.created_at).format("YYYY-MM-DD HH:mm") }} 上报
            </text>
            <view
              class="more"
              v-if="scope === 'can_handle'"
              @click="onClickReport(item)"
            >
              <image src="/static/image/more.png" />
            </view>
          </view>
          <view class="report-content">
            <rich-text :nodes="item.content"></rich-text>
          </view>
          <view>
            <fileList :fileList="item.file_ids_attachments" />
          </view>
        </view>
        <view class="empty" v-if="!reportList.length"> 暂无内容 </view>
      </view>
    </view>

    <view style="height: 220rpx"></view>

    <view class="footer">
      <template v-if="scope === 'can_handle'">
        <button
          class="btn"
          @click="navTo('/pages/office/postWork', { taskId: taskId })"
        >
          上报
        </button>
      </template>
      <template v-if="scope === 'create'">
        <button class="delete" @click="deleteOpen">删除</button>
        <button class="edit" @click="goEdit">修改</button>
      </template>
    </view>
  </view>

  <up-popup
    mode="bottom"
    :show="actionsPopupVisible"
    @close="closeActionsPopup"
  >
    <view>
      <view class="popbox">
        <view class="box bottom" @click="goReportEdit"> 修改 </view>
        <view class="box" @click="deleteReportOpen"> 删除 </view>
      </view>
      <view class="popbox" style="margin-top: 24rpx">
        <view class="box" @click="closeActionsPopup"> 取消 </view>
      </view>
    </view>
  </up-popup>

  <up-popup
    mode="center"
    :safeAreaInsetBottom="false"
    :show="deleteDialogVisible"
    :round="10"
    closeable
    @close="deleteClose"
    @open="deleteOpen"
  >
    <view class="dialog">
      <view class="dialog-title"> 确定要删除此任务吗？ </view>

      <view class="dialog-btn-group">
        <button class="dialog-btn red" @click="confirmDel">确定删除</button>
        <button class="dialog-btn" @click="deleteClose">不了</button>
      </view>
    </view>
  </up-popup>

  <up-popup
    mode="center"
    :safeAreaInsetBottom="false"
    :show="deleteReportDialogVisible"
    :round="10"
    closeable
    @close="deleteReportClose"
    @open="deleteReportOpen"
  >
    <view class="dialog">
      <view class="dialog-title"> 确定要删除此上报吗？ </view>

      <view class="dialog-btn-group">
        <button class="dialog-btn red" @click="confirmDelReport">
          确定删除
        </button>
        <button class="dialog-btn" @click="deleteReportClose">不了</button>
      </view>
    </view>
  </up-popup>
</template>

<style lang="scss" scoped>
.footer {
  width: 750rpx;
  padding: 40rpx 60rpx;
  padding-bottom: calc(40rpx + env(safe-area-inset-bottom) / 2);
  background: #ffffff;
  box-shadow: 0rpx 3rpx 60rpx 1rpx rgba(0, 0, 0, 0.16);
  border-radius: 22rpx 22rpx 0rpx 0rpx;
  position: fixed;
  z-index: 1;
  bottom: 0rpx;
  left: 0rpx;

  display: flex;
  justify-content: center;
  gap: 60rpx;

  button {
    width: 630rpx;
    height: 100rpx;
    line-height: 100rpx;
    border-radius: 50rpx 50rpx 50rpx 50rpx;
    line-height: 100rpx;
    text-align: center;
    margin: 0 auto;
    background: #ffffff;

    &.delete {
      border: 1rpx solid #e1e1e1;
      color: #ea2b2b;
    }

    &.edit {
      border: 1rpx solid #e1e1e1;
      color: #21232c;
    }

    &.btn {
      background: #577f49;
      color: #ffffff;
      box-shadow: 0rpx 20rpx 48rpx 1rpx rgba(87, 127, 73, 0.2);
    }
  }
}

.content {
  font-weight: 400;
  font-size: 30rpx;
  color: #000000;

  .empty {
    height: 200rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #9ca3b5;
    font-size: 30rpx;
  }
}

.section {
  padding: 37rpx 60rpx;
  background: #ffffff;
  border-radius: 0rpx 0rpx 0rpx 0rpx;
  border: 1rpx solid #ffffff;
  margin-bottom: 20rpx;

  .title {
    margin-bottom: 26rpx;
    font-weight: bold;
    font-size: 30rpx;
    color: #303445;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}

.tabs {
  display: flex;
  gap: 55rpx;
  margin-bottom: 30rpx;

  .tab-item {
    font-weight: 500;
    font-size: 30rpx;
    color: #8c9198;
    padding-bottom: 18rpx;
    position: relative;

    &.active {
      font-weight: bold;
      color: #303445;

      &::after {
        content: "";
        position: absolute;
        bottom: 0rpx;
        left: 50%;
        transform: translateX(-50%);
        width: 24rpx;
        height: 6rpx;
        background: #577f49;
        border-radius: 3rpx;
      }
    }
  }
}

.departments-wrapper {
  max-height: 500rpx;
  overflow-y: auto;

  .department {
    margin-bottom: 8rpx;
    font-weight: 400;
    font-size: 30rpx;
    color: #000000;
  }
}

.info-item {
  margin-bottom: 20rpx;
  color: #bdc4ce;
  font-size: 26rpx;
  display: flex;
  align-items: center;
  font-weight: 500;

  image {
    width: 26rpx;
    height: 26rpx;
    margin-right: 10rpx;
  }
}

.size {
  width: 26rpx;
  height: 26rpx;
}

.report-item {
  padding-left: 30rpx;
  padding-bottom: 60rpx;
  position: relative;

  &::before {
    content: "";
    position: absolute;
    top: 14rpx;
    left: 0rpx;
    width: 12rpx;
    height: 12rpx;
    background: #577f49;
    border-radius: 50%;
    z-index: 1;
  }

  &::after {
    content: "";
    position: absolute;
    top: 14rpx;
    left: 5rpx;
    width: 0rpx;
    height: calc(100% - 14rpx);
    border-left: 1rpx dashed #bdc4ce;
    z-index: 0;
  }

  .report-title {
    font-weight: bold;
    font-size: 30rpx;
    color: #21232c;
    margin-bottom: 12rpx;
  }

  .report-time {
    font-weight: 500;
    font-size: 26rpx;
    color: #8e9bae;
    margin-bottom: 10rpx;

    display: flex;
    align-items: center;
    justify-content: space-between;

    .more {
      width: 42rpx;
      height: 42rpx;
      display: flex;
      justify-content: center;
      align-items: center;

      image {
        width: 30rpx;
        height: 6rpx;
      }
    }
  }

  .report-content {
    font-size: 30rpx;
    color: #000000;
    margin-bottom: 10rpx;
  }
}

.finish-status {
  font-weight: 500;
  font-size: 22rpx;
  color: #111111;

  .done {
    font-weight: 500;
    font-size: 32rpx;
    color: #f39c12;
  }
}

.popbox {
  width: 702rpx;
  margin: 0 auto;
  background: #ffffff !important;
  box-shadow: 0rpx 3rpx 26rpx 1rpx rgba(12, 39, 38, 0.04);
  border-radius: 20rpx 20rpx 20rpx 20rpx;
  text-align: center;

  .box {
    width: 688rpx;
    height: 110rpx;
    line-height: 110rpx;
  }
  .bottom {
    border-bottom: 1rpx solid #e6e6e6;
  }
}
</style>
