<script setup>
import { ref, watch, computed } from "vue"
import { onLoad, onShow } from "@dcloudio/uni-app"
import request from "@/utils/request"

const articleId = ref(0)
const title = ref("")
const content = ref("")
const datetime = ref("")
const categories = ref([])

const renderString = (html) => {
  content.value = html.replace(
    /<p>/g,
    `<p style="margin-bottom: 16px;">&emsp;&emsp;`
  )

  content.value = html.replace(
    /<img /g,
    '<img style="max-width:100%;height:auto;" '
  )
}

const loadData = () => {
  request({
    url: `/mini/article/${articleId.value}`,
    method: "get",
  }).then((res) => {
    if (res.code === 200) {
      console.log("res", res)
      title.value = res.result.title
      renderString(res.result?.content || "")
      datetime.value = res.result.datetime
      categories.value = res.result.categories
      isCollected.value = res.result.is_collect
    }
  })
}

const isCollected = ref(false)
const collect = () => {
  request({
    url: `/mini/collect`,
    method: "post",
    data: {
      collect_type: "article",
      collect_id: articleId.value,
    },
  }).then((res) => {
    if (res.code === 200) {
      isCollected.value = !isCollected.value
    }
  })
}

onLoad((options) => {
  console.log("onLoad", options)
  articleId.value = options.articleId
  loadData()
})
</script>

<template>
  <view class="page">
    <view class="title"> {{ title }} </view>
    <view class="text">
      <!-- #类型 -->
      <text
        style="margin-right: 10rpx"
        v-for="category in categories"
        :key="category.id"
      >
        #{{ category.name }}
      </text>
    </view>
    <view class="body">
      <mp-html
        :content="content"
        :tag-style="{
          p: 'margin-bottom: 42rpx; font-size: 34rpx; color: #111111;',
        }"
      ></mp-html>

      <view class="favorite" @click="collect">
        <image
          :src="
            isCollected
              ? '/static/image/star-active.svg'
              : '/static/image/star.svg'
          "
          class="star"
        />
        <text>
          {{ isCollected ? "已收藏" : "收藏" }}
        </text>
      </view>
    </view>
  </view>
</template>

<style>
page {
  background: #ffffff;
}
</style>

<style lang="scss" scoped>
.page {
  padding: 50rpx;
}

.body {
  margin-top: 60rpx;
  padding-bottom: 300rpx;

  .favorite {
    position: fixed;
    display: flex;
    align-items: center;
    width: 285rpx;
    height: 100rpx;
    background: #ffffff;
    box-shadow: 0rpx 20rpx 48rpx 1rpx rgba(33, 35, 44, 0.2);
    border-radius: 50rpx 50rpx 50rpx 50rpx;
    border: 1rpx solid #e1e1e1;
    bottom: 140rpx;
    left: 50%;
    transform: translateX(-50%);

    padding: 0 55rpx;

    .star {
      width: 40rpx;
      height: 40rpx;
    }

    text {
      width: 150rpx;
      text-align: center;
    }
  }
}
.title {
  font-weight: bold;
  font-size: 42rpx;
  color: #111111;

  text-align: left;
  font-style: normal;
  text-transform: none;
  margin-bottom: 24rpx;
}
.text {
  font-weight: 500;
  font-size: 26rpx;
  color: #bdc4ce;
  text-align: left;
  font-style: normal;
  text-transform: none;
}
</style>
