<script setup>
import { ref, watch, computed } from "vue"
import {
  onLoad,
  onShow,
  onPullDownRefresh,
  onReachBottom,
} from "@dcloudio/uni-app"
import request from "@/utils/request"
import { useCommonStore } from "@/store/common"
import { usePagination } from "@/utils/hooks"
import { navTo } from "@/utils/utils"

const commonStore = useCommonStore()

const policyCategoryId = computed(() => commonStore.policyCategoryId)

const typeList = ref([])
const currentTypeIndex = ref(-1)
const checkedType = (type) => {
  currentTypeIndex.value = type
  refresh()
}
const loadTypes = () => {
  request({
    url: `/mini/categories?parent_id=${policyCategoryId.value}`,
  }).then((res) => {
    typeList.value = res.result.data
  })
}

const keyword = ref("")

const goDetail = (item) => {
  navTo(`/pages/policy/detail?articleId=${item.id}`)
}

const list = ref([])
const loadSections = (options) => {
  return new Promise((resolve) => {
    request({
      url: "/mini/articles",
      method: "GET",
      data: Object.assign(
        {
          category_id:
            currentTypeIndex.value > -1
              ? typeList.value[currentTypeIndex.value].id
              : policyCategoryId.value,
          keyword: keyword.value,
        },
        options
      ),
    }).then((res) => {
      list.value.push(...(res.result?.data || []))
      resolve(res)
    })
  })
}

const { page, loadPage, loadMoreStatus, refresh } = usePagination({
  loadData: loadSections,
  list,
})

watch(
  () => policyCategoryId.value,
  (newVal, oldVal) => {
    if (newVal) {
      loadPage()
      loadTypes()
    }
  },
  { immediate: true }
)

onLoad((options) => {
  console.log(options)
})
onPullDownRefresh(() => {
  refresh()
})
onReachBottom(() => {
  loadPage()
})
</script>

<template>
  <view>
    <navHeader
      bg="/static/image/policy.png"
      :imgHeight="374"
      :showLeft="true"
      color="#fff"
    ></navHeader>
    <view class="body">
      <view class="search">
        <up-search
          :show-action="false"
          placeholder="关键词搜索"
          placeholderColor="#bdc4ce"
          searchIconColor="#bdc4ce"
          v-model="keyword"
          bgColor="#ffffff"
          :searchIconSize="28"
          :inputStyle="{ height: '80rpx' }"
          @search="refresh"
          @clear="refresh"
        ></up-search>
      </view>

      <view class="type-list">
        <view
          class="type-item"
          :class="currentTypeIndex == -1 ? 'active' : ''"
          @click="checkedType(-1)"
        >
          全部
        </view>
        <view
          class="type-item"
          :class="currentTypeIndex == index ? 'active' : ''"
          v-for="(item, index) in typeList"
          :key="index"
          @click="checkedType(index)"
        >
          {{ item.name }}
        </view>
      </view>

      <view class="list">
        <view
          class=""
          @click="goDetail(item)"
          v-for="(item, index) in list"
          :key="index"
        >
          <policyItem :item="item" />
        </view>

        <empty v-if="!list.length && loadMoreStatus === 'nomore'" />

        <up-loadmore v-if="list.length" :status="loadMoreStatus" />
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.body {
  min-height: calc(100vh - 430rpx);
  width: 750rpx;
  background: rgba(247, 248, 250, 0.65);
  border-radius: 44rpx 44rpx 0rpx 0rpx;
  border: 1rpx solid #fff;
  backdrop-filter: blur(10rpx);

  margin-top: 331rpx;
  padding: 35rpx 24rpx;

  .search {
    // margin-bottom: 24rpx;
  }

  .type-list {
    display: flex;
    align-items: center;
    overflow-x: auto;
    width: 100%;
    // height: 60rpx;
    padding: 32rpx 0rpx;

    .type-item {
      flex-shrink: 0;
      color: #111111;
      padding: 0 36rpx;
      height: 54rpx;
      line-height: 54rpx;
      background: #ffffff;
      border-radius: 25rpx 25rpx 25rpx 25rpx;
      border: 1rpx solid #f7f8fa;
      text-align: center;
      margin-right: 10rpx;

      font-weight: 500;
      font-size: 24rpx;
      color: #111111;
    }
    .active {
      color: #577f49;
      background: #f5fff2;
      border: 1rpx solid #577f49;
    }
  }

  .list {
  }
}
</style>
