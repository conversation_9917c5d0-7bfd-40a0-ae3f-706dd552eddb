<script setup>
import { ref } from "vue"
import { onLoad } from "@dcloudio/uni-app"
import request from "@/utils/request"

const personnelId = ref("")
const personnelDetail = ref({})

const faceUserIdKey = ref("")
const face = async () => {
  wx.startFacialRecognitionVerify({
    userIdKey: faceUserIdKey.value,
    success: (res) => {
      if (res.errCode === 0) {
        request({
          url: "/mini/recall_get_user_face_result",
          data: {
            verify_result: res.verifyResult,
          },
        }).then((res) => {
          console.log(res)
          getPersonnelDetail()
        })
      }
    },
    fail: (err) => {
      console.log(err)
    },
  })
}

const getFaceUserIdKey = () => {
  request({
    url: `/mini/recall_get_user_id_key?id=${personnelId.value}`,
  }).then((res) => {
    if (res.code === 200) {
      faceUserIdKey.value = res.result.user_id_key
    }
  })
}

const getPersonnelDetail = () => {
  request({
    url: `/mini/recall_get_user_info?id=${personnelId.value}`,
  }).then((res) => {
    if (res.code === 200) {
      personnelDetail.value = res.result

      if (!res.result.face_complete) {
        getFaceUserIdKey()
      }
    }
  })
}

onLoad((options) => {
  personnelId.value = options.id
  // getFaceUserIdKey()
  getPersonnelDetail()
})
</script>

<template>
  <navHeader
    title="特殊时期工作"
    bg="/static/image/bg.png"
    :showLeft="false"
  ></navHeader>
  <view class="page">
    <view class="img">
      <image src="/static/image/face-verify.svg" mode=""></image>
    </view>
    <view class="title"> 人脸认证 </view>
    <view class="text"> 为避免影响使用本系统，请尽快 完成人脸识别认证。 </view>
    <view class="btn-wrapper">
      <button
        class="btn"
        @click="face"
        :disabled="personnelDetail.face_complete"
      >
        {{ personnelDetail.face_complete ? "已认证" : "开始认证" }}
      </button>
    </view>

    <view class="footer"> 北京市人民政府征兵办公室 </view>
  </view>
</template>

<style lang="scss">
page {
  // background: #ffffff;
}
</style>
<style lang="scss" scoped>
.page {
  padding: 0 60rpx;
  padding-top: 250rpx;
}

.img {
  width: 58rpx;
  height: 58rpx;
  margin: 50rpx auto;

  image {
    width: 58rpx;
    height: 58rpx;
  }
}
.title {
  font-weight: bold;
  font-size: 52rpx;
  color: #000000;
  text-align: center;
  margin-bottom: 70rpx;
}
.text {
  font-weight: 400;
  font-size: 32rpx;
  color: #9ca3b5;
  // text-align: center;
  font-style: normal;
  text-transform: none;
  margin-bottom: 51rpx;
}

.btn-wrapper {
  margin-top: 100rpx;
}

.btn {
  height: 100rpx;
  background: #577f49;
  box-shadow: 0rpx 20rpx 48rpx 1rpx rgba(87, 127, 73, 0.2);
  font-size: 34rpx;
  border-radius: 55rpx 55rpx 55rpx 55rpx;
  color: #fff;
  line-height: 100rpx;
  text-align: center;

  &[disabled] {
    background: #bdc4ce;
    color: #fff;
    box-shadow: none;
  }
}

.footer {
  position: fixed;
  left: 0;
  bottom: 110rpx;
  width: 100%;
  text-align: center;
  color: #bdc4ce;
  font-size: 26rpx;
}
</style>
