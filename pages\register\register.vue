<script setup>
import { ref, watch, computed } from "vue"
import { onLoad, onShow } from "@dcloudio/uni-app"
import request from "@/utils/request"
import { inputStyle } from "@/utils/constants.js"
import { navTo } from "@/utils/utils.js"

//步骤
const step = ref(0)
const formData = ref({
  invitation_code: "",
  name: "",
  id_card: "",
  sms_code: "",
  department_code: "",
  job: "",
  phone: "",
  captcha_code: "",
  // business_type_id: "",
})

// 第一步的规则判断
const rules = {
  invitation_code: [
    {
      type: "string",
      required: true,
      message: "请填写注册码",
      trigger: "blur",
    },
    {
      type: "string",
      pattern: /^[a-zA-Z0-9]{6}$/,
      message: "注册码格式不正确",
      trigger: "blur",
    },
  ],
  name: [
    {
      type: "string",
      required: true,
      message: "请填写姓名",
      trigger: "blur",
    },
    {
      type: "string",
      min: 2,
      message: "姓名至少2个字符",
      trigger: "blur",
    },
  ],
  id_card: [
    {
      type: "string",
      required: true,
      message: "请填写身份证号码",
      trigger: "blur",
    },
    {
      type: "string",
      pattern: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/,
      message: "身份证号码格式不正确",
      trigger: "blur",
    },
  ],
  sms_code: {
    type: "string",
    required: true,
    message: "请填写验证码",
    trigger: "blur",
  },
  department_code: {
    type: "string",
    required: true,
    message: "请填写单位",
    trigger: "blur",
  },
  job: {
    type: "string",
    required: true,
    message: "请填写职务",
    trigger: "blur",
  },
  phone: {
    type: "string",
    required: true,
    message: "请填写手机号",
    trigger: "blur",
  },
  // business_type_id: {
  //   type: "string",
  //   required: true,
  //   message: "请填写业务类型",
  // },
}

// 业务类别
// const businessType = ref({
//   name: "",
// })
// const showBusinessType = ref(false)
// const businessTypeList = ref([])
// const changeBusinessType = (e) => {
//   console.log(e)
//   businessType.value = e
//   formData.value.business_type_id = e.id
//   showBusinessType.value = false
// }
// const fetchBusinessType = () => {
//   request({
//     url: "/mini/business_types",
//     method: "get",
//   }).then((res) => {
//     if (res.code === 200) {
//       businessTypeList.value = res.result
//     }
//   })
// }

//第一步表单
const uFormRef = ref(null)
//进行下一步
const next = () => {
  uFormRef.value
    .validate()
    .then((valid) => {
      if (valid) {
        // step.value = 1
        uni.showLoading({
          title: "加载中",
        })
        checkFirstStep()
      }
    })
    .catch(() => {
      // 处理验证错误
      uni.$u.toast("校验失败")
    })
}

const checkFirstStep = () => {
  request({
    url: "/mini/register_first_step_check",
    method: "post",
    data: formData.value,
  }).then((res) => {
    if (res.code === 200) {
      accountData.value.phone = formData.value.phone
      step.value = 1
    } else {
      formData.value.sms_code = ""
      getCaptcha()
    }
    uni.hideLoading()
  })
}

// 第二步
const accountFormRef = ref(null)

const accountData = ref({
  phone: "",
  password: "",
  password_confirmation: "",
})

const accountRules = {
  phone: {
    type: "string",
    required: true,
    message: "请填写登录账号",
  },
  password: [
    {
      type: "string",
      required: true,
      message: "请填写密码",
    },
    {
      type: "string",
      pattern: /^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{6,}$/,
      message: "密码需包含数字和字母，不少于6位数",
    },
  ],
  password_confirmation: [
    {
      type: "string",
      required: true,
      message: "请填写确认密码",
    },
    {
      type: "string",
      validator: (rule, value, callback) => {
        if (value !== accountData.value.password) {
          callback(new Error("两次输入密码不一致"))
        } else {
          callback()
        }
      },
    },
  ],
}

const submit = () => {
  accountFormRef.value
    .validate()
    .then((valid) => {
      if (valid) {
        open()
      }
    })
    .catch(() => {
      // 处理验证错误
      uni.$u.toast("校验失败")
    })
}
// 注册完成弹窗
const show = ref(false)

//提交注册
const register = () => {
  request({
    url: "/mini/register",
    method: "post",
    data: Object.assign(formData.value, accountData.value),
  }).then((res) => {
    if (res.code === 200) {
      navTo("/pages/result/accountResult", { title: "注册成功" })
    }
  })
}
const close = () => {
  show.value = false
}
const open = () => {
  show.value = true
}

const department = ref({
  name: "",
})
const getInvitationCodeInfo = (code) => {
  request({
    url: `/mini/invitation_code/${code}`,
    method: "get",
  }).then((res) => {
    if (res.code === 200) {
      department.value = res.result[res.result.length - 1]
      formData.value.department_code = department.value.code
    }
  })
}
const onInvitationCodeInput = (e) => {
  if (e.length === 6) {
    getInvitationCodeInfo(e)
  } else {
    department.value = {
      name: "",
    }
    formData.value.department_code = ""
  }
}

const captcha = ref({})
const getCaptcha = () => {
  formData.value.captcha_code = ""
  request({
    url: "/mini/get_captcha",
    method: "post",
  }).then((res) => {
    if (res.code === 200) {
      captcha.value = res.result
    }
  })
}

const smsCountdown = ref(0)
const sendSmsCode = () => {
  if (!formData.value.phone) {
    uni.$u.toast("请输入手机号")
    return
  }
  if (!formData.value.captcha_code) {
    uni.$u.toast("请输入图形验证码")
    return
  }

  request({
    url: "/mini/send_code",
    method: "post",
    data: {
      phone: formData.value.phone,
      scene: "register",
      captcha_key: captcha.value.key,
      captcha_code: formData.value.captcha_code,
    },
  }).then((res) => {
    if (res.code === 200) {
      uni.$u.toast("发送成功")
      smsCountdown.value = 60
      const timer = setInterval(() => {
        smsCountdown.value--
        if (smsCountdown.value <= 0) {
          clearInterval(timer)
        }
      }, 1000)
    } else {
      getCaptcha()
    }
  })
}

const back = () => {
  uni.navigateBack()
}

onLoad((options) => {
  step.value = options.step ? parseInt(options.step) : 0

  if (step.value === 0) {
    // fetchBusinessType()
    getCaptcha()
  }

  if (options.invitation_code) {
    formData.value.invitation_code = options.invitation_code
    getInvitationCodeInfo(options.invitation_code)
  }
})
</script>

<template>
  <view class="page">
    <navHeader
      title=""
      :imgHeight="980"
      bg="/static/image/register.png"
      :showLeft="true"
    ></navHeader>

    <view class="body">
      <view class="header">
        <view class="header-title"> 新用户注册 </view>
        <up-steps
          :current="step"
          active-color="#577F49"
          inactive-color="#D8D8D8"
        >
          <up-steps-item title="基本信息"> </up-steps-item>
          <up-steps-item title="账号设置"></up-steps-item>
          <up-steps-item title="注册成功"></up-steps-item>
        </up-steps>
      </view>
      <!-- 第一步 -->
      <up-form
        v-if="step == 0"
        labelPosition="top"
        labelWidth="auto"
        :model="formData"
        :rules="rules"
        ref="uFormRef"
      >
        <up-form-item label="注册码" prop="invitation_code">
          <up-input
            :customStyle="inputStyle"
            v-model="formData.invitation_code"
            placeholder="请输入注册码"
            placeholderClass="placeholder"
            @change="onInvitationCodeInput"
          ></up-input>
        </up-form-item>
        <up-form-item
          label="单位"
          prop="department_code"
          v-if="formData.department_code"
        >
          <up-input
            color="#BDC4CE"
            :customStyle="inputStyle"
            :modelValue="department.name"
            readonly
          ></up-input>
        </up-form-item>

        <up-form-item label="姓名" prop="name">
          <up-input
            :customStyle="inputStyle"
            v-model="formData.name"
            placeholder="请输入姓名"
            placeholderClass="placeholder"
          ></up-input>
        </up-form-item>
        <up-form-item label="身份证号码" prop="id_card">
          <up-input
            :customStyle="inputStyle"
            v-model="formData.id_card"
            disabledColor="#ffffff"
            placeholder="请输入身份证号码"
            placeholderClass="placeholder"
          >
          </up-input>
        </up-form-item>

        <up-form-item label="图形验证码" prop="captcha_code">
          <up-input
            :customStyle="inputStyle"
            v-model="formData.captcha_code"
            disabledColor="#ffffff"
            placeholder="请输入图形验证码"
            placeholderClass="placeholder"
          ></up-input>
          <image :src="captcha.img" @click="getCaptcha" class="captcha"></image>
        </up-form-item>

        <up-form-item label="手机号" prop="phone">
          <up-input
            :customStyle="inputStyle"
            v-model="formData.phone"
            disabledColor="#ffffff"
            placeholder="请输入手机号"
            placeholderClass="placeholder"
          ></up-input>
        </up-form-item>

        <up-form-item label="手机验证码" prop="sms_code">
          <up-input
            :customStyle="inputStyle"
            v-model="formData.sms_code"
            disabledColor="#ffffff"
            placeholder="请输入手机验证码"
            placeholderClass="placeholder"
          ></up-input>
          <button
            class="sms-btn"
            @click="sendSmsCode"
            :disabled="smsCountdown > 0"
          >
            {{ smsCountdown > 0 ? smsCountdown + "s" : "获取验证码" }}
          </button>
        </up-form-item>

        <up-form-item label="职务" prop="job">
          <up-input
            :customStyle="inputStyle"
            v-model="formData.job"
            disabledColor="#ffffff"
            placeholder="请输入职务"
            placeholderClass="placeholder"
          ></up-input>
        </up-form-item>

        <!-- <up-form-item
          label="业务类别"
          prop="business_type_id"
          @click="showBusinessType = true"
        >
          <up-input
            :modelValue="businessType.name"
            readonly
            :customStyle="inputStyle"
            disabledColor="#ffffff"
            placeholder="请选择业务类别"
            placeholderClass="placeholder"
          ></up-input>
          <template #right style="margin-right: 100rpx">
            <up-icon name="arrow-right"></up-icon>
          </template>
        </up-form-item> -->
      </up-form>

      <!-- 第二步 -->
      <up-form
        v-if="step == 1"
        labelPosition="top"
        labelWidth="auto"
        :model="accountData"
        :rules="accountRules"
        ref="accountFormRef"
      >
        <up-form-item label="账号" prop="phone">
          <up-input
            :customStyle="inputStyle"
            v-model="accountData.phone"
            placeholder="手机号码作为登录账号"
            placeholderClass="placeholder"
          ></up-input>
        </up-form-item>
        <up-form-item
          label="设置密码（需包含数字和字母，不少于6位数）"
          prop="password"
        >
          <up-input
            :customStyle="inputStyle"
            v-model="accountData.password"
            disabledColor="#ffffff"
            placeholder="请输入密码"
            placeholderClass="placeholder"
            type="password"
          >
          </up-input>
        </up-form-item>
        <up-form-item label="再次输入密码" prop="password_confirmation">
          <up-input
            :customStyle="inputStyle"
            v-model="accountData.password_confirmation"
            disabledColor="#ffffff"
            placeholder="请再次输入密码"
            placeholderClass="placeholder"
            type="password"
          >
          </up-input>
        </up-form-item>
      </up-form>

      <up-action-sheet
        :show="showBusinessType"
        :actions="businessTypeList"
        title="请选择业务类别"
        @close="showBusinessType = false"
        @select="changeBusinessType"
      >
      </up-action-sheet>

      <up-popup
        :show="show"
        mode="center"
        :safeAreaInsetBottom="false"
        :round="20"
        closeable
        @close="close"
        @open="open"
      >
        <view class="pop">
          <view class="title"> 确定提交注册吗? </view>
          <view class="text"> 请确保信息无误 </view>
          <view class="btn-wrapper">
            <button class="btn" @click="register">提交注册</button>
          </view>
        </view>
      </up-popup>
    </view>

    <view class="footer">
      <view class="btn-group">
        <template v-if="step == 0">
          <button class="btn next" @click="next">下一步</button>
        </template>
        <template v-if="step == 1">
          <button class="btn prev" @click="up">上一步</button>
          <button class="btn submit" @click="submit">提交</button>
        </template>
      </view>

      <view class="bottom_text">
        已有账号？
        <text @click="back"> 前往登录 </text>
      </view>
    </view>
  </view>
</template>

<style lang="scss">
page {
  background-color: #ffffff;
}
.u-form-item__body__right__content {
  position: relative;
}
.item__body__right__content__icon {
  transform: translateX(-70rpx);
  position: absolute;
  right: 0rpx;
}
</style>

<style lang="scss" scoped>
.placeholder-disabled {
  color: #bdc4ce;
}
.page {
  padding-bottom: 250rpx;

  .pop {
    box-sizing: border-box;
    width: 630rpx;
    background: #ffffff;
    border-radius: 20rpx 20rpx 20rpx 20rpx;
    border: 2rpx solid #ffffff;

    .title {
      margin-top: 80rpx;
      font-weight: bold;
      font-size: 38rpx;
      color: #06121e;
      line-height: 40rpx;
      text-align: center;
    }
    .text {
      font-weight: 400;
      font-size: 32rpx;
      color: #9ca3b5;
      text-align: center;
      font-style: normal;
      text-transform: none;
      margin-top: 27rpx;
      margin-bottom: 67rpx;
    }

    .btn-wrapper {
      padding-bottom: 38rpx;
      display: flex;
      justify-content: center;
    }

    .btn {
      width: 524rpx;
      height: 100rpx;
      background: #577f49;
      border-radius: 55rpx 55rpx 55rpx 55rpx;
      color: #fff;
      line-height: 100rpx;
      text-align: center;
    }
  }
}

.footer {
  width: 750rpx;
  background: #ffffff;
  box-shadow: 0rpx 3rpx 60rpx 1rpx rgba(0, 0, 0, 0.16);
  border-radius: 22rpx 22rpx 0rpx 0rpx;
  position: fixed;
  bottom: 0;
  left: 0;
  padding-top: 40rpx;
  padding-bottom: calc(env(safe-area-inset-bottom) / 2 + 40rpx);
  z-index: 999;

  .btn-group {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 60rpx;
    padding: 0 60rpx;

    .btn {
      flex: 1;
      height: 100rpx;
      border-radius: 50rpx 50rpx 50rpx 50rpx;
      font-weight: 500;
      font-size: 34rpx;
      color: #ffffff;
      line-height: 100rpx;
      text-align: center;

      &.next,
      &.submit {
        background: #577f49;
        box-shadow: 0rpx 20rpx 48rpx 1rpx rgba(87, 127, 73, 0.2);
        color: #ffffff;
      }

      &.prev {
        background: #ffffff;
        border: 1rpx solid #e1e1e1;
        color: #21232c;
      }
    }
  }

  .bottom_text {
    margin-top: 50rpx;
    text-align: center;
    color: #b7b9be;
    text {
      color: #577f49;
    }
  }
}

.body {
  width: 750rpx;
  padding: 60rpx;
  padding-top: 200rpx;
  padding-bottom: 200rpx;
}

.header {
  margin-bottom: 70rpx;

  .header-title {
    font-weight: 800;
    font-size: 48rpx;
    color: #21232c;
    text-align: center;
    font-style: normal;
    text-transform: none;
    margin-bottom: 70rpx;
  }
}

.u-input {
  margin: 0rpx !important;
}

.sms-btn {
  font-size: 32rpx;
  color: #577f49;
  border: none;
  background: none;
  position: absolute;
  right: 34rpx;
  top: 50%;
  transform: translateY(-50%);
  z-index: 2;
}

.captcha {
  width: 233rpx;
  height: 62rpx;
  background: #577f49;
  border-radius: 15rpx 15rpx 15rpx 15rpx;
  position: absolute;
  right: 34rpx;
  top: 50%;
  transform: translateY(-50%);
  z-index: 2;
}
</style>
