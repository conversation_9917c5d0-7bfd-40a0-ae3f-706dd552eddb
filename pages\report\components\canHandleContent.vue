<script setup>
import { ref, watch, computed } from "vue"
import dayjs from "dayjs"
import { navTo } from "@/utils/utils"
import { usePagination } from "@/utils/hooks"
import request from "@/utils/request"

const props = defineProps({
  queryParams: {
    type: Object,
    default: () => ({}),
  },
})

const goDetail = (item) => {
  navTo(`/pages/report/detail?reportId=${item.id}&scope=can_handle`)
}

const list = ref([])
const total = ref(0)
const loadData = async (options) => {
  return new Promise((resolve) => {
    request({
      url: "/mini/report",
      method: "GET",
      data: Object.assign(
        {
          scope: "can_handle",
          ...props.queryParams,
        },
        options
      ),
    }).then((res) => {
      list.value.push(...(res.result?.data || []))
      total.value = res.result?.total || 0
      resolve(res)
    })
  })
}
const { page, loadPage, loadMoreStatus, refresh } = usePagination({
  loadData,
  list,
})

defineExpose({
  total,
  list,
  loadPage,
  refresh,
})
</script>

<template>
  <view class="list">
    <view
      class="list-item-handle"
      @click="goDetail(item)"
      v-for="(item, index) in list"
      :key="index"
    >
      <view class="left">
        <view class="content"> {{ item.content }} </view>
        <view class="date">
          提交于：{{ dayjs(item.submitted_at).format("YYYY/MM/DD HH:mm") }}
        </view>
      </view>
      <view class="right">
        <button
          class="btn"
          @click.stop="goDetail(item)"
          v-if="item.status === 'created'"
        >
          处理
        </button>
        <button
          class="btn btn-done"
          @click.stop="goDetail(item)"
          v-if="item.status === 'replied'"
        >
          已处理
        </button>
      </view>
    </view>

    <empty v-if="!list.length && loadMoreStatus === 'nomore'" />
    <up-loadmore v-if="list.length" :status="loadMoreStatus" />
  </view>
</template>

<style lang="scss" scoped>
.list {
  .list-item-handle {
    background: #ffffff;
    box-shadow: 0rpx 6rpx 13rpx 1rpx rgba(0, 0, 0, 0.04);
    border-radius: 20rpx 20rpx 20rpx 20rpx;
    padding: 30rpx;
    display: flex;
    justify-content: space-between;
    gap: 45rpx;
    margin-bottom: 20rpx;

    .left {
      flex: 1;
      overflow: hidden;

      .content {
        margin-bottom: 30rpx;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }

      .date {
        font-weight: 400;
        font-size: 24rpx;
        color: #9ca3b5;
      }
    }

    .right {
      flex-shrink: 0;
      display: flex;
      flex-direction: column;
      justify-content: center;

      .btn {
        width: 121rpx;
        height: 61rpx;
        background: #577f49;
        border-radius: 31rpx 31rpx 31rpx 31rpx;
        font-weight: 500;
        font-size: 26rpx;
        color: #ffffff;
        line-height: 61rpx;
        text-align: center;

        &.btn-done {
          background: #f7f8fa;
          color: #9ca3b5;
        }
      }
    }
  }
}
</style>
