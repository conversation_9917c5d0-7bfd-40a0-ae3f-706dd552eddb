<script setup>
import { ref, watch, computed } from "vue"
import dayjs from "dayjs"
import { navTo } from "@/utils/utils"
import { usePagination } from "@/utils/hooks"
import request from "@/utils/request"

const props = defineProps({
  queryParams: {
    type: Object,
    default: () => ({}),
  },
})

const goDetail = (item) => {
  navTo(`/pages/report/detail?reportId=${item.id}&scope=create`)
}

const list = ref([])
const loadData = async (options) => {
  return new Promise((resolve) => {
    request({
      url: "/mini/report",
      method: "GET",
      data: Object.assign(
        {
          scope: "create",
          ...props.queryParams,
        },
        options
      ),
    }).then((res) => {
      list.value.push(...(res.result?.data || []))
      resolve(res)
    })
  })
}
const { page, loadPage, loadMoreStatus, refresh } = usePagination({
  loadData,
  list,
})

defineExpose({
  list,
  loadPage,
  refresh,
})
</script>

<template>
  <view class="list">
    <view
      class="list-item-create"
      @click="goDetail(item)"
      v-for="(item, index) in list"
      :key="index"
    >
      <view
        class="tag"
        :class="{
          wait: item.status === 'created',
          end: item.status === 'replied',
        }"
      >
        {{ item.status === "created" ? "待回复" : "已回复" }}
      </view>
      <view class="content">
        {{ item.content }}
      </view>
      <view class="date">
        提交于：{{ dayjs(item.submitted_at).format("YYYY/MM/DD HH:mm") }}
      </view>
    </view>

    <empty v-if="!list.length && loadMoreStatus === 'nomore'" />
    <up-loadmore v-if="list.length" :status="loadMoreStatus" />
  </view>
</template>

<style lang="scss" scoped>
.list {
  .list-item-create {
    position: relative;
    background: #ffffff;
    box-shadow: 0rpx 6rpx 13rpx 1rpx rgba(0, 0, 0, 0.04);
    border-radius: 20rpx 20rpx 20rpx 20rpx;
    padding: 30rpx;
    margin-bottom: 20rpx;

    .content {
      margin-top: 25rpx;
      margin-bottom: 30rpx;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
    }

    .date {
      font-weight: 400;
      font-size: 24rpx;
      color: #9ca3b5;
    }

    .wait {
      background: #f39c12;
    }

    .end {
      background: #577f49;
    }

    .tag {
      position: absolute;
      top: 0;
      left: 0;
      border-radius: 15rpx 0rpx 15rpx 0rpx;
      width: 86rpx;
      font-weight: 500;
      font-size: 22rpx;
      color: #ffffff;
      padding-left: 10rpx;
    }
  }
}
</style>
