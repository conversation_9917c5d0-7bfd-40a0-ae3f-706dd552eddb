<script setup>
import { ref, watch, computed } from "vue"
import { onLoad, onShow } from "@dcloudio/uni-app"
import request from "@/utils/request"
import { navTo } from "@/utils/utils"
import dayjs from "dayjs"
import { reportStatusMap } from "@/utils/constants"

const scope = ref("create")

const reportId = ref(0)
const detailData = ref({})
const loadData = () => {
  request({
    url: `/mini/report/${reportId.value}`,
  }).then((res) => {
    if (res.code === 200) {
      detailData.value = res.result
    }
  })
}

onLoad((options) => {
  reportId.value = options.reportId

  if (options.scope) {
    scope.value = options.scope
  }
})

onShow(() => {
  loadData()
})
</script>

<template>
  <view class="page">
    <view class="section">
      <view class="status wait" v-if="detailData.status === 'created'">
        <image src="/static/image/status-wait.svg" class="icon" />
        {{ scope === "can_handle" ? "待处理" : "待回复" }}
      </view>
      <view class="status ok" v-if="detailData.status === 'replied'">
        <image src="/static/image/status-success.svg" class="icon" />
        {{ scope === "can_handle" ? "已处理" : "已回复" }}
      </view>
    </view>

    <view class="section" v-if="detailData.reply_content">
      <view class="item">
        <view class="header">
          <view class=""> 回复内容 </view>
          <view class="date" v-if="detailData.processed_at">
            {{ dayjs(detailData.processed_at).format("YYYY-MM-DD HH:mm") }}
          </view>
        </view>
        <view class="content"> {{ detailData.reply_content }} </view>
        <view
          class="reply-files"
          v-if="detailData.reply_file_ids_attachments?.length > 0"
        >
          <fileList :fileList="detailData.reply_file_ids_attachments" />
        </view>
      </view>
    </view>

    <view class="section">
      <view class="item">
        <view class="header"> 举报详情 </view>
        <view class="content">
          <rich-text :nodes="detailData.content" />
        </view>
      </view>

      <view class="item" v-if="detailData.file_ids_attachments?.length > 0">
        <view class="header"> 相关材料 </view>
        <view class="content">
          <fileList :fileList="detailData.file_ids_attachments" />
        </view>
      </view>

      <!-- <view class="item">
        <view class="header"> 举报者信息 </view>
        <view class="content user-info">
          <view class="user-info-item"> {{ detailData.user_name }} </view>
          <view class="user-info-item">
            {{ detailData.district_name }} {{ detailData.department_name }}
            {{ detailData.user?.job }}
          </view>
          <view class="user-info-item phone-wrapper">
            <text>{{ detailData.phone }} </text>
            <button class="phone" @click="callPhone(detailData.phone)">
              <image src="/static/image/call.svg" />
              拨打
            </button>
          </view>
        </view>
      </view> -->
    </view>

    <view class="time">
      提交于: {{ dayjs(detailData.created_at).format("YYYY/MM/DD HH:mm") }}
    </view>

    <view class="footer" v-if="detailData.can_reply">
      <button
        class="reply"
        @click="
          navTo('/pages/report/reply', {
            reportId,
          })
        "
      >
        {{
          detailData.status === reportStatusMap.replied.status
            ? "修改回复"
            : "回复"
        }}
      </button>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.page {
  background: #f7f8fa;
  padding-bottom: 100px;
}
.footer {
  position: fixed;
  bottom: 0rpx;
  width: 750rpx;
  background: #ffffff;
  box-shadow: 0rpx 3rpx 60rpx 1rpx rgba(0, 0, 0, 0.16);
  border-radius: 22rpx 22rpx 0rpx 0rpx;
  padding: 40rpx 60rpx;
  padding-bottom: calc(env(safe-area-inset-bottom) / 2 + 40rpx);
  display: flex;
  justify-content: space-between;
  gap: 34rpx;

  button {
    flex: 1;
    height: 100rpx;
    line-height: 100rpx;
    border-radius: 50rpx 50rpx 50rpx 50rpx;
    font-weight: 500;
    font-size: 34rpx;
  }

  .up {
    background: #ffffff;
    border: 1rpx solid #e1e1e1;
    color: #000;
  }
  .reply {
    background: #577f49;
    color: #ffffff;
    box-shadow: 0rpx 20rpx 48rpx 1rpx rgba(87, 127, 73, 0.2);
  }
}
.status {
  display: flex;
  align-items: center;

  .icon {
    width: 42rpx;
    height: 42rpx;
    margin-right: 18rpx;
  }

  &.wait {
    font-weight: 500;
    font-size: 42rpx;
    color: #f39c12;
  }
  &.ok {
    font-weight: 500;
    font-size: 42rpx;
    color: #577f49;
  }
}

.section {
  padding: 38rpx 60rpx;
  background-color: #fff;
  margin-bottom: 20rpx;

  .item {
    margin-bottom: 65rpx;

    &:last-child {
      margin-bottom: 0;
    }

    .header {
      font-weight: 400;
      font-size: 26rpx;
      color: #9ca3b5;
      display: flex;
      justify-content: space-between;
      margin-bottom: 6rpx;
    }
    .content {
      color: #000;
      font-size: 30rpx;
      font-weight: 500;
    }
  }
}

.user-info {
  .user-info-item {
    margin-bottom: 6rpx;
    line-height: 42rpx;
  }

  .phone-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 10rpx;
  }

  .phone {
    background: #ffffff;
    height: 42rpx;
    line-height: 42rpx;
    color: #577f49;
    border: 1rpx solid #577f49;
    border-radius: 31rpx 31rpx 31rpx 31rpx;
    padding: 0 12rpx;
    font-size: 22rpx;
    display: flex;
    justify-content: center;
    align-items: center;

    image {
      width: 28rpx;
      height: 28rpx;
    }
  }
}

.time {
  font-size: 24rpx;
  color: #9ca3b5;
  text-align: center;
  padding: 80rpx 0;
}
</style>
