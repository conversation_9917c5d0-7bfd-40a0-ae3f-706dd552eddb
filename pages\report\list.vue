<script setup>
import { ref, watch, computed, nextTick } from "vue"
import {
  onLoad,
  onShow,
  onPullDownRefresh,
  onReachBottom,
} from "@dcloudio/uni-app"
import request from "@/utils/request"
import { checkPermissionOr, navTo } from "@/utils/utils"
import dayjs from "dayjs"
import canHandleContent from "./components/canHandleContent.vue"
import createContent from "./components/createContent.vue"
import { permissionConst } from "@/utils/permissionConst"

const createContentRef = ref(null)
const canHandleContentRef = ref(null)

const keyword = ref("")

const currentTabIndex = ref(0)
const tabs = computed(() => [
  {
    title: "由我提交",
    component: createContentRef,
    count: statistics.value.my,
    show: 1,
  },
  {
    title: "需我处理",
    component: canHandleContentRef,
    count: statistics.value.can_handle,
    show: checkPermissionOr([permissionConst.mini_report_reply.status]),
  },
])
const currentTab = computed(() => tabs.value[currentTabIndex.value])
const changeTab = (index) => {
  currentTabIndex.value = index
  if (!currentTab.value?.component.value?.list.length) {
    loadMore()
  }
}

const loadMore = () => {
  currentTab.value?.component.value?.loadPage()
}

const statistics = ref({
  my: 0,
  can_handle: 0,
})
const loadStatistics = async () => {
  const res = await request({
    url: "/mini/report_statistics",
  })
  if (res && res.result) {
    statistics.value = res.result
  }
}

const refresh = () => {
  currentTab.value?.component.value?.refresh()
}

onLoad((options) => {
  loadStatistics()

  nextTick(() => {
    changeTab(currentTabIndex.value)
  })
})

onReachBottom(() => {
  loadMore()
})
</script>

<template>
  <view style="">
    <view class="header">
      <view class="title">
        <template v-for="(item, index) in tabs" :key="index">
          <view
            v-if="item.show"
            :class="currentTabIndex == index ? 'checked' : ''"
            @click="changeTab(index)"
          >
            <view class=""> {{ item.title }}({{ item.count }}) </view>
            <view class="green" v-if="currentTabIndex == index"> </view>
          </view>
        </template>
      </view>
      <view class="search">
        <up-search
          :show-action="false"
          placeholder="搜索"
          placeholderColor="#8E9BAE"
          searchIconColor="#8E9BAE"
          v-model="keyword"
          bgColor="#f7f8fa"
          :searchIconSize="24"
          :inputStyle="{ height: '74rpx' }"
          @search="refresh"
          @clear="refresh"
        ></up-search>
      </view>
    </view>

    <view class="body">
      <view class="list">
        <createContent
          ref="createContentRef"
          v-show="currentTabIndex == 0"
          :queryParams="{ keyword }"
        />
        <canHandleContent
          v-if="checkPermissionOr([permissionConst.mini_report_reply.status])"
          ref="canHandleContentRef"
          v-show="currentTabIndex == 1"
          :queryParams="{ keyword }"
        />
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.search {
  margin: 24rpx;
}

.header {
  background: #ffffff;
  padding-bottom: 10rpx;

  .title {
    font-size: 30rpx;
    color: rgba(0, 0, 0, 0.9);
    text-align: center;
    font-style: normal;
    text-transform: none;
    display: flex;
    text-align: center;
    margin: 0rpx auto;
    padding-top: 50rpx;
    justify-content: space-evenly;
    border-bottom: 1rpx solid #e6e6e6;

    .green {
      width: 25rpx;
      height: 0rpx;
      background: #ffffff;
      border-bottom: 6rpx solid #577f49;
      border-radius: 6rpx;
      margin: 26rpx auto 0;
    }

    .checked {
      font-weight: bold;
      color: #577f49;
    }
  }
}

.body {
  background: #f7f8fa;
  padding: 24rpx;

  .list {
    width: 702rpx;
    height: 202rpx;
    margin: 0 auto;
  }
}
</style>
