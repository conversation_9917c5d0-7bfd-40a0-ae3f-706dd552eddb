<script setup>
import { ref, watch, computed } from "vue"
import { onLoad, onShow } from "@dcloudio/uni-app"
import request from "@/utils/request"

const reportId = ref(0)
const fileList = ref([])
const content = ref("")

const submit = () => {
  console.log("submit")
  const data = {
    reply_content: content.value,
    reply_file_ids: fileList.value.map((item) => item.id),
  }
  request({
    url: `/mini/report/${reportId.value}/reply`,
    method: "POST",
    data,
  }).then((res) => {
    if (res.code === 200) {
      uni.showToast({
        title: "提交成功",
        icon: "none",
        duration: 2000,
      })

      setTimeout(() => {
        uni.navigateBack()
      }, 2000)
    }
  })
}

onLoad((options) => {
  reportId.value = options.reportId ? parseInt(options.reportId) : 0
  loadData()
})

const detailData = ref({})
const loadData = () => {
  request({
    url: `/mini/report/${reportId.value}`,
  }).then((res) => {
    if (res.code === 200) {
      detailData.value = res.result
      content.value = detailData.value.reply_content
      if (Array.isArray(detailData.value.reply_file_ids)) {
        fileList.value = detailData.value.reply_file_ids_attachments
      }
    }
  })
}
</script>

<template>
  <view>
    <view class="body">
      <view class="box">
        <view class="title">
          <text style="color: #fb5854">*</text> 处理意见
        </view>
        <view>
          <textarea
            class="text-area"
            placeholder="请输入处理意见"
            v-model="content"
            :maxlength="-1"
          />
        </view>
        <view class="title"> 材料上传 </view>
        <uploadFiles v-model:fileList="fileList" />

        <view class="btn" @click="submit"> 提交 </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.body {
  position: relative;
  .box {
    width: 702rpx;
    min-height: 1075rpx;
    background: #ffffff;
    border-radius: 20rpx 20rpx 20rpx 20rpx;

    margin: 20rpx auto;
    padding: 30rpx;
    .title {
      font-weight: bold;
      font-size: 32rpx;
      color: #303445;
      margin-bottom: 11rpx;
    }
    .text-area {
      width: 618rpx;
      height: 350rpx;
      background: #f7f8fa;
      border-radius: 20rpx 20rpx 20rpx 20rpx;
      border: 1rpx solid #ebebeb;
      margin: 0 auto;
      margin-bottom: 40rpx;
      padding: 22rpx 38rpx;
      box-sizing: border-box;
    }

    .btn {
      width: 630rpx;
      height: 100rpx;
      background: #577f49;
      box-shadow: 0rpx 20rpx 48rpx 1rpx rgba(87, 127, 73, 0.2);
      border-radius: 50rpx 50rpx 50rpx 50rpx;
      margin: 0 auto;
      color: #fff;
      text-align: center;
      line-height: 100rpx;
      margin-top: 100rpx;
    }
  }
}
</style>
