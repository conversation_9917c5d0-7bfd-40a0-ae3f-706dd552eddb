<script setup>
import { ref, watch, computed } from "vue"
import { onLoad, onShow } from "@dcloudio/uni-app"
import request from "@/utils/request"
import { inputStyle } from "@/utils/constants.js"
import { navTo } from "@/utils/utils"
import { useUserStore } from "@/store/user"

const userStore = useUserStore()

const fileList = ref([])

const content = ref("")

const submit = () => {
  if (!content.value) {
    uni.showToast({
      title: "请输入举报详情",
      icon: "none",
      duration: 2000,
    })
    return
  }

  const data = {
    content: content.value,
    file_ids: fileList.value.map((item) => item.id),
  }
  request({
    url: "/mini/report",
    method: "POST",
    data,
  }).then((res) => {
    if (res.code === 200) {
      navTo("/pages/result/report", {}, "redirectTo")
    }
  })
}

onLoad((options) => {
  console.log(options)
})
</script>

<template>
  <view class="page">
    <navHeader
      bg="/static/image/report-bg.png"
      :imgHeight="478"
      :showLeft="true"
      color="#fff"
    ></navHeader>
    <view class="body">
      <view class="box">
        <view class="box-item">
          <view class="title">
            <text style="color: #fb5854">*</text> 举报详情
          </view>
          <view>
            <textarea
              class="text-area"
              placeholder="请输入举报详情"
              placeholderClass="placeholder"
              v-model="content"
              :maxlength="-1"
            />
            <!-- <up-textarea class="text-area"   v-model="content"  ></up-textarea> -->
          </view>
        </view>
        <view class="box-item">
          <view class="title"> 材料上传 </view>
          <uploadFiles v-model:fileList="fileList" />
        </view>

        <view class="btn" @click="submit"> 提交 </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.page {
  .body {
    padding: 24rpx;

    .box {
      margin-top: 320rpx;
      min-height: 1075rpx;
      background: #ffffff;
      border-radius: 20rpx 20rpx 20rpx 20rpx;
      padding: 42rpx;

      .box-item {
        margin-bottom: 40rpx;

        .title {
          font-weight: bold;
          font-size: 32rpx;
          color: #303445;
          margin-bottom: 11rpx;
        }
        .text-area {
          width: 618rpx;
          height: 350rpx;
          background: #f7f8fa;
          border-radius: 20rpx 20rpx 20rpx 20rpx;
          border: 1rpx solid #ebebeb;
          margin: 0 auto;
          margin-bottom: 40rpx;
          padding: 22rpx 38rpx;
          box-sizing: border-box;
        }
      }

      .btn {
        width: 630rpx;
        height: 100rpx;
        background: #577f49;
        box-shadow: 0rpx 20rpx 48rpx 1rpx rgba(87, 127, 73, 0.2);
        border-radius: 50rpx 50rpx 50rpx 50rpx;
        margin: 0 auto;
        color: #fff;
        text-align: center;
        line-height: 100rpx;
        margin-top: 100rpx;
      }
    }
  }
}

.nav {
  position: absolute;
  top: 0;
  left: 0;
  width: 750rpx;
  height: 478rpx;
}
</style>
