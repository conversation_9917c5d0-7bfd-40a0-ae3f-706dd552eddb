<script setup>
import { ref, watch, computed } from "vue"
import { onLoad, onShow } from "@dcloudio/uni-app"
import request from "@/utils/request"
import { inputStyle } from "@/utils/constants.js"
import { navTo } from "@/utils/utils"

const accountFormRef = ref(null)
const accountData = ref({
  phone: "",
  sms_code: "",
  captcha_code: "",
  password: "",
  password_confirmation: "",
})

const accountRules = {
  phone: {
    type: "string",
    required: true,
    message: "请填写登录账号",
  },
  sms_code: {
    type: "string",
    required: true,
    message: "请填写登录账号",
  },
  captcha_code: {
    type: "string",
    required: true,
    message: "请填写图形验证码",
  },
  password: {
    type: "string",
    required: true,
    message: "请填写密码",
  },
  password_confirmation: {
    type: "string",
    required: true,
    message: "请再次输入密码",
  },
}

const captcha = ref({})
const getCaptcha = () => {
  accountData.value.captcha_code = ""
  request({
    url: "/mini/get_captcha",
    method: "post",
  }).then((res) => {
    if (res.code === 200) {
      captcha.value = res.result
    }
  })
}

const smsCountdown = ref(0)
const sendSmsCode = () => {
  if (!accountData.value.phone) {
    uni.$u.toast("请输入手机号")
    return
  }
  if (!accountData.value.captcha_code) {
    uni.$u.toast("请输入图形验证码")
    return
  }

  request({
    url: "/mini/send_code",
    method: "post",
    data: {
      phone: accountData.value.phone,
      scene: "reset_password",
      captcha_key: captcha.value.key,
      captcha_code: accountData.value.captcha_code,
    },
  }).then((res) => {
    if (res.code === 200) {
      uni.$u.toast("发送成功")
      smsCountdown.value = 60
      const timer = setInterval(() => {
        smsCountdown.value--
        if (smsCountdown.value <= 0) {
          clearInterval(timer)
        }
      }, 1000)
    } else {
      getCaptcha()
    }
  })
}

const submit = () => {
  console.log("submit===================")
  accountFormRef.value.validate().then((valid) => {
    console.log(accountData.value)
    if (valid) {
      request({
        url: "/mini/reset_password",
        method: "post",
        data: accountData.value,
      }).then((res) => {
        if (res.code === 200) {
          navTo("/pages/result/accountResult", { title: "密码修改成功" })
        } else {
          accountData.value.sms_code = ""
          getCaptcha()
        }
      })
    }
  })
}

onLoad((options) => {
  getCaptcha()
})
</script>

<template>
  <view>
    <navHeader
      title=""
      bg="/static/image/register.png"
      :showLeft="true"
    ></navHeader>
    <view class="header">
      <view class="header-title"> 重置密码 </view>
    </view>
    <view class="body">
      <up-form
        labelPosition="top"
        labelWidth="auto"
        :model="accountData"
        :rules="accountRules"
        ref="accountFormRef"
      >
        <up-form-item label="手机号码" prop="phone">
          <up-input
            :customStyle="inputStyle"
            v-model="accountData.phone"
            border="none"
            placeholder="手机号码作为登录账号"
          ></up-input>
        </up-form-item>
        <up-form-item label="图形验证码" prop="captcha_code">
          <up-input
            :customStyle="inputStyle"
            v-model="accountData.captcha_code"
            disabledColor="#ffffff"
            placeholder="请输入图形验证码"
            placeholderClass="placeholder"
          ></up-input>
          <image :src="captcha.img" @click="getCaptcha" class="captcha"></image>
        </up-form-item>
        <up-form-item label="手机验证码" prop="sms_code">
          <up-input
            :customStyle="inputStyle"
            v-model="accountData.sms_code"
            border="none"
            placeholder="请输入手机验证码"
          ></up-input>
          <button
            class="sms-btn"
            @click="sendSmsCode"
            :disabled="smsCountdown > 0"
          >
            {{ smsCountdown > 0 ? smsCountdown + "s" : "获取验证码" }}
          </button>
        </up-form-item>
        <up-form-item label="设置密码" prop="password">
          <up-input
            :customStyle="inputStyle"
            v-model="accountData.password"
            disabledColor="#ffffff"
            placeholder="请输入密码"
            border="none"
            type="password"
          >
          </up-input>
        </up-form-item>
        <up-form-item label="再次输入密码" prop="password_confirmation">
          <up-input
            :customStyle="inputStyle"
            v-model="accountData.password_confirmation"
            disabledColor="#ffffff"
            placeholder="请再次输入密码"
            border="none"
            type="password"
          >
          </up-input>
        </up-form-item>
      </up-form>
    </view>
    <view class="footer">
      <button class="btn" @click="submit">提交</button>
    </view>
  </view>
</template>

<style>
page {
  background-color: #ffffff;
}
.u-form-item__body__right__content {
  position: relative;
}
</style>

<style lang="scss" scoped>
.body {
  padding: 60rpx;
}

.header {
  margin-top: 200rpx;

  .header-title {
    font-weight: 800;
    font-size: 48rpx;
    color: #21232c;
    text-align: center;
    font-style: normal;
    text-transform: none;
    margin-bottom: 50rpx;
  }
}
.footer {
  width: 750rpx;
  padding: 40rpx 60rpx;
  padding-bottom: calc(40rpx + env(safe-area-inset-bottom) / 2);
  background: #ffffff;
  box-shadow: 0rpx 3rpx 60rpx 1rpx rgba(0, 0, 0, 0.16);
  border-radius: 22rpx 22rpx 0rpx 0rpx;
  position: absolute;
  bottom: 0rpx;
  .btn {
    height: 100rpx;
    background: #577f49;
    box-shadow: 0rpx 20rpx 48rpx 1rpx rgba(87, 127, 73, 0.2);
    border-radius: 50rpx 50rpx 50rpx 50rpx;
    color: #fff;
    line-height: 100rpx;
  }
}

.sms-btn {
  font-size: 32rpx;
  color: #577f49;
  border: none;
  background: none;
  position: absolute;
  right: 34rpx;
  top: 50%;
  transform: translateY(-50%);
  z-index: 2;
}

.captcha {
  width: 233rpx;
  height: 62rpx;
  background: #577f49;
  border-radius: 15rpx 15rpx 15rpx 15rpx;
  position: absolute;
  right: 34rpx;
  top: 50%;
  transform: translateY(-50%);
  z-index: 2;
}
</style>
