<script setup>
import { ref, watch, computed } from "vue"
import { onLoad, onShow } from "@dcloudio/uni-app"
import request from "@/utils/request"
import dayjs from "dayjs"

const type = ref("exam") // assessment, exam
const paperId = ref(0)
const correctCount = ref(0)
const totalCount = ref(0)
const isPass = ref(false)
const score = ref(0)

const isSimulate = ref(false)

const reExam = () => {
  uni.navigateBack({
    delta: 1,
  })
}

const back = () => {
  uni.navigateBack({
    delta: 2,
  })
}

const backHome = () => {
  uni.reLaunch({
    url: "/pages/index/index",
  })
}

const activeCertificate = ref(null)

const posterData = computed(() => {
  return {
    css: {
      // 根节点若无尺寸，自动获取父级节点
      width: "620rpx",
      height: "400rpx",
      backgroundColor: "#ffffff",
      position: "relative",
    },
    views: [
      {
        name: "bg",
        type: "image",
        src: "https://beijing-zb.oss-cn-beijing.aliyuncs.com/certificate/20240818-123122.png",
        css: {
          width: "620rpx",
          height: "400rpx",
        },
      },
      {
        name: "no",
        type: "text",
        text: `证书编号：${activeCertificate.value?.no}`,
        css: {
          position: "absolute",
          top: "57rpx",
          right: "60rpx",
          fontSize: "10rpx",
          fontWeight: "500",
          color: "#717171",
        },
      },
      {
        name: "expiredDate",
        type: "text",
        text: dayjs(activeCertificate.value?.expired_at).format(
          "YYYY年MM月DD日"
        ),
        css: {
          position: "absolute",
          bottom: "90rpx",
          left: "78rpx",
          fontSize: "10rpx",
          fontWeight: "500",
          color: "#717171",
        },
      },
      {
        name: "name",
        type: "text",
        text: activeCertificate.value?.name,
        css: {
          position: "absolute",
          bottom: "90rpx",
          left: "0",
          width: "620rpx",
          textAlign: "center",
          fontSize: "24rpx",
          fontWeight: "500",
          color: "#717171",
        },
      },
      {
        name: "date",
        type: "text",
        text: dayjs(activeCertificate.value?.created_at).format(
          "YYYY年MM月DD日"
        ),
        css: {
          position: "absolute",
          bottom: "90rpx",
          right: "78rpx",
          fontSize: "10rpx",
          fontWeight: "500",
          color: "#717171",
        },
      },
    ],
  }
})
const imgUrl = ref("")
const onSuccess = (e) => {
  imgUrl.value = e
  console.log("onSuccess", "生成成功", imgUrl)
  // console.log("imgUrl", imgUrl.value)
}
const onFail = (e) => {
  console.log("onFail", "生成失败", e)
}

const loadCertificate = async () => {
  const res = await request({ url: "/mini/certificate" })
  let list = res.result.data
  if (list.length > 0) {
    activeCertificate.value = list[0]
  }
}

onLoad((options) => {
  if (options.type) {
    type.value = options.type
  }
  paperId.value = options.paperId ? parseInt(options.paperId) : 0
  correctCount.value = options.correctCount ? parseInt(options.correctCount) : 0
  totalCount.value = options.totalCount ? parseInt(options.totalCount) : 0
  isPass.value = options.isPass == "true"
  score.value = options.score ? parseInt(options.score) : 0

  isSimulate.value = options.isSimulate == "true"

  if (isPass.value && !isSimulate.value) {
    loadCertificate()
  }
})
</script>

<template>
  <view>
    <navHeader
      title="考核结果"
      bg="/static/image/bg.png"
      :showLeft="true"
    ></navHeader>
    <image
      v-if="isPass"
      src="/static/image/sprinkle-flowers.png"
      class="atmosphere"
    />
    <view class="body">
      <view class="result" v-if="isPass">
        <view class="score">
          <text> {{ score }} </text>分
        </view>
        <view class="title"> 合格 </view>

        <view class="certificate" v-if="type === 'exam' && !isSimulate">
          <view class="text">恭喜您获得征兵工作资格</view>
          <view class="image">
            <image class="certificate-img" :src="imgUrl" mode="aspectFill" />
          </view>
        </view>
      </view>

      <view class="result loser" v-else>
        <view class="score">
          <text>{{ score }}</text
          >分
        </view>
        <view class="title"> 不合格 </view>
      </view>

      <view class="footer">
        <template v-if="type === 'assessment'">
          <template v-if="isPass">
            <button class="btn green" @click="back">返回课程</button>
          </template>
          <template v-else>
            <button class="btn green" @click="reExam">重新考核</button>
            <button class="btn" @click="back">返回课程</button>
          </template>
        </template>
        <template v-if="type === 'exam'">
          <button class="btn" @click="backHome">返回首页</button>
        </template>
      </view>
    </view>
  </view>

  <l-painter
    custom-style="position: fixed; left: 200%"
    v-if="activeCertificate"
    path-type="url"
    isCanvasToTempFilePath
    :board="posterData"
    @success="onSuccess"
    @fail="onFail"
  />
</template>

<style>
page {
  background-color: #ffffff;
}
</style>

<style lang="scss" scoped>
.atmosphere {
  width: 624rpx;
  height: 140rpx;
  position: absolute;
  top: 57rpx;
  left: 63rpx;
  z-index: 0;
}

.body {
  text-align: center;
  padding: 0 60rpx;
  padding-top: 220rpx;

  .text {
    font-weight: 400;
    font-size: 28rpx;
    color: #9ca3b5;
  }
  // .result {
  //   text-align: center;
  //   .loser {
  //     background: #9ca3b5;
  //   }
  //   .score {
  //     color: #9ca3b5;
  //     font-size: 32rpx;

  //     text {
  //       font-weight: bold;
  //       font-size: 106rpx;
  //       color: #577f49;
  //     }
  //   }
  //   .win {
  //     background: rgba(87, 127, 73, 0.16);
  //     color: #577f49;
  //   }
  //   text {
  //     font-weight: bold;
  //     font-size: 106rpx;
  //   }

  //   .title {
  //     margin: 40rpx auto 10rpx;
  //     width: 176rpx;
  //     height: 64rpx;
  //     line-height: 64rpx;
  //     border-radius: 32rpx 32rpx 32rpx 32rpx;
  //     font-weight: bold;
  //     font-size: 38rpx;
  //     background: rgba(87, 127, 73, 0.16);
  //     text-align: center;
  //     color: #577f49;
  //   }
  // }

  .result {
    margin-top: 120rpx;
    min-height: 700rpx;

    .score {
      font-size: 32rpx;
      color: #9ca3b5;

      text {
        font-weight: bold;
        font-size: 106rpx;
        color: #577f49;
      }
    }

    .title {
      margin: 40rpx auto 10rpx;
      width: 176rpx;
      height: 64rpx;
      line-height: 64rpx;
      border-radius: 32rpx 32rpx 32rpx 32rpx;
      font-weight: bold;
      font-size: 38rpx;
      background: rgba(87, 127, 73, 0.16);
      text-align: center;
      color: #577f49;
    }

    &.loser {
      .score {
        text {
          color: #9ca3b5;
        }
      }

      .title {
        background: #9ca3b5;
        color: #ffffff;
      }
    }
  }
  .image {
    width: 99rpx;
    height: 99rpx;
    margin: 0 auto;
    margin-top: 200rpx;
    img {
      width: 100%;
      height: 100%;
    }
  }
}

.certificate {
  margin-top: 104rpx;
  // margin-bottom: 40rpx;

  .text {
    font-weight: bold;
    font-size: 38rpx;
    color: #06121e;
    text-align: center;
  }

  .image {
    width: 584rpx;
    height: 376rpx;
    margin: 56rpx auto;
    border-radius: 16rpx;
    overflow: hidden;

    image {
      width: 100%;
      height: 100%;
    }
  }
}

.footer {
  // width: 750rpx;
  background: #ffffff;
  border-radius: 22rpx 22rpx 0rpx 0rpx;
  // margin-top: 200rpx;

  .btn {
    width: 630rpx;
    height: 100rpx;
    background: #ffffff;
    color: #21232c;
    border-radius: 50rpx 50rpx 50rpx 50rpx;
    border: 1rpx solid #e1e1e1;
    line-height: 100rpx;
    margin-bottom: 45rpx;
  }
  .green {
    box-shadow: 0rpx 20rpx 48rpx 1rpx rgba(87, 127, 73, 0.2);
    color: #fff;
    background: #577f49;
  }
}
</style>
