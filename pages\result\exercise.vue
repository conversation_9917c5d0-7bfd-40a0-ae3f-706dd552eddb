<script setup>
import { ref, watch, computed } from "vue"
import { onLoad, onShow } from "@dcloudio/uni-app"
import request from "@/utils/request"
import { navTo } from "@/utils/utils"

const feedbackType = ref("final") // final, instant
const paperId = ref(0)
const roundId = ref(0)
const subChapterId = ref(0)

const correctCount = ref(0)
const totalCount = ref(0)

const accuracy = computed(() => {
  return ((correctCount.value / totalCount.value) * 100).toFixed(0)
})

const goAnalysis = () => {
  navTo("/pages/exam/analysis", { roundId: roundId.value }, "redirectTo")
}

const reExercise = () => {
  navTo(
    "/pages/exam/exam",
    {
      paperId: paperId.value,
      type: "practice",
      subChapterId: subChapterId.value,
    },
    "redirectTo"
  )
}

const goCourse = () => {
  uni.navigateBack({
    delta: 1,
  })
}

onLoad((options) => {
  feedbackType.value = options.feedbackType || "final"
  paperId.value = options.paperId ? parseInt(options.paperId) : 0
  roundId.value = options.roundId ? parseInt(options.roundId) : 0
  correctCount.value = options.correctCount ? parseInt(options.correctCount) : 0
  totalCount.value = options.totalCount ? parseInt(options.totalCount) : 0
  subChapterId.value = options.subChapterId ? parseInt(options.subChapterId) : 0
})
</script>

<template>
  <view>
    <navHeader
      title="练习结果"
      bg="/static/image/bg.png"
      :showLeft="true"
    ></navHeader>
    <view class="header"> </view>
    <view class="body">
      <view class="title"> 练习结束 </view>
      <view class="text">
        共做
        {{ totalCount }}
        道题，正确率
        {{ accuracy }}
        %
      </view>

      <view class="result">
        <view class="bgbox">
          <view class="bgbox-title"> 回答正确 </view>
          <view class="box agree">
            {{ correctCount }}
          </view>
        </view>

        <view class="bgbox wrong">
          <view class="bgbox-title"> 回答错误 </view>
          <view class="box close">
            {{ totalCount - correctCount }}
          </view>
        </view>
      </view>

      <view class="footer">
        <template v-if="feedbackType == 'final'">
          <button class="btn green" @click="goAnalysis">查看答案解析</button>
          <button class="btn" @click="reExercise">重新练习</button>
          <button class="btn" @click="goCourse">返回课程</button>
        </template>
        <template v-else>
          <button class="btn green" @click="goCourse">返回课程</button>
          <button class="btn" @click="reExercise">重新练习</button>
        </template>
      </view>
    </view>
  </view>
</template>

<style>
page {
  background: #ffffff;
}
</style>

<style lang="scss" scoped>
.body {
  padding: 0 60rpx;
  text-align: center;
  padding-top: 120rpx;

  .text {
    font-weight: 400;
    font-size: 28rpx;
    color: #9ca3b5;
  }
  .result {
    margin-top: 96rpx;
    display: flex;
    justify-content: space-between;
    padding: 0 17rpx;

    .bgbox {
      background-color: #577f49;
      border-radius: 20rpx 20rpx 20rpx 20rpx;

      &-title {
        font-size: 28rpx;
        color: #fff;
        padding: 6rpx 0;
      }

      &.wrong {
        background-color: #9ca3b5;
      }
    }
    .box {
      width: 275rpx;
      height: 145rpx;
      border-radius: 20rpx 20rpx 20rpx 20rpx;
      text-align: center;
      line-height: 145rpx;
    }
    .agree {
      border: 2rpx solid #577f49;
      background: #fafff8;
      font-weight: bold;
      font-size: 62rpx;
      color: #577f49;
    }
    .close {
      border: 2rpx solid #9ca3b5;
      background: #f7f8fa;
      font-weight: bold;
      font-size: 62rpx;
      color: #9ca3b5;
    }
  }
  .image {
    width: 99rpx;
    height: 99rpx;
    margin: 0 auto;
    margin-top: 200rpx;
    img {
      width: 100%;
      height: 100%;
    }
  }
}
.title {
  font-weight: 800;
  font-size: 48rpx;
  color: #21232c;
  text-align: center;
  font-style: normal;
  text-transform: none;
  margin-bottom: 10rpx;
}
.header {
  margin-top: 200rpx;
}
.footer {
  width: 750rpx;
  background: #ffffff;
  border-radius: 22rpx 22rpx 0rpx 0rpx;
  margin-top: 130rpx;

  .btn {
    width: 630rpx;
    height: 100rpx;
    background: #ffffff;
    color: #577f49;
    border-radius: 50rpx 50rpx 50rpx 50rpx;
    border: 1rpx solid #577f49;
    line-height: 100rpx;
    margin-bottom: 38rpx;
  }
  .green {
    box-shadow: 0rpx 20rpx 48rpx 1rpx rgba(87, 127, 73, 0.2);
    color: #fff;
    background: #577f49;
  }
}
</style>
