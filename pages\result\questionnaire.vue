<script setup>
import { ref, watch, computed } from "vue"
import { onLoad, onShow } from "@dcloudio/uni-app"

const role = ref("worker")
const status = ref("success")

const back = () => {
  uni.navigateBack({
    delta: 1,
  })
}
onLoad((options) => {
  if (options.role) {
    role.value = options.role
  }
  if (options.status) {
    status.value = options.status
  }
})
</script>

<template>
  <view>
    <navHeader
      title=""
      bg="/static/image/bg.png"
      :showLeft="role === 'worker'"
    ></navHeader>
    <view class="body">
      <template v-if="status === 'success'">
        <image
          src="/static/image/result-right.png"
          mode=""
          class="image"
        ></image>
        <view class="title"> 提交成功 </view>
        <view class="footer" v-if="role === 'worker'">
          <button class="btn" @click="back">返回问卷列表</button>
        </view>
      </template>
      <empty v-else> 调查已结束 </empty>
    </view>
  </view>
</template>

<style>
page {
  background: #ffffff;
}
</style>

<style lang="scss" scoped>
.body {
  padding: 0 60rpx;
  padding-top: 220rpx;
  text-align: center;
  .image {
    width: 99rpx;
    height: 99rpx;
    margin: 0 auto;
    margin-top: 200rpx;
  }
}
.title {
  margin-top: 40rpx;

  font-weight: 800;
  font-size: 48rpx;
  color: #21232c;
  text-align: center;
  font-style: normal;
  text-transform: none;
}
.footer {
  margin-top: 194rpx;
  background: #ffffff;

  .btn {
    width: 630rpx;
    height: 100rpx;
    background: #577f49;
    box-shadow: 0rpx 20rpx 48rpx 1rpx rgba(87, 127, 73, 0.2);
    border-radius: 50rpx 50rpx 50rpx 50rpx;
    color: #fff;
    line-height: 100rpx;
    margin: 45rpx auto;
  }
}
</style>
