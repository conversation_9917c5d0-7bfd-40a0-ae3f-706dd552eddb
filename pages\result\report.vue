<script setup>
import { ref, watch, computed } from "vue"
import { onLoad, onShow } from "@dcloudio/uni-app"
import { navTo } from "@/utils/utils"

onLoad((options) => {
  console.log(options)
})
</script>

<template>
  <view>
    <navHeader
      title=""
      bg="/static/image/bg.png"
      color="#000"
      :showLeft="true"
    ></navHeader>
    <view class="header"> </view>
    <view class="body">
      <image src="/static/image/result-right.png" mode="" class="image"></image>
      <view class="title"> 提交成功 </view>
      <view class="desc"> 稍后可在“我的-我的举报”中查看反馈 </view>
    </view>
    <view class="footer">
      <button class="btn" @click="navTo('/pages/report/list', {}, 'redirectTo')">
        查看我的举报
      </button>
    </view>
  </view>
</template>

<style>
page {
  background: #ffffff;
}
</style>

<style lang="scss" scoped>
.body {
  padding: 60rpx;
  text-align: center;
  .image {
    width: 99rpx;
    height: 99rpx;
    margin: 0 auto;
    margin-top: 200rpx;
  }
  .desc {
    font-weight: 400;
    font-size: 28rpx;
    color: #9ca3b5;
    text-align: center;
    font-style: normal;
    text-transform: none;
  }
}
.title {
  margin-top: 40rpx;

  font-weight: 800;
  font-size: 48rpx;
  color: #21232c;
  text-align: center;
  font-style: normal;
  text-transform: none;
  margin-bottom: 70rpx;
}
.header {
  margin-top: 200rpx;
}
.footer {
  padding: 0 60rpx;

  .btn {
    width: 630rpx;
    height: 100rpx;
    background: #577f49;
    box-shadow: 0rpx 20rpx 48rpx 1rpx rgba(87, 127, 73, 0.2);
    border-radius: 50rpx 50rpx 50rpx 50rpx;
    color: #fff;
    line-height: 100rpx;
    margin-top: 45rpx;
  }
}
</style>
