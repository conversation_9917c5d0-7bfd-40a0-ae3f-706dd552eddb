<script setup>
import { ref, watch, computed } from "vue"
import { onLoad, onShow } from "@dcloudio/uni-app"
import { useUserStore } from "@/store/user"
import request from "@/utils/request"
import dayjs from "dayjs"

const userStore = useUserStore()
const userInfo = computed(() => userStore.userInfo)

const checked = ref("detail")
const changeTab = (type) => {
  checked.value = type
}

const filterRef = ref()
const filterOptions = computed(() => [
  {
    value: "department",
    label: "本单位",
  },
  {
    value: "district",
    label: "全区",
  },
  {
    value: "city",
    label: "全市",
  },
])
const currentFilter = ref("department")
const onFilterChange = (e) => {
  loadRankingList()
}

const rankingList = ref()
const loadRankingList = () => {
  request({
    url: "/mini/point_rank",
    data: {
      scope: currentFilter.value,
    },
  }).then((res) => {
    rankingList.value = res.result.data
  })
}

const myPointsList = ref()
const loadMyPoints = () => {
  request({
    url: "/mini/point",
  }).then((res) => {
    myPointsList.value = res.result.data
  })
}

onLoad((options) => {
  console.log(options)
  if (options.tab) {
    checked.value = options.tab
  }
  loadRankingList()
  loadMyPoints()
})
</script>

<template>
  <view class="page">
    <navHeader
      bg="/static/image/bg.png"
      title="我的积分"
      :showLeft="true"
      color="#000"
    ></navHeader>

    <view class="header">
      <view class="banner">
        <image src="/static/image/score-bg.png" mode=""></image>
        <view class="content">
          <view class="no1"> {{ userInfo.points }} </view>
          <view class="title"> - 我的当前积分 - </view>
        </view>
      </view>
    </view>
    <view class="body">
      <view class="title">
        <view
          :class="checked == 'detail' ? 'checked' : ''"
          @click="changeTab('detail')"
        >
          <view class=""> 积分明细 </view>
          <view class="green" v-if="checked == 'detail'"> </view>
        </view>
        <view
          :class="checked == 'ranking' ? 'checked' : ''"
          @click="changeTab('ranking')"
        >
          <view class=""> 积分排名 </view>
          <view class="green" v-if="checked == 'ranking'"> </view>
        </view>
      </view>

      <view class="list" v-if="checked == 'detail'">
        <view
          class="list-item"
          v-for="(item, index) in myPointsList"
          :key="index"
        >
          <view class="left">
            <view class="name"> {{ item.remark }} </view>
            <view class="date">
              {{ dayjs(item.created_at).format("YYYY-MM-DD HH:mm") }}
            </view>
          </view>
          <view class="score"> + {{ item.point }} </view>
        </view>
      </view>
      <view class="list" v-if="checked == 'ranking'">
        <u-dropdown
          ref="filterRef"
          active-color="#577f49"
          inactive-color="#bdc4ce"
        >
          <u-dropdown-item
            v-model="currentFilter"
            :title="
              filterOptions.find((item) => item.value == currentFilter).label
            "
            :options="filterOptions"
            @change="onFilterChange"
          ></u-dropdown-item>
        </u-dropdown>

        <view class="ranking-list">
          <view class="item" v-for="(item, index) in rankingList" :key="index">
            <view class="no-name">
              <view class="no">
                <image
                  :src="`/static/image/rank-${index + 1}.png`"
                  mode=""
                  v-if="index < 3"
                ></image>
                <text v-else> {{ index + 1 }} </text>
              </view>
              <view class="name"> {{ item.name }} </view>
            </view>

            <text class="score"> {{ item.points }} </text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss">
page {
  background-color: #ffffff;
}

.u-dropdown__menu {
  height: 60rpx !important;
  justify-content: center !important;
}

.u-dropdown__menu__item {
  .u-dropdown__menu__item__text {
    color: #577f49 !important;
  }

  flex: unset !important;
  min-width: 165rpx;
  height: 60rpx;
  background: #f7f8fa;
  border-radius: 37rpx 37rpx 37rpx 37rpx;
  padding: 20rpx 40rpx;
}
</style>

<style lang="scss" scoped>
.page {
  background: #ffffff;
}
.header {
  margin-top: 200rpx;

  .banner {
    width: 702rpx;
    height: 250rpx;
    margin: 0 auto;
    position: relative;
    padding: 50rpx;

    image {
      width: 100%;
      height: 100%;
      object-fit: cover;
      position: absolute;
      border-radius: 20rpx 20rpx 20rpx 20rpx;
      top: 0;
      left: 0;
      overflow: hidden;
    }
  }

  .content {
    display: flex;
    flex-direction: column;
    justify-content: space-around;

    .no1 {
      font-weight: bold;
      font-size: 68rpx;
      color: #ffffff;
      text-align: center;
      font-style: normal;
      text-transform: none;
      z-index: 1;
    }

    .title {
      color: #ffffff;
      z-index: 1;
      font-size: 28rpx;
      text-align: center;
      font-style: normal;
      text-transform: none;
      opacity: 0.6;
      margin-top: 16rpx;
    }
  }
}
.checked {
  color: #577f49;
}
.body {
  padding: 24rpx;

  width: 100%;

  .title {
    font-size: 30rpx;
    color: rgba(0, 0, 0, 0.9);
    text-align: center;
    font-style: normal;
    text-transform: none;
    display: flex;
    text-align: center;
    width: 60%;
    margin: 30rpx auto;
    justify-content: space-evenly;

    .green {
      width: 25rpx;
      height: 0rpx;
      background: #ffffff;
      border-bottom: 6rpx solid #577f49;
      border-radius: 6rpx;
      margin: 10rpx auto;
    }
  }

  .list {
    .item {
      margin: 0 24rpx;
      padding: 44rpx 0;
      justify-content: space-between;
      border-bottom: 2rpx solid #f8f8f8;
      display: flex;

      &:last-child {
        border-bottom: none;
      }

      .no-name {
        display: flex;
        align-items: center;
        margin-right: 50rpx;

        .no {
          width: 42rpx;
          height: 42rpx;
          display: flex;
          justify-content: center;
          align-items: center;
          margin-right: 50rpx;

          image {
            width: 100%;
            height: 100%;
          }

          text {
            font-weight: 500;
            font-size: 28rpx;
            color: #577f49;
          }
        }
        .name {
          font-weight: 500;
          font-size: 32rpx;
          color: rgba(0, 0, 0, 0.9);

          text-align: left;
        }
      }

      .score {
        display: flex;
        flex-direction: column;
        justify-content: center;
        font-weight: 500;
        font-size: 28rpx;
        color: #577f49;
      }
    }
    .list-item {
      display: flex;
      margin: 0 24rpx;
      padding: 20rpx 0;
      justify-content: space-between;
      border-bottom: 2rpx solid rgba(0, 0, 0, 0.1);

      &:last-child {
        border-bottom: none;
      }

      .left {
        .name {
          font-weight: 500;
          font-size: 32rpx;
          color: rgba(0, 0, 0, 0.9);
        }
        .date {
          margin-top: 4rpx;
          font-weight: 400;
          font-size: 26rpx;
          color: #bdc4ce;
        }
      }
      .score {
        display: flex;
        flex-direction: column;
        justify-content: center;
        font-weight: 500;
        font-size: 28rpx;
        color: #577f49;
      }
    }
  }
}
</style>
