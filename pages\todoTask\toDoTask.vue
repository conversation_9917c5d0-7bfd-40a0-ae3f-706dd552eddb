<script setup>
import { ref, watch, computed, nextTick } from "vue"
import {
  onLoad,
  onShow,
  onPullDownRefresh,
  onReachBottom,
} from "@dcloudio/uni-app"
import { checkPermissionOr, navTo } from "@/utils/utils"
import request from "@/utils/request"
import dayjs from "dayjs"
import taskContent from "/pages/office/components/taskContent.vue"
import questionnaireContent from "/pages/office/components/questionnaireContent.vue"
import reportContent from "/pages/report/components/canHandleContent.vue"
import { useUserStore } from "@/store/user"
import { permissionConst } from "@/utils/permissionConst"

const userStore = useUserStore()

const permissions = computed(() => userStore.permissions)

const taskContentRef = ref(null)
const questionnaireContentRef = ref(null)
const reportContentRef = ref(null)

const currentTabIndex = ref(0)
const tabs = computed(() => [
  {
    title: "我的任务",
    type: "task",
    component: taskContentRef,
    count: taskContentRef?.value?.total || 0,
    show: 1,
  },
  {
    title: "调查问卷",
    type: "questionnaire",
    component: questionnaireContentRef,
    count: questionnaireContentRef?.value?.total || 0,
    show: 1,
  },
  {
    title: "举报处理",
    type: "report",
    component: reportContentRef,
    count: reportContentRef?.value?.total || 0,
    show: checkPermissionOr([permissionConst.mini_report_reply.status]),
  },
])

const currentTab = computed(() => tabs.value[currentTabIndex.value])
const changeTab = (index) => {
  currentTabIndex.value = index
  nextTick(() => {
    refresh()
  })
}

const refresh = () => {
  currentTab.value?.component.value?.refresh()
}

const loadMore = () => {
  currentTab.value?.component.value?.loadPage()
}

onLoad((options) => {
  uni.hideTabBar()
  const { tab } = options
  if (tab) {
    currentTabIndex.value = tabs.value.findIndex((item) => item.type === tab)
  }
  nextTick(() => {
    tabs.value.forEach((item, index) => {
      item.component.value?.loadPage()
    })
  })
})

onShow(() => {
  if (currentTab.value?.component.value?.list.length) {
    refresh()
  }
  // tabs.value.forEach((item, index) => {
  //   item.component.value?.refresh()
  // })
})

onPullDownRefresh(() => {
  tabs.value.forEach((item, index) => {
    item.component.value?.refresh()
  })
})

onReachBottom(() => {
  loadMore()
})
</script>

<template>
  <view>
    <view class="header">
      <view class="title">
        <template v-for="(item, index) in tabs" :key="index">
          <view
            :class="currentTabIndex == index ? 'checked' : ''"
            @click="changeTab(index)"
            v-if="item.show"
          >
            <view class=""> {{ item.title }} ({{ item.count }})</view>
            <view class="green" v-if="currentTabIndex == index"> </view>
          </view>
        </template>
      </view>
    </view>

    <view class="list">
      <taskContent
        ref="taskContentRef"
        v-show="currentTab.type === 'task'"
        :queryParams="{ status: 'published', finish: 0 }"
      />
      <questionnaireContent
        ref="questionnaireContentRef"
        v-show="currentTab.type === 'questionnaire'"
        :queryParams="{ status: 'running', finish: 0 }"
      />
      <reportContent
        v-if="checkPermissionOr([permissionConst.mini_report_reply.status])"
        ref="reportContentRef"
        v-show="currentTab.type === 'report'"
        :queryParams="{ status: 'created' }"
      />

      <view style="height: 180rpx"></view>
    </view>
    <tabNav index="1" />
  </view>
</template>

<style lang="scss" scoped>
.header {
  background-color: #fff;
}
.title {
  font-weight: 400;
  font-size: 30rpx;
  color: #39455b;

  text-align: center;
  font-style: normal;
  text-transform: none;
  display: flex;
  text-align: center;
  margin: 0 auto;
  padding-top: 50rpx;
  justify-content: space-evenly;

  .green {
    width: 25rpx;
    height: 0rpx;
    background: #ffffff;
    border-bottom: 6rpx solid #577f49;
    border-radius: 6rpx;
    margin: 10rpx auto;
  }
}

.list {
  padding: 24rpx;
}
</style>
