// stores/counter.js
import { defineStore } from "pinia"
import request from "@/utils/request"

export const useCommonStore = defineStore("common", {
  state: () => ({
    baseUrl: "http://10.0.0.6:20088",
    socketUrl: "ws://10.0.0.6:8008",
    // baseUrl: "http://office.mengya.link:40004",
    // socketUrl: "ws://office.mengya.link:40004/ai-chat",
    // baseUrl: "https://work.zxhckj.cn",
    // socketUrl: "wss://work.zxhckj.cn/ai-chat",

    navHeight: 0,
    config: {},
    // categories: [],
  }),
  actions: {
    setNavHeight(height) {
      this.navHeight = height
    },
    setConfig(config) {
      this.config = config
    },
    // setCategories(categories) {
    //   this.categories = categories
    // },
    fetchConfig() {
      return new Promise((resolve) => {
        request({
          url: "/mini/config",
          method: "get",
        }).then((res) => {
          this.setConfig(res.result)
          resolve(res)
        })
      })
    },
    // fetchCategories() {
    //   return new Promise((resolve) => {
    //     request({
    //       url: "/mini/categories",
    //       method: "get",
    //     }).then((res) => {
    //       this.setCategories(res.result)
    //       resolve(res)
    //     })
    //   })
    // },
  },
  getters: {
    currentStageId: (state) => state.config?.current_stage_id || 0,
    noticeCategoryId: (state) => state.config?.notice_category_id || 0,
    platformNoticeCategoryId: (state) =>
      state.config?.platform_notice_category_id || 0,
    policyCategoryId: (state) => state.config?.policy_category_id || 0,
    forwardDefaultImage: (state) =>
      state.config?.forward_default_image?.full_path || "",
    errorQuestionRoundQuestionCount: (state) =>
      state.config?.error_question_round_question_count || 10,
    userAgreementId: (state) => state.config?.user_agreement_id || 0,
  },
})
