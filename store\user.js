import { defineStore } from "pinia"
import request from "@/utils/request.js"

export const useUserStore = defineStore("user", {
  state: () => ({
    token: "",
    userInfo: {},
    remindCount: 0,
  }),
  // 也可以这样定义
  // state: () => ({ count: 0 })
  actions: {
    setToken(token) {
      uni.setStorageSync("token", token)
      this.token = token
    },
    setUserInfo(info) {
      uni.setStorageSync("userInfo", info)
      this.userInfo = info
    },
    setRemindCount(count) {
      this.remindCount = count
    },
    login(params) {
      return new Promise((resolve, reject) => {
        request({
          url: "/mini/login",
          method: "POST",
          data: params,
        }).then((res) => {
          console.log(res)
          if (res.code == 200) {
            this.setToken(res.result.token)
            this.setUserInfo(res.result.user)
            resolve(res)
          } else {
            reject(res)
          }
        })
      })
    },
    getUserInfo() {
      return new Promise((resolve, reject) => {
        request({
          url: "/mini/info",
          method: "GET",
        })
          .then((res) => {
            resolve(res)
            if (res.code == 200) {
              this.setUserInfo(res.result)
            }
          })
          .catch((err) => {
            reject(err)
          })
      })
    },
    logout() {
      uni.removeStorageSync("token")
      uni.removeStorageSync("userInfo")
      this.token = ""
      this.userInfo = {}
    },
    getRemindCount() {
      return new Promise((resolve, reject) => {
        request({
          url: "/mini/statistics/remind_count",
          method: "GET",
        })
          .then((res) => {
            if (res.code == 200) {
              this.setRemindCount(res.result?.notification_unread_count || 0)
              resolve(res)
            } else {
              reject(res)
            }
          })
          .catch((err) => {
            reject(err)
          })
      })
    },
  },
  getters: {
    isLogin() {
      return !!this.token
    },
    permissions() {
      return this.userInfo.permission_slugs || []
    },
    roleSlugs() {
      return this.userInfo.role_slugs || []
    },
    haveToFaceAuth() {
      return this.userInfo?.face_rec_enable == 1
    },
  },
})
