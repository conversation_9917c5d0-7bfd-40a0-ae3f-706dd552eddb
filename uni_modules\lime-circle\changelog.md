## 0.1.6（2024-04-27）
- feat: 增加`gapDegree`属性，可设置缺口的角度大小
- feat: 增加`gapPosition`属性，可设置缺口位置
## 0.1.5（2023-12-16）
- chore: hb升级到3.99后支持vue2 nvue ts
## 0.1.4（2023-08-10）
- chore: 文档更新,因文档的`dashboard`写错了
## 0.1.3（2023-06-28）
- chore: 文档更新
## 0.1.2（2023-06-13）
- feat: 增加 canvas 实现方式
## 0.1.1（2023-06-08）
- feat: 去掉`units.ts`改用`lime-shared`
## 0.1.0（2023-04-13）
- chore: 修改文档中的错误
## 0.0.9（2023-04-13）
- chore: 文档增加vue2使用方法示例
## 0.0.8（2023-04-04）
- chore: 去掉 script-setup 语法糖
- chore: 文档增加 vue2 使用方法
## 0.0.7（2022-11-09）
- feat: 破坏性更新，由原来的canvas改为css3实现
## 0.0.6（2022-11-04）
- feat: 增加使用uni-popup示例
## 0.0.5（2022-11-03）
- fix: 修复 有毛点问题（因清空尺寸不对）
## 0.0.4（2022-11-02）
- feat: 增加  `delay` props 延时渲染，用于把组件放在弹窗时，无法正确获取尺寸时。
## 0.0.3（2022-11-02）
更新文档
## 0.0.2（2022-11-01）
更新文档
## 0.0.1（2022-11-01）
首次上传
