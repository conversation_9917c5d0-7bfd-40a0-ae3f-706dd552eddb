<!DOCTYPE html>
<html lang="zh">
	<head>
		<meta charset="UTF-8">
		<meta http-equiv="X-UA-Compatible" content="IE=edge">
		<meta name="viewport" content="width=device-width, initial-scale=1.0">
		<title>lime-circle</title>
		<style>
			/* @property --l-circle-percent {
			  syntax: '<percentage>';
			  initial-value: 25%;
			  inherits: false;
			} */
			html,
			body {
				padding: 0;
				margin: 0;
				width: 100%;
				height: 100%;
			}

			.l-circle {
				position: relative;
				width: 100%;
				height: 100%;
			}

			.l-circle__trail,
			.l-circle__stroke,.l-circle__stroke-line {
				position: absolute;
				width: 100%;
				height: 100%;
				border-radius: 50%;
			}
			.l-circle__stroke-line {
				z-index: 2;
				background: var(--l-background);
			}
			.l-circle__inner {
				position: absolute;
				left: 0;
				right: 0;
				top: 0;
				bottom: 0;
				align-items: center;
				justify-content: center;
				display: flex;
			}

			.is-round .l-circle__trail .cap {
				position: absolute;
				display: block;
				width: var(--l-circle-trail-cap-size, 6px);
				height: var(--l-circle-trail-cap-size, 6px);
				background-color: var(--l-circle-trail-cap-color, #ddd);
				z-index: 10;
				border-radius: 50%;
			}

			.is-round .l-circle__trail .cap.start {
				left: var(--l-circle-trail-cap-start-x, 14%);
				top: var(--l-circle-trail-cap-start-y, 81%);
			}

			.is-round .l-circle__trail .cap.end {
				left: var(--l-circle-trail-cap-end-x, 14%);
				top: var(--l-circle-trail-cap-end-y, 81%);
			}

			.is-round .l-circle__stroke .cap {
				position: absolute;
				display: block;
				width: var(--l-circle-stroke-cap-size, 6px);
				height: var(--l-circle-stroke-cap-size, 6px);
				background-color: var(--l-circle-stroke-cap-start-color, #ddd);
				border-radius: 50%;
				transition: opacity 350ms ease-out;
				opacity: var(--l-circle-stroke-cap-opacity, 0);
			}

			.is-round .l-circle__stroke .cap.start {
				left: var(--l-circle-stroke-cap-start-x, 14%);
				top: var(--l-circle-stroke-cap-start-y, 81%);
				background-color: var(--l-circle-stroke-cap-start-color, #ddd);
			}

			.is-round .l-circle__stroke .cap.end {
				left: var(--l-circle-stroke-cap-end-x, 14%);
				top: var(--l-circle-stroke-cap-end-y, 81%);
				z-index: -1;
				background-color: var(--l-circle-stroke-cap-end-color, #ddd);
			}

			.clockwise .l-circle__inner {
				transform: scale(-1, 1);
			}
		</style>
	</head>
	<body>
		<canvas id="limeCanvas" class="l-circle"></canvas>
		<div id="limeCss" class="l-circle">
			<div class="l-circle__trail">
				<i class="cap start"></i>
				<i class="cap end"></i>
			</div>
			<div class="l-circle__stroke">
				<div class="l-circle__stroke-line"></div>
				<i class="cap start"></i>
				<i class="cap end"></i>
			</div>
		</div>
		<script type="text/javascript" src="./uni.webview.1.5.3.js"></script>
		<script type="text/javascript" src="./circle.min.js"></script>
		<script>
			let useCanvas = !CSS.supports('background', 'conic-gradient(#000, #fff)');
			const canvas = document.querySelector('#limeCanvas');
			const css = document.querySelector('#limeCss');
			let circle = null

			// function toStyle(obj) {
			// 	let str = ''
			// 	for (const key in obj) {
			// 		str += `${key}:${obj[key]};`
			// 	}
			// 	return str
			// }

			function setStyle(type, style) {
				
				let el = null;
				if (!type) {
					// const trail = document.querySelector('.l-circle__trail')
					el = document.querySelector('.l-circle__trail')
					// trail.style = toStyle(style)
				} else {
					// const stroke = document.querySelector('.l-circle__stroke')
					el = document.querySelector('.l-circle__stroke')
					// stroke.style = toStyle(style)
				}
				
				if (el != null) {
					for (let key in style) {
						el.style.setProperty(key, style[key]);
					}
				}
			}
			// type 0删除 1增加
			function setClass(target, className, type) {
				const box = document.querySelector(target);
				if (!type) {
					box.classList.remove(className)
				} else {
					box.classList.add(className)
				}
			}

			function postMessage(data) {
				uni.postMessage({
					data
				});
			};

			function emit(event, data) {
				postMessage({
					event,
					data
				});
			};

			function setOption(data) {
				if (circle) circle.setOption(data)
			}

			function clear() {
				if (circle) circle.clear()
			}

			function play(v) {
				// alert(v)
				if (circle) circle.play(v)
			}

			function stop() {
				if (circle) circle.stop()
			}

			function dispose() {
				if (circle) circle.dispose()
			}
			function init(type){
				useCanvas = type;
				if (useCanvas) {
					css.remove()
					circle = new lime.Circle(canvas, {
						pixelRatio: window.devicePixelRatio,
						run: (current) => {
							emit('progress', current);
						}
					})
					
				} else {
					canvas.remove()
					setStyle(0, {
						"mask": "radial-gradient(transparent 65.5px, #000 66px)",
						"-webkit-mask": "radial-gradient(transparent 65.5px, #000 66px)",
						"background": "conic-gradient(from 225deg, #ddd 0%, #ddd 75%, transparent 75%, transparent 100%)",
						"--l-circle-trail-cap-start-x": "20.209632098128225px",
						"--l-circle-trail-cap-start-y": "117.79036790187178px",
						"--l-circle-trail-cap-end-x": "117.79036790187178px",
						"--l-circle-trail-cap-end-y": "117.79036790187178px",
						"--l-circle-trail-cap-color": "#ddd",
						"--l-circle-trail-cap-size": "6px"
					})
				
					setStyle(1, {
						"mask": "radial-gradient(transparent 57.5px, #000 58px)",
						"-webkit-mask": "radial-gradient(transparent 57.5px, #000 58px)",
						"background": "conic-gradient( transparent 0%, #2db7f5 0%, #2db7f5 var(--l-circle-circle-percent), transparent var(--l-circle-circle-percent), transparent 100%)",
						"--l-circle-circle-percent": "0%",
						"--l-circle-circle-stroke-cap-start-color": "#2db7f5",
						"--l-circle-circle-stroke-cap-end-color": "#2db7f5",
						"--l-circle-circle-stroke-cap-start-x": "61.00000000000001px",
						"--l-circle-circle-stroke-cap-start-y": "0px",
						"--l-circle-circle-stroke-cap-end-x": "0",
						"--l-circle-circle-stroke-cap-end-y": "0",
						"--l-circle-circle-stroke-cap-size": "6px",
						"--l-circle-circle-stroke-cap-opacity": 0
					})
				}
			}
			
			emit('init', {
				useCanvas
			})
		</script>
	</body>
</html>