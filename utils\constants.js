export const categoryTemplateMap = {
  article: {
    status: "article",
    text: "文章",
  },
  business: {
    status: "business",
    text: "业务类型",
  },
  course: {
    status: "course",
    text: "课程类型",
  },
  work_task: {
    status: "work_task",
    text: "日常办公任务类型",
  },
}

export const articleTemplateMap = {
  article: {
    status: "article",
    text: "文章",
  },
  consult_knowledge: {
    status: "consult_knowledge",
    text: "咨询知识",
  },
}

export const menuTemplateMap = {
  category: {
    status: "article-category",
    text: "文章类目",
  },
  page: {
    status: "page",
    text: "页面",
  },
  fixed: {
    status: "fixed",
    text: "固定菜单",
  },
  diy: {
    status: "custom",
    text: "自定义菜单",
  },
}

export const userSexMap = {
  male: {
    status: "male",
    text: "男",
    color: "#409EFF",
  },
  female: {
    status: "female",
    text: "女",
    color: "#F56C6C",
  },
  unknown: {
    status: "unknown",
    text: "未知",
    color: "#909399",
  },
}

export const userStatusMap = {
  normal: {
    status: "normal",
    text: "正常",
    color: "#67C23A",
  },
  disabled: {
    status: "disabled",
    text: "禁用",
    color: "#F56C6C",
  },
}

export const chapterTemplateMap = {
  chapter: {
    status: "chapter",
    text: "章",
    activeIcon: "/static/image/chapter-template-chapter-active.svg",
    inactiveIcon: "/static/image/chapter-template-chapter-inactive.svg",
  },
  video: {
    status: "video",
    text: "视频",
    activeIcon: "/static/image/chapter-template-chapter-active.svg",
    inactiveIcon: "/static/image/chapter-template-chapter-inactive.svg",
  },
  voice: {
    status: "voice",
    text: "音频",
    activeIcon: "/static/image/chapter-template-chapter-active.svg",
    inactiveIcon: "/static/image/chapter-template-chapter-inactive.svg",
  },
  practice: {
    status: "practice",
    text: "练习",
    activeIcon: "/static/image/chapter-template-practice-active.svg",
    inactiveIcon: "/static/image/chapter-template-practice-inactive.svg",
  },
  assessment: {
    status: "assessment",
    text: "小结考核",
    activeIcon: "/static/image/chapter-template-assessment-active.svg",
    inactiveIcon: "/static/image/chapter-template-assessment-inactive.svg",
  },
}

export const fileExtensionMap = {
  pdf: {
    status: "pdf",
    text: "PDF",
    icon: "/static/image/file-pdf.svg",
    type: "document",
  },
  doc: {
    status: "doc",
    text: "DOC",
    icon: "/static/image/file-doc.svg",
    type: "document",
  },
  docx: {
    status: "docx",
    text: "DOCX",
    icon: "/static/image/file-doc.svg",
    type: "document",
  },
  xls: {
    status: "xls",
    text: "XLS",
    icon: "/static/image/file-excel.svg",
    type: "document",
  },
  xlsx: {
    status: "xlsx",
    text: "XLSX",
    icon: "/static/image/file-excel.svg",
    type: "document",
  },
  ppt: {
    status: "ppt",
    text: "PPT",
    icon: "/static/image/file-pdf.svg",
    type: "document",
  },
  pptx: {
    status: "pptx",
    text: "PPTX",
    icon: "/static/image/file-pdf.svg",
    type: "document",
  },
  txt: {
    status: "txt",
    text: "TXT",
  },
  jpg: {
    status: "jpg",
    text: "JPG",
    icon: "/static/image/file-image.svg",
    type: "image",
  },
  jpeg: {
    status: "jpeg",
    text: "JPEG",
    icon: "/static/image/file-image.svg",
    type: "image",
  },
  png: {
    status: "png",
    text: "PNG",
    icon: "/static/image/file-image.svg",
    type: "image",
  },
  gif: {
    status: "gif",
    text: "GIF",
    icon: "/static/image/file-image.svg",
    type: "image",
  },
  mp4: {
    status: "mp4",
    text: "MP4",
    icon: "/static/image/file-video.svg",
    type: "video",
  },
  mp3: {
    status: "mp3",
    text: "MP3",
    type: "audio",
  },
}

export const fileTypeMap = {
  image: {
    extensions: [
      fileExtensionMap.jpg.status,
      fileExtensionMap.jpeg.status,
      fileExtensionMap.png.status,
      fileExtensionMap.gif.status,
    ],
    text: "图片",
  },
  video: {
    extensions: [fileExtensionMap.mp4.status],
    text: "视频",
  },
  audio: {
    extensions: [fileExtensionMap.mp3.status],
    text: "音频",
  },
  document: {
    extensions: [
      fileExtensionMap.pdf.status,
      fileExtensionMap.doc.status,
      fileExtensionMap.docx.status,
      fileExtensionMap.xls.status,
      fileExtensionMap.xlsx.status,
      fileExtensionMap.ppt.status,
      fileExtensionMap.pptx.status,
      fileExtensionMap.txt.status,
    ],
    text: "文档",
  },
}

export const questionTypeMap = {
  single: {
    status: "single",
    text: "单选题",
    color: "#409EFF",
  },
  multiple: {
    status: "multiple",
    text: "多选题",
    color: "#E6A23C",
  },
  judge: {
    status: "judge",
    text: "判断题",
    color: "#F56C6C",
  },
}

export const paperTypeMap = {
  exam: {
    status: "exam",
    text: "考试",
  },
  practice: {
    status: "practice",
    text: "练习",
  },
  assessment: {
    status: "assessment",
    text: "小结考核",
  },
}

export const regionLevelMap = {
  city: {
    status: "city",
    text: "市",
  },
  district: {
    status: "district",
    text: "区",
  },
  street: {
    status: "street",
    text: "街道/镇",
  },
  village: {
    status: "village",
    text: "村/社区",
  },
  school: {
    status: "school",
    text: "学校",
  },
  department: {
    status: "department",
    text: "部门",
  },
}

export const taskTypeMap = {
  import_user_excel: {
    status: "import_user_excel",
    text: "导入用户",
    url: false,
  },
  export_user_excel: {
    status: "export_user_excel",
    text: "导出用户",
    url: true,
  },
  generate_invitation_code: {
    status: "generate_invitation_code",
    text: "生成邀请码",
    url: false,
  },
  export_invitation_code_excel: {
    status: "export_invitation_code_excel",
    text: "导出邀请码",
    url: true,
  },
  import_out_notice_task_receive_excel: {
    status: "import_out_notice_task_receive_excel",
    text: "导入外呼任务接收人",
    url: false,
  },
  export_out_notice_task_receive_excel: {
    status: "export_out_notice_task_receive_excel",
    text: "导出外呼任务接收人",
    url: true,
  },
  import_teen_soldier_excel: {
    status: "import_teen_soldier_excel",
    text: "导入应征青年",
    url: false,
  },
  export_teen_soldier_excel: {
    status: "export_teen_soldier_excel",
    text: "导出应征青年",
    url: true,
  },
  import_enlist_soldier_excel: {
    status: "import_enlist_soldier_excel",
    text: "导入在伍士兵",
    url: false,
  },
  export_enlist_soldier_excel: {
    status: "export_enlist_soldier_excel",
    text: "导出在伍士兵",
    url: true,
  },
  import_ex_soldier_excel: {
    status: "import_ex_soldier_excel",
    text: "导入退役士兵",
    url: false,
  },
  export_ex_soldier_excel: {
    status: "export_ex_soldier_excel",
    text: "导出退役士兵",
    url: true,
  },
  import_recall_task_soldier_excel: {
    status: "import_recall_task_soldier_excel",
    text: "导入特殊时期征招任务人员",
    url: false,
  },
  export_recall_task_soldier_excel: {
    status: "export_recall_task_soldier_excel",
    text: "导出特殊时期征招任务人员",
    url: true,
  },
  import_question_excel: {
    status: "import_question_excel",
    text: "导入题目",
    url: false,
  },
  export_question_excel: {
    status: "export_question_excel",
    text: "导出题目",
    url: true,
  },
  import_knowledge_excel: {
    status: "import_knowledge_excel",
    text: "导入知识点",
    url: false,
  },
  export_knowledge_excel: {
    status: "export_knowledge_excel",
    text: "导出知识点",
    url: true,
  },
  import_consult_knowledge_excel: {
    status: "import_consult_knowledge_excel",
    text: "导入咨询问答",
    url: false,
  },
  export_consult_knowledge_excel: {
    status: "export_consult_knowledge_excel",
    text: "导出咨询问答",
    url: true,
  },
}

export const taskStatusMap = {
  pending: {
    status: "pending",
    text: "待执行",
    color: "#eeeeee",
  },
  running: {
    status: "running",
    text: "执行中",
    color: "#E6A23C",
  },
  failed: {
    status: "failed",
    text: "执行失败",
    color: "#F56C6C",
  },
  success: {
    status: "success",
    text: "执行成功",
    color: "#67C23A",
  },
}

export const feedbackTypeMap = {
  instant: {
    status: "instant",
    text: "即时反馈",
  },
  final: {
    status: "final",
    text: "做完查看",
  },
}

export const consultFileUploadStatusMap = {
  CREATED: {
    status: "CREATED",
    text: "待处理",
    color: "#cccccc",
  },
  PARSING: {
    status: "PARSING",
    text: "解析中",
    color: "#409EFF",
  },
  PARSE_SUCCESS: {
    status: "PARSE_SUCCESS",
    text: "解析成功",
    color: "#67C23A",
  },
  PARSE_FAIL: {
    status: "PARSE_FAIL",
    text: "解析失败",
    color: "#F56C6C",
  },
  FAIL: {
    status: "FAIL",
    text: "上传失败",
    color: "#F56C6C",
  },
}

export const consultFileIndexStatusMap = {
  CREATED: {
    status: "CREATED",
    text: "待处理",
    color: "#cccccc",
  },
  RUNNING: {
    status: "RUNNING",
    text: "索引中",
    color: "#409EFF",
  },
  FINISH: {
    status: "FINISH",
    text: "索引完成",
    color: "#67C23A",
  },
  FAIL: {
    status: "FAIL",
    text: "索引失败",
    color: "#F56C6C",
  },
}

export const reportStatusMap = {
  created: {
    status: "created",
    text: "待处理",
    color: "#E6A23C",
  },
  replied: {
    status: "replied",
    text: "已回复",
    color: "#67C23A",
  },
}

export const surveyUserTypeMap = {
  platform: {
    status: "platform",
    text: "平台用户",
  },
  anybody: {
    status: "anybody",
    text: "任意用户",
  },
}

export const surveyStatusMap = {
  pending: {
    status: "pending",
    text: "未开始",
    color: "#cccccc",
  },
  running: {
    status: "running",
    text: "进行中",
    color: "#67C23A",
  },
  finished: {
    status: "finished",
    text: "已结束",
    color: "#F56C6C",
  },
}

export const smsTemplateStatusMap = {
  0: {
    status: 0,
    text: "审核中",
    color: "#E6A23C",
  },
  1: {
    status: 1,
    text: "已通过",
    color: "#67C23A",
  },
  2: {
    status: 2,
    text: "未通过",
    color: "#F56C6C",
  },
  10: {
    status: 10,
    text: "已取消",
    color: "#cccccc",
  },
}

export const outNoticeTaskTypeMap = {
  sms: {
    status: "sms",
    text: "短信",
  },
  vms: {
    status: "vms",
    text: "语音通知",
  },
  sms_vms: {
    status: "sms_vms",
    text: "短信+语音通知",
  },
  robot: {
    status: "robot",
    text: "机器人对话",
  },
}

export const outNoticeTaskStatusMap = {
  created: {
    status: "created",
    text: "待执行",
    color: "#E6A23C",
  },
  running: {
    status: "running",
    text: "执行中",
    color: "#409EFF",
  },
  stopped: {
    status: "stopped",
    text: "已停止",
    color: "#F56C6C",
  },
  finished: {
    status: "finished",
    text: "已完成",
    color: "#67C23A",
  },
}

export const outNoticeTaskReceiveStatusMap = {
  created: {
    status: "created",
    text: "待执行",
    color: "#E6A23C",
  },
  running: {
    status: "running",
    text: "执行中",
    color: "#409EFF",
  },
  error: {
    status: "error",
    text: "执行失败",
    color: "#F56C6C",
  },
  finished: {
    status: "finished",
    text: "已完成",
    color: "#67C23A",
  },
}

export const outNoticeTaskReceiveTypeMap = {
  sms: {
    status: "sms",
    text: "短信",
  },
  vms: {
    status: "vms",
    text: "语音通知",
  },
  robot: {
    status: "robot",
    text: "机器人对话",
  },
}

export const smsRuleMap = {
  phone_number2: {
    status: "phone_number2",
    text: "电话号码",
  },
  time: {
    status: "time",
    text: "时间",
  },
  money: {
    status: "money",
    text: "金额",
  },
  user_nick: {
    status: "user_nick",
    text: "用户昵称",
  },
  name: {
    status: "name",
    text: "姓名",
  },
  unit_name: {
    status: "unit_name",
    text: "单位名称",
  },
  address: {
    status: "address",
    text: "地址",
  },
  license_plate_number: {
    status: "license_plate_number",
    text: "车牌号",
  },
  tracking_number: {
    status: "tracking_number",
    text: "运单号",
  },
  pick_up_code: {
    status: "pick_up_code",
    text: "取件码",
  },
  other_number2: {
    status: "other_number2",
    text: "其他号码",
  },
  link_param: {
    status: "link_param",
    text: "链接参数",
  },
  email_address: {
    status: "email_address",
    text: "邮箱地址",
  },
  others: {
    status: "others",
    text: "其他",
  },
}

export const articleStatusMap = {
  published: {
    status: "published",
    text: "已发布",
    color: "#67C23A",
  },
  draft: {
    status: "draft",
    text: "草稿",
    color: "#cccccc",
  },
}

export const workTaskStatusMap = {
  published: {
    status: "published",
    text: "已发布",
    color: "#67C23A",
  },
  draft: {
    status: "draft",
    text: "草稿",
    color: "#cccccc",
  },
}

export const systemNotificationTypeMap = {
  report_reply: {
    status: "report_reply",
    text: "举报回复",
    color: "#409EFF",
  },
  report_new: {
    status: "report_new",
    text: "新举报",
    color: "#409EFF",
  },
}

export const inputStyle = {
  maxWidth: "630rpx",
  height: "100rpx",
  background: "#F7F8FA",
  borderRadius: "20rpx 20rpx 20rpx 20rpx",
  border: "1rpx solid #E1E1E1",
  padding: "0 41rpx",
  boxSizing: "border-box",
  margin: "0 auto",
}

export const fixedMenuTemplateMap = {
  work: {
    title: "日常办公",
    url: "/pages/office/office",
  },
  match: {
    title: "精准匹配",
    url: "/pages/matching/matching",
  },
  special_period: {
    title: "特殊时期征招",
    url: "/pages/conscript/list",
  },
  report: {
    title: "监督举报",
    url: "/pages/report/report",
  },
  exam: {
    title: "考核认证",
    url: "/pages/exam/examList",
  },
  error_question: {
    title: "错题集",
    url: "/pages/errorNotebook/errorNotebook",
  },
  course: {
    title: "课程学习",
    url: "/pages/course/course",
  },
}
