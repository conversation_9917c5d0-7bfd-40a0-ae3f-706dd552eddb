import { ref, onMounted, onUnmounted } from "vue"

export const usePagination = (config) => {
  const page = ref(config.page || 1)
  const pageSize = ref(config.pageSize || 15)
  const loadMoreStatus = ref("loadmore") // loadMore, loading, noMore

  const load = async () => {
    if (loadMoreStatus.value !== "loadmore") return
    loadMoreStatus.value = "loading"
    const res = await config.loadData({
      page: page.value,
      page_size: pageSize.value,
    })
    uni.stopPullDownRefresh()
    if (res.result?.next_page_url) {
      loadMoreStatus.value = "loadmore"
      page.value++
    } else {
      loadMoreStatus.value = "nomore"
    }
  }

  const refresh = () => {
    page.value = 1
    loadMoreStatus.value = "loadmore"
    if (config.list?.value) {
      config.list.value = []
    }
    load()
  }

  return {
    page,
    pageSize,
    loadMoreStatus,
    refresh,
    loadPage: load,
  }
}

export const useNavbar = function () {
  const opacity = ref(0)
  const { statusBarHeight } = uni.getSystemInfoSync()
  const navBarHeight = statusBarHeight + 44

  onPageScroll((e) => {
    const scrollTop = e.scrollTop
    let opacityVal = scrollTop / navBarHeight
    console.log("opacityVal", opacityVal)
    opacity.value = opacityVal > 1 ? 1 : opacityVal.toFixed(2)
  })

  return {
    height: navBarHeight,
    statusBarHeight,
    opacity,
  }
}
