import { useCommonStore } from "../store/common"
import { useUserStore } from "../store/user"
import { navTo } from "/utils/utils"

const header = {
  Accept: "application/json",
}

let reqCount = 0

const request = (config) => {
  const store = useCommonStore()
  const userStore = useUserStore()

  reqCount++
  uni.showLoading({
    title: "加载中",
  })

  const requestConfig = {
    ...config,
    url: `${store.baseUrl}/api${config.url}`,
  }

  return new Promise((resolve, reject) => {
    uni
      .request({
        ...requestConfig,
        header: {
          ...header,
          Authorization: userStore.token ? `Bearer ${userStore.token}` : "",
        },
      })
      .then((res) => {
        console.log(res.data)
        reqCount--
        if (reqCount === 0) {
          uni.hideLoading()
        }
        resolve(res.data)

        if (res.statusCode === 401) {
          userStore.logout()
          const pages = getCurrentPages()
          const currentPage = pages[pages.length - 1]
          if (currentPage.route !== "pages/login/login") {
            navTo("/pages/login/login")
          }
        } else if ([10001, 10002].includes(res.data.code)) {
          uni.showModal({
            title: "温馨提示",
            content: res.data.error || res.data.message,
            showCancel: false,
            success: function () {
              uni.reLaunch({
                url: "/pages/index/index",
              })
            },
          })
        } else if (res.data.code !== 200) {
          uni.showModal({
            title: "温馨提示",
            content: res.data.error || res.data.message,
            showCancel: false,
          })
        }
      })
      .catch((err) => {
        if (reqCount === 0) {
          uni.hideLoading()
        }
        uni.showModal({
          title: "温馨提示",
          content: err.errMsg || JSON.stringify(err),
          showCancel: false,
        })
        reject(err)
      })
  })
}

export const fetchUpload = function (
  filePath,
  extra = {},
  originName,
  uploadUrl = "/mini/upload_file"
) {
  const store = useCommonStore()
  const userStore = useUserStore()

  const url = `${store.baseUrl}/api${uploadUrl}`
  const token = userStore.token || ""
  uni.showLoading({
    title: "上传中...",
  })
  const formData = {
    extra: JSON.stringify(extra),
  }

  if (originName) {
    formData.original_name = originName
  }

  console.log("formData", formData)

  return new Promise((resolve, reject) => {
    uni
      .uploadFile({
        url: url,
        name: "file",
        filePath: filePath,
        formData,
        header: {
          Accept: "application/json",
          Authorization: token ? "Bearer " + token : "",
        },
      })
      .then((res) => {
        uni.hideLoading()
        console.log("res", res)
        resolve(JSON.parse(res.data))
      })
      .catch((err) => {
        uni.hideLoading()
        uni.showModal({
          title: "出错了",
          content: err.errMsg || JSON.stringify(err),
        })
        console.error(err)
        reject(err)
      })
  })
}

export default request
