import { useUserStore } from "@/store/user"

export function rpx2px(rpx) {
  return (rpx / 750) * uni.getSystemInfoSync().windowWidth
}

export function getAgeFromIdCard(idCard) {
  if (!idCard) {
    return 0
  }
  const birth = idCard.substring(6, 14)
  const year = birth.substring(0, 4)
  const month = birth.substring(4, 6)
  const day = birth.substring(6, 8)
  const date = new Date()
  const nowYear = date.getFullYear()
  const nowMonth = date.getMonth() + 1
  const nowDay = date.getDate()
  let age = nowYear - year
  if (nowMonth < month || (nowMonth === month && nowDay < day)) {
    age--
  }
  return age
}

//身份证号脱敏
export function idCardDesensitization(idCard) {
  return idCard.replace(/^(.{3})(?:\d+)(.{4})$/, "$1***********$2")
}

// 跳转页面
export function navTo(url, query, method = "navigateTo") {
  const userStore = useUserStore()

  if (query) {
    const queryString = Object.keys(query)
      .map((key) => `${key}=${query[key]}`)
      .join("&")
    url = `${url}?${queryString}`
  }
  const tabBarUrls = [
    "/pages/index/index",
    "/pages/todoTask/todoTask",
    "/pages/data/data",
    "/pages/my/my",
  ]
  if (tabBarUrls.includes(url)) {
    method = "switchTab"
    userStore.getRemindCount()
  }
  uni[method]({
    url,
    fail: (e) => {
      console.error(e)
    },
  })
}

// 阿拉伯数字转中文数字
export function numberToChinese(num) {
  const chnNumChar = [
    "零",
    "一",
    "二",
    "三",
    "四",
    "五",
    "六",
    "七",
    "八",
    "九",
  ]
  const chnUnitSection = ["", "万", "亿", "万亿", "亿亿"]
  const chnUnitChar = ["", "十", "百", "千"]
  let unitPos = 0
  let strIns = ""
  let chnStr = ""
  let needZero = false

  if (num === 0) {
    return chnNumChar[0]
  }

  function sectionToChinese(section) {
    let str = ""
    let unitPos = 0
    let zero = true
    while (section > 0) {
      const v = section % 10
      if (v === 0) {
        if (!zero) {
          zero = true
          str = chnNumChar[v] + str
        }
      } else {
        zero = false
        str = chnNumChar[v] + chnUnitChar[unitPos] + str
      }
      unitPos++
      section = Math.floor(section / 10)
    }
    return str
  }

  while (num > 0) {
    const section = num % 10000
    if (needZero) {
      chnStr = chnNumChar[0] + chnStr
    }
    strIns = sectionToChinese(section)
    strIns += section !== 0 ? chnUnitSection[unitPos] : chnUnitSection[0]
    chnStr = strIns + chnStr
    needZero = section < 1000 && section > 0
    num = Math.floor(num / 10000)
    unitPos++
  }
  return chnStr
}

// 权限判断  或
export function checkPermissionOr(slugs) {
  const userStore = useUserStore()
  const permissions = userStore.permissions
  for (let i = 0; i < slugs.length; i++) {
    if (permissions.includes(slugs[i])) {
      return true
    }
  }
  return false
}

// 权限判断  与
export function checkPermissionAnd(slugs) {
  const userStore = useUserStore()
  const permissions = userStore.permissions
  for (let i = 0; i < slugs.length; i++) {
    if (!permissions.includes(slugs[i])) {
      return false
    }
  }
  return true
}
